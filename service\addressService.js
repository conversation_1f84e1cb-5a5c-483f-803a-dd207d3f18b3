import Address from "../model/address.model.js";
import AppError from "../utils/appError.js";

export async function createAddressInDB(
  address,
  distributor,
  shopifyAddressId
) {
  try {
    if (!address || Object.keys(address).length === 0) {
      return new AppError("Address is mandatory to create in DB", 400);
    }

    const addressPayload = {
      shopifyCustomerId: distributor?.shopifyCustomerId,
      shopifyAddressId: shopifyAddressId,
      distributor: distributor?._id,
      first_name: address?.first_name,
      last_name: address?.last_name,
      company: address?.company,
      address1: address?.address1,
      address2: address?.address2,
      city: address?.city,
      province: address?.province,
      country: address?.country,
      zip: address?.zip,
      province_code: address?.province_code,
      country_code: address?.country_code,
      country_name: address?.country_name,
    };

    const createdAddress = await Address.create(addressPayload);
    return createdAddress;
  } catch (error) {
    console.error("Error in createAddressInDB: ", error);
    return new AppError("Failed to create address in database.", 500);
  }
}
