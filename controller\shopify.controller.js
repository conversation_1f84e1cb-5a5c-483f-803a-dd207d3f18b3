import axios from "axios";
import Status from "../model/status.model.js";
import AppError from "../utils/appError.js";
import multer from "multer";
import NewAppError from "../utils/newAppError.js";
import {
  CONFIRM_DRAFT_ORDER,
  CREATE_DRAFT_ORDER,
  CREATE_PRODUCT_WITH_VARIANTS,
  CREATE_VARIANT,
  DELETE_DRAFT_ORDER,
  GET_CATALOGS_FROM_COMPANY,
  GET_SHOPIFY_LOCATIONS,
  GET_PRODUCT_DETAILS,
  GET_VARIANT_PRICE,
  GET_VARIANT_PRICE_FROM_CATALOG,
  INVENTORY_ACTIVATE,
  INVENTORY_SET_QUANTITIES,
  UPDATE_PRODUCT_VARIANTS,
  UPDATE_VARIANT,
  VARIANT_BY_SKU,
  GET_PUBLICATIONS,
  PUBLISH_PRODUCT,
  GET_ORDERS_FULFILLMENT,
  FULFILLMENT_CREATE,
  GET_PRODUCTS,
  UPDATE_PRODUCT_TAGS,
  SMART_GET_PRODUCT_METAFIELD_DEFINITIONS,
  SMART_GET_PRODUCTS_WITH_METAFIELDS,
  SMART_GET_ALL_COLLECTIONS,
  SMART_CREATE_COLLECTION,
  UPDATE_PRODUCT_METAFIELD,
} from "../queries/internalQueries.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";

const upload = multer({ dest: "uploads/" });

export const createOrder = async (req, res, next) => {
  try {
    const initialOrderStatusExist = await Status.findOne({
      isInitialStatus: true,
      statusType: "Order",
    });
    const initialShipmentStatusExist = await Status.findOne({
      isInitialStatus: true,
      statusType: "Shipment",
    });
    if (!initialShipmentStatusExist || !initialOrderStatusExist)
      return next(
        new AppError(
          "No Initial Status Available in Admin Dashboard for Order Or Shipment"
        )
      );
    const is_line_items =
      req.body.order.line_items && req.body.order.line_items.length > 0;
    const is_transactions =
      req.body.order.transactions && req.body.order.transactions.length > 0;
    const is_total_tax = req.body.order.total_tax;
    const is_currency = req.body.order.currency;
    if (!is_line_items || !is_transactions || !is_total_tax || !is_currency)
      return next(
        new AppError(
          "One of the following required field is missing line_items[] or transactions[] or total_tax or currency"
        )
      );
    const data = JSON.stringify(req.body);
    const config = {
      method: "post",
      url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/orders.json`,
      headers: {
        "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
      data: data,
    };

    const productCreateResponse = await axios(config);
    res.status(200).json({
      status: "success",
      data: productCreateResponse.data,
    });
  } catch (error) {
    console.log(error);
    res.status(400).json({
      status: "fail",
      error: error.response.data.errors,
      errorStack: JSON.stringify(error),
    });
  }
};

export const getCompanyLocations = async (companyId) => {
  const data = JSON.stringify({
    query: `{company(id: "${companyId}") {
    locations(first: 2) {
      nodes {
        billingAddress {
          address1
          address2
          city
          companyName
          country
          countryCode
          firstName
          phone
          province
          zip
          zoneCode
        }
         shippingAddress {
          address1
          address2
          city
          companyName
          country
          countryCode
          firstName
          phone
          province
          zip
          zoneCode
        }
        orderCount
        note
      }
    }
  }}`,
    variables: {},
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
    headers: {
      "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      "Content-Type": "application/json",
    },
    data: data,
  };

  return axios(config)
    .then(function (response) {
      const companyLocations =
        response.data.data.company?.locations?.nodes || {};
      return companyLocations;
    })
    .catch(function (error) {
      console.log(error);
    });
};

export const getCustomerInfo = async (customerId) => {
  const data = JSON.stringify({
    query: `{
      customer(id: "${customerId}") {
        displayName
        email
        phone
        companyContactProfiles {
          isMainContact
          company {
            id
            name
          }
        }
      }
    }`,
    variables: {},
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/2024-07/graphql.json`,
    headers: {
      "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      "Content-Type": "application/json",
    },
    data: data,
  };
  return axios(config)
    .then(function (response) {
      const customer = response.data.data.customer;
      const companyContactProfiles =
        response.data.data.customer?.companyContactProfiles;
      const companyInfo = companyContactProfiles
        ? companyContactProfiles[0]?.company
        : {};
      return {
        customer: {
          shopifyCustomerId: customerId.split("/")?.pop(),
          name: customer?.displayName,
          email: customer?.email,
          phone: customer?.email,
          isMainContact: companyContactProfiles
            ? companyContactProfiles[0]?.isMainContact
            : "",
        },
        companyInfo: companyInfo,
      };
    })
    .catch(function (error) {
      console.log(error);
    });
};

export const getCompanyList = async (req, res, next) => {
  const data = JSON.stringify({
    query: `{
      companies(first:100) {
          nodes {
              id
              name
          }
      }
  }`,
    variables: {},
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
    headers: {
      "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config)
    .then(function (response) {
      const trasformedData = response.data.data.companies?.nodes?.map(
        (company) => {
          return {
            id: company.id.split("/").pop(),
            name: company.name,
          };
        }
      );
      res.status(200).send({
        status: "success",
        data: trasformedData,
      });
    })
    .catch(function (error) {
      console.log(error);
    });
};

export async function createDraftOrder(orderData) {
  const variables = {
    input: {
      lineItems: orderData.input.lineItems.map((item) => ({
        variantId: item.variantId,
        quantity: item.quantity,
        priceOverride: {
          amount: `${item.originalUnitPrice.amount}`,
          currencyCode: `${item.originalUnitPrice.currencyCode}`,
        },
      })),
      customerId: orderData.input.customer.id,
    },
  };
  const data = await fetchGraphqlDataShopify(
    CREATE_DRAFT_ORDER,
    variables,
    "2025-01"
  );

  const { draftOrder, userErrors } = data.data.draftOrderCreate;

  if (userErrors?.length > 0) {
    throw new NewAppError(
      "DRAFT_ORDER_ERROR",
      userErrors.map((e) => e.message).join(", "),
      400
    );
  }
  return draftOrder;
}

export async function deleteDraftOrder(draftOrderId) {
  const globalId = draftOrderId.startsWith("gid://")
    ? draftOrderId
    : `gid://shopify/DraftOrder/${draftOrderId}`;

  const variables = {
    input: {
      id: globalId,
    },
  };

  const data = await fetchGraphqlDataShopify(DELETE_DRAFT_ORDER, variables);
  const { deletedId, userErrors } = data.data.draftOrderDelete;

  if (userErrors.length > 0) {
    throw new NewAppError(
      "DELETE_ORDER_ERROR",
      userErrors.map((e) => e.message).join(", "),
      400
    );
  }
  return deletedId;
}

export async function confirmDraftOrder(draftOrderId) {
  const variables = {
    id: `gid://shopify/DraftOrder/${draftOrderId}`,
  };

  const data = await fetchGraphqlDataShopify(CONFIRM_DRAFT_ORDER, variables);
  const { draftOrder, userErrors } = data.data.draftOrderComplete;

  if (userErrors.length > 0) {
    throw new NewAppError(
      "CONFIRM_ORDER_ERROR",
      userErrors.map((e) => e.message).join(", "),
      400
    );
  }
  return draftOrder;
}

export async function getVariantIdBySKU(sku) {
  const variables = {
    sku: sku,
  };

  const data = await fetchGraphqlDataShopify(VARIANT_BY_SKU, variables);
  const edges = data.data.productVariants.edges;

  if (!edges.length) {
    return null;
  }

  return edges[0].node;
}

export async function getCompanyCatalogs(companyId) {
  const globalId = companyId.startsWith("gid://")
    ? companyId
    : `gid://shopify/Company/${companyId}`;

  const variables = {
    companyId: globalId,
  };

  const data = await fetchGraphqlDataShopify(
    GET_CATALOGS_FROM_COMPANY,
    variables
  );
  const locations = data.data.company?.locations?.edges || [];

  const catalogs = locations.reduce((acc, location) => {
    const locationCatalogs = location.node?.catalogs?.edges || [];
    return [
      ...acc,
      ...locationCatalogs.map((catalog) => ({
        id: catalog.node.id,
      })),
    ];
  }, []);

  return catalogs;
}

export const getVariantPriceFromCatalog = async (catalogId, variantId) => {
  const response = await fetchGraphqlDataShopify(
    GET_VARIANT_PRICE_FROM_CATALOG,
    {
      catalogId,
      variantQuery: `variant_id:${variantId}`,
    }
  );
  const prices = response?.data.catalog?.priceList?.prices?.edges || [];
  if (prices.length === 0) {
    return null;
  }

  const priceData = prices[0]?.node?.price;
  if (!priceData) {
    return null;
  }
  return priceData;
};

export const getVariantPrice = async (variantId) => {
  try {
    const globalId = variantId.startsWith("gid://")
      ? variantId
      : `gid://shopify/ProductVariant/${variantId}`;

    const variable = {
      variantId: globalId,
    };

    const response = await fetchGraphqlDataShopify(GET_VARIANT_PRICE, variable);

    const variant = response?.data?.productVariant;
    if (!variant) {
      return null;
    }

    return {
      price: variant.price,
      compareAtPrice: variant.compareAtPrice,
    };
  } catch (error) {
    console.log(error?.message, `Error when fetching variant price`);
    throw new NewAppError("FETCHING_VARIANT_PRICE_ERROR", error?.message, 400);
  }
};

export const getExistingLocationsShopify = async () => {
  try {
    const response = await fetchGraphqlDataShopify(GET_SHOPIFY_LOCATIONS, {});
    if (response.error) {
      throw new NewAppError(
        `Error when fetching existing locations.Error:${response.error.response.data.errors}`
      );
    }

    const responseData =
      response.data.locations.edges.map((location) => {
        return { id: location?.node?.id, name: location?.node?.name };
      }) || [];

    //responseData =[{id:"75220123748",name:"1SR"}]
    return responseData;
  } catch (error) {
    console.log(error?.message, `Error when fetching existing locations`);
    throw new NewAppError("FETCHING_LOCATIONS_ERROR", error?.message, 400);
  }
};

export const setInventoryLevelOnHandGraphQL = async (
  inventoryItemId,
  locationId,
  onHandQuantity
) => {
  try {
    const gidInventoryItemId = `gid://shopify/InventoryItem/${inventoryItemId}`;
    const gidLocationId = `gid://shopify/Location/${locationId}`;

    const variables = {
      input: {
        name: "on_hand",
        reason: "correction",
        ignoreCompareQuantity: true,
        quantities: [
          {
            inventoryItemId: gidInventoryItemId,
            locationId: gidLocationId,
            quantity: onHandQuantity,
          },
        ],
      },
    };

    const response = await fetchGraphqlDataShopify(
      INVENTORY_SET_QUANTITIES,
      variables
    );

    const result = response?.data?.inventorySetQuantities;
    const userErrors = result?.userErrors || [];

    if (userErrors.length > 0) {
      throw new NewAppError(
        "INVENTORY_SET_QUANTITIES_ERROR",
        userErrors.map((e) => e.message).join(", "),
        400
      );
    }
    return true;
  } catch (error) {
    const errorMessage =
      error?.message || "Unknown error occurred while setting inventory level.";
    console.error("Error setting inventory on hold level:", errorMessage);
    throw new NewAppError("INVENTORY_SET_QUANTITIES_ERROR", errorMessage, 400);
  }
};

export const setInventoryLevel = async (
  inventoryItemId,
  locationId,
  availableQuantity
) => {
  try {
    const gidInventoryItemId = inventoryItemId.startsWith("gid://")
      ? inventoryItemId
      : `gid://shopify/InventoryItem/${inventoryItemId}`;
    // const gidInventoryItemId = `gid://shopify/InventoryItem/${inventoryItemId}`;
    const gidLocationId = locationId.startsWith("gid://")
      ? locationId
      : `gid://shopify/Location/${locationId}`;

    const response = await fetchGraphqlDataShopify(INVENTORY_SET_QUANTITIES, {
      input: {
        name: "available",
        reason: "correction",
        ignoreCompareQuantity: true,
        quantities: [
          {
            inventoryItemId: gidInventoryItemId,
            locationId: gidLocationId,
            quantity: availableQuantity,
          },
        ],
      },
    });

    if (response.data?.inventorySetQuantities?.userErrors?.length > 0) {
      const errors = response.data.inventorySetQuantities.userErrors;
      throw new NewAppError(
        `Error setting inventory level: ${errors
          .map((e) => e.message)
          .join(", ")}`
      );
    }
    return true;
  } catch (error) {
    // console.error("Error setting inventory level:", error);
    throw new NewAppError(
      "SHOPIFY_Error",
      `Error setting inventory level: ${error?.message}`,
      400
    );
  }
};

export const activateInventoryItemAtLocation = async (
  inventoryItemId,
  locationId
) => {
  try {
    const gidInventoryItemId = inventoryItemId.startsWith("gid://")
      ? inventoryItemId
      : `gid://shopify/InventoryItem/${inventoryItemId}`;
    const gidLocationId = locationId.startsWith("gid://")
      ? locationId
      : `gid://shopify/Location/${locationId}`;

    const response = await fetchGraphqlDataShopify(INVENTORY_ACTIVATE, {
      inventoryItemId: gidInventoryItemId,
      locationId: gidLocationId,
    });

    const errors = response?.data?.inventoryActivate?.userErrors;
    if (errors?.length) {
      throw new NewAppError(
        `Inventory activation failed: ${errors
          .map((e) => e.message)
          .join(", ")}`
      );
    }
  } catch (err) {
    console.error("Error activating inventory:", err.message);
    throw new NewAppError("SHOPIFY_ERROR", err.message, 500);
  }
};

export const updateProductVariants = async (variables) => {
  try {
    const response = await fetchGraphqlDataShopify(
      UPDATE_VARIANT,
      variables,
      "2024-10"
    );
    return response;
  } catch (err) {
    console.log(err);
    throw new NewAppError("SHOPIFY_ERROR", err?.message, 400);
  }
};

export const createVariants = async (variables) => {
  try {
    const response = await fetchGraphqlDataShopify(
      CREATE_VARIANT,
      variables,
      "2024-10"
    );
    return response;
  } catch (err) {
    console.log(err);
    throw new NewAppError("SHOPIFY_ERROR", err?.message, 400);
  }
};

export const createProductWithVariants = async (variables) => {
  try {
    const response = await fetchGraphqlDataShopify(
      CREATE_PRODUCT_WITH_VARIANTS,
      variables,
      "2024-10"
    );
    // console.log(response, "updateProductsVAriants");
    return response;
  } catch (err) {
    console.log(err);
    throw new NewAppError("SHOPIFY_Error", err?.message, 400);
  }
};

export const getProductDetails = async (productId) => {
  const globalId = productId.startsWith("gid://")
    ? productId
    : `gid://shopify/Product/${productId}`;

  const variable = {
    productId: globalId,
  };

  const response = await fetchGraphqlDataShopify(GET_PRODUCT_DETAILS, variable);

  const product = response?.data?.product;
  if (!product) {
    return null;
  }

  return product;
};

export const getPublications = async () => {
  try {
    const response = await fetchGraphqlDataShopify(GET_PUBLICATIONS, {});
    return response?.data?.publications?.edges;
  } catch (error) {
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};

export const PublishProductToOnlineStore = async (variable) => {
  try {
    const response = await fetchGraphqlDataShopify(
      PUBLISH_PRODUCT,
      variable,
      "2024-10"
    );
    return response;
  } catch (error) {
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};

export const getOrderFulfillments = async (orderId, cursor) => {
  try {
    const response = await fetchGraphqlDataShopify(
      GET_ORDERS_FULFILLMENT,
      { orderId, cursor },
      "2024-10"
    );
    return response;
  } catch (error) {
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};

export const createFulfillment = async (fulfillmentInput) => {
  try {
    const response = await fetchGraphqlDataShopify(
      FULFILLMENT_CREATE,
      { fulfillment: fulfillmentInput },
      "2024-10"
    );
    return response;
  } catch (error) {
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};

export const getProductsQuery = async (variables = {}) => {
  try {
    const response = await fetchGraphqlDataShopify(
      GET_PRODUCTS,
      variables,
      "2024-04"
    );
    return response;
      } catch (error) {
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};

export const updateProductTagsMutation = async (variables = {}) => {
  try {
    const response = await fetchGraphqlDataShopify(
      UPDATE_PRODUCT_TAGS,
      variables,
      "2024-04"
    );
    return response;
  } catch (error) {
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};

// export const updateProductMetafieldMutation = async (variables = {}) => {
//   try {
//     const response = await fetchGraphqlDataShopify(
//       UPDATE_PRODUCT_METAFIELD,
//       variables,
//       "2024-04"
//        );
//     return response;
//   } catch (error) {
//     throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
//   }
// };
// Update product tags by product ID
export const updateProductTags = async (productId, tags) => {
  const variables = { input: { id: productId, tags } };
  const data = await fetchGraphqlDataShopify(UPDATE_PRODUCT_TAGS, variables);
  return data;
};

// Fetch one page of product metafield definitions (smart collection)
export const smartGetProductMetafieldDefinitions = async (cursor = null) => {
  const variables = { cursor };
  const data = await fetchGraphqlDataShopify(
    SMART_GET_PRODUCT_METAFIELD_DEFINITIONS,
    variables
  );
  return data?.data?.metafieldDefinitions;
};

// Fetch one page of products with metafields (smart collection)
export const smartGetProductsWithMetafields = async (cursor = null) => {
  const variables = { cursor };
  const data = await fetchGraphqlDataShopify(
    SMART_GET_PRODUCTS_WITH_METAFIELDS,
    variables
  );
  return data?.data?.products;
};

// Fetch one page of collections (smart collection)
export const smartGetCollections = async (cursor = null) => {
  const variables = { cursor };
  const data = await fetchGraphqlDataShopify(
    SMART_GET_ALL_COLLECTIONS,
    variables
  );
  return data?.data?.collections;
};

// Create a collection with rules (smart collection)
export const smartCreateCollection = async (input) => {
  const variables = { input };
  const data = await fetchGraphqlDataShopify(
    SMART_CREATE_COLLECTION,
    variables
  );
  return data?.data?.collectionCreate;
}

export const updateProductMetafieldMutation = async (variables) => {
  try {

    const response = await fetchGraphqlDataShopify(
      UPDATE_PRODUCT_METAFIELD,
      variables,
      "2024-10"
    );
    return response;
  } catch (error) {
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};


