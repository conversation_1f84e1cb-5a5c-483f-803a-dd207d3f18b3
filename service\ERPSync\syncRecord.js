import SyncRecord from "../../model/syncRecordModel.js";

const getSyncRecordFromDB = async () => {
  try {
    const syncRecord = await SyncRecord.findOne();
    return syncRecord;
  } catch (error) {
    console.error("Error fetching sync record:", error.message);
    return null;
  }
};

const saveSyncRecord = async (lastSyncDate, type) => {
  try {
    const date = new Date(lastSyncDate);
    const updateData = type === "customer" ? { customerlastSyncDate: date } : { productlastSyncDate: date };

    const syncRecord = await SyncRecord.findOneAndUpdate(
      {},
      { $set: updateData },
      { new: true, upsert: true }
    );

    console.log(
      syncRecord ? `Sync record updated with ${type} sync date: ${lastSyncDate}` : `New sync record created with ${type} sync date: ${lastSyncDate}`
    );
  } catch (error) {
    console.error("Error saving sync record:", error.message);
    throw new Error("Unable to save sync record");
  }
};

export { getSyncRecordFromDB,saveSyncRecord };
