import express from "express";
import cors from "cors";
import * as dotenv from "dotenv";
import path from "path";
import cron from "node-cron";
import * as fs from "fs";
import SftpClient from "ssh2-sftp-client";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";
import ApiRouter from "./router/index.js";
import AppError from "./utils/appError.js";
import { Email } from "./service/rule/index.js";
import globalErrorHandler from "./utils/globalErrorHandler.js";
import {
  checkSLABreach,
  emailSendCron,
  generateEscalationNotification,
} from "./service/cron/email.cron.js";
import { generateInvoicePdf } from "./utils/pdfGenerator.js";
import inventorySyncShopify from "./service/shopify/inventorySyncShopify.service.js";
import { fetchAndSaveCompanies } from "./service/shopify/priceSyncShopify.service.js";
import { getPricesForCustomerAndSkus } from "./helpers/priceHelper.js";
import { getCustomerAndPriceForAllSKUs } from "./service/shopify/metafieldUpdate.service.js";
import { customerUpdateScheduledService } from "./service/ERPSync/customerService.js";
import { productScheduledService } from "./service/ERPSync/productScheduledService.js";
import {
  shippedOrderSync,
  cancelOrderSync,
  updateERPStatusesInBatches,
} from "./service/ERPSync/orderService.js";
import Distributor from "./model/distributor.model.js";
import orderCreationWebhook from "./service/webhookService.js";
import NewAppError from "./utils/newAppError.js";
import { runCronJob } from "./controller/cron.controller.js";

const __filename = new URL(import.meta.url).pathname;
const __dirname = path.dirname(__filename);

// dotenv.config();
const app = express();

app.use(cors());
app.set("view engine", "pug");
app.set("views", path.join(__dirname, "views"));
app.use("/dump", express.static(path.join(__dirname, "asset", "dump")));

app.use(express.json());
// app.use(express.json({ limit: "100kb" }));

const handleCreateInvoice = async (req, res, next) => {
  try {
    const { moreDetails, client, items, paid, debugMode } = req.body;

    const invoiceNumber = "FACT-";

    // Calculate sum per item
    items.forEach((item) => {
      item.amountSum = item.price * item.quantity;
      return item;
    });

    // getting subtotal ->
    const subtotal = items.reduce((prev, curr) => {
      return curr.amountSum + prev;
    }, 0);

    const fileName = invoiceNumber + ".pdf";
    const filePath = path.join(__dirname, `/asset/static/${fileName}`);

    const invoiceDetails = { items, invoiceNumber, paid, subtotal, client };

    generateInvoicePdf(invoiceDetails, filePath);

    const files = [filePath];

    return res.send({ success: true, data: { files } });
  } catch (err) {
    console.error(err);
    return res.status(400).send({ message: err.message });
  }
};

app.post("/generate-pdf", handleCreateInvoice);

// cron.schedule("*/5 * * * *", () => {
//   console.log("Running email cron job");
  emailSendCron();
// });
// cron.schedule("*/50 * * * *", () => {
// console.log("Running inventory sync cron job as every 50 minutes");
// inventorySyncShopify();
// });

// cron.schedule("*/50 * * * *", () => {
//   console.log("Running escalation cron job every 50 minutes...");
//   generateEscalationNotification();
// });

// cron.schedule("*/5 * * * *", () => {
//   console.log("Running SLA Breach cron job every 5 minutes...");
//   checkSLABreach();
// });

// cron.schedule("*/5 * * * *", () => {
//   console.log("Running SLA Breach cron job every 5 minutes...");
//   checkSLABreach();
// });

// cron.schedule("*/5 * * * *", () => {
//   console.log("Running customer aand price save in metafields cron");
//   getCustomerAndPriceForAllSKUs();
// });

// cron.schedule("*/50 * * * *", async () => {
//   console.log("Running catalog, customer and price sync cron job");
//   fetchAndSaveCompanies();
// });

// productScheduledService(false, false);
// ERPSync
// Product sync (2:40 AM Singapore/Malaysia time)
cron.schedule("40 18 * * *", async () => {
  productScheduledService(false, false);
});
// Cancelled ERP orders sync (2:20 AM Singapore/Malaysia time)
// cron.schedule("20 18 * * *", async () => {
//   cancelOrderSync();
// });

// Shipped ERP orders sync (2:00 AM Singapore/Malaysia time)
// cron.schedule("0 18 * * *", async () => {
//   shippedOrderSync();
// });

// Customer update sync (3:40 AM Singapore/Malaysia time)
// cron.schedule("40 19 * * *", async () => {
//   customerUpdateScheduledService();
// });

// Product sync cron (2:00 AM Singapore/Malaysia time)
cron.schedule("0 18 * * *", async () => {
  runCronJob();
});

// cron.schedule("0 21 * * *", async () => {
//   createCollectionsFromProductMetafields()
// });

cron.schedule("0 4,16 * * *", async () => {
  updateERPStatusesInBatches();
});



export const downloadFileFromSFTP = async (
  sftpConfig,
  remotePath,
  localPath
) => {
  // const sftpConfig = {
  //   host: "************",
  //   username: process.env.SFTP_USERNAME,
  //   port: "22",
  //   password: process.env.SFTP_PASSWORD,
  //   remoteDir: req.body.path,
  // };
  console.log(remotePath, "REMOTE PATH");
  console.log(localPath, "LOCAL PATH");
  const sftp = new SftpClient();
  try {
    await sftp.connect(sftpConfig);
    await sftp.get(remotePath, localPath);
    return { code: 0, message: "File downloaded" };
  } catch (error) {
    return error;
  } finally {
    await sftp.end();
  }
};

orderCreationWebhook();

app.get("/download", async (req, res) => {
  const sftpConfig = {
    host: "************",
    username: process.env.SFTP_USERNAME,
    port: "22",
    password: process.env.SFTP_PASSWORD,
    remoteDir: req.body.path,
  };
  const filePath = decodeURI(req.query.filePath) || req.query.filePath;
  const localFilePath = path.join(
    __dirname,
    "asset",
    "dump",
    `${filePath.split("/").pop()}`
  );
  console.log("localFilePath", localFilePath);
  console.log("localFilePath", localFilePath);

  const apiAuth = req.query.auth;
  if (!apiAuth || apiAuth != "2XCF_786Ja19038_5hree_8376Ram837") {
    return res.send({
      code: 786,
      error: "Missing Auth Token",
    });
  }
  await fs.promises.writeFile(localFilePath, "");
  const remotePath = `${filePath}`;
  await downloadFileFromSFTP(sftpConfig, remotePath, localFilePath);
  if (!filePath) {
    return res.status(400).send("filePath query parameter is required");
  }

  // const sftp = new SftpClient();

  try {
    // const files = fs.readdirSync(publicFolderPath);
    // const transformedFileDetails = files.map((file) => {
    //   return {
    //     fileName: file,
    //     downloadUrl: `${process.env.APP_URL}/dump/${file}`
    //   };
    // });
    res.status(200).send({
      data: `${process.env.APP_URL}/dump/${filePath.split("/").pop()}`,
    });
    // await sftp.connect(sftpConfig);
    // sftp.get(filePath)
    //   .then((stream) => {
    //     res.setHeader('Content-Disposition', `attachment; filename="${filePath.split('/').pop()}"`);
    //     res.setHeader('Content-Type', 'application/octet-stream');

    //     stream.pipe(res);
    //   })
    //   .catch(err => {
    //     console.error('Error fetching file from SFTP:', err);
    //     res.status(500).send('Error fetching file from SFTP');
    //   });
  } catch (err) {
    console.error("SFTP connection error:", err);
    res.status(500).send("SFTP connection error");
  }
});

app.post("/sftp", async (req, res, next) => {
  console.log("PAYLOAD", req.body.path);
  console.log(
    "process.env.SFTP_USERNAME_PRODUCTION",
    process.env.SFTP_USERNAME_PRODUCTION
  );
  console.log(
    "process.env.SFTP_PASSWORD_PRODUCTION",
    process.env.SFTP_PASSWORD_PRODUCTION
  );

  const sftpConfig = {
    host: "************",
    username: process.env.SFTP_USERNAME,
    port: "22",
    password: process.env.SFTP_PASSWORD,
    remoteDir: req.body.path,
  };
  const sftp = new SftpClient();
  try {
    await sftp.connect(sftpConfig);
    console.log("sftp connected");
    const payload = req.body;
    if (!payload.method) {
      return res.send({
        code: 786,
        message:
          "method is missing in payload, following are the possible values [GET, DOWNLOAD]",
      });
    }
    if (!payload.path) {
      return res.send({
        code: 786,
        message:
          "path is missing in payload, Example: /home/<USER>/DEV/Indent/Process",
      });
    }
    if (payload.method.toLowerCase() == "get") {
      console.log("PAYLOAD INSIDE GET IF CONDITION", req.body.path);

      console.log(await sftp.list(req.body.path), "success list inside get");
      const sftpFiles = await sftp.list(req.body.path);
      return res.send({
        code: 0,
        data: sftpFiles,
        fileNames: sftpFiles.map((it) => it.name),
      });
    }
  } catch (error) {
    await sftp.end();
    console.log(error, "catch");
    res.send({
      code: 786,
      error: error,
    });
    return error;
  } finally {
    await sftp.end();
  }
});

app.use("/api", ApiRouter);

app.get("/healthcheck", (req, res) => {
  console.log("health test");
  res.status(200).json({ status: "Health Status - OK!!!" });
});

app.all("*", (req, res, next) => {
  next(
    new NewAppError(
      "UNKNOWN",
      `Can't find ${req.originalUrl} on this Server`,
      404
    )
  );
});

app.use(globalErrorHandler);

export default app;
