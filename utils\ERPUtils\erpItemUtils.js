import { getERPItemsInventory } from "../../service/ERPSync/productScheduledService.js";

const getErpItemsAvailableInventoryUtility = async (skus) => {
  try {
    const itemsInventory = await getERPItemsInventory(skus);
    if (itemsInventory.status !== 200) {
      return itemsInventory;
    }

    const availableInventory = skus.map((sku) => {
      const matchedERPItem = itemsInventory.data.items.find(
        (erpItem) => erpItem?.ItemNumber === sku
      );

      return {
        sku,
        quantity: matchedERPItem ? matchedERPItem.QuantityAvailableToShip : 0,
      };
    });

    return {
      data: { items: availableInventory },
    };
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error while fetching erp available inventory.Message:${error.message}`,
      500
    );
  }
};

export { getErpItemsAvailableInventoryUtility };
