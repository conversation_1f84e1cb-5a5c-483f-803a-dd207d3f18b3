extends baseEmail

block content
  p Hi #{emailPayload.name},
  p You requested a password reset for your account.
  p Please click the link below to reset your password:
  
  table(width='100%' cellpadding='0' cellspacing='0' border='0')
    tr
      td(align='center' style='padding: 20px 0;')
        table(cellpadding='0' cellspacing='0' border='0')
          tr
            td(align='center' bgcolor='#007bff' style='border-radius: 4px;')
              a(href=`${emailPayload.url}` target='_blank' style='display: inline-block; padding: 16px 36px; font-family: Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 4px;') Reset Your Password
  
  p(style='margin: 20px 0;') If you can't click the link above, copy and paste this URL into your browser:
  div(style='background-color: #f5f5f5; padding: 15px; border-radius: 4px; margin: 10px 0;')
    p(style='word-break: break-all; font-family: monospace; margin: 0;') #{emailPayload.url}
  
  p If you didn't request this email, please ignore it.
  p This password reset link will expire in 15 minutes.
  p If you need any help, please don't hesitate to contact us.
  p - Sunrise Team
