import express from "express";
import {
  shopifyOrderDataInDB,
  getOrders,
  getOneOrder,
  updateOrder,
  deleteOrder,
  getOrderByStatus,
  convertCartToOrder,
  orderReport,
  generateOrderSheet,
  generateOrderSheetNewController,
  editOrderFromSheet,
  manualAllocateOrderSheetController,
  storeOrderInDB,
  cancelOrderController,
  getDeptWiseOrders,
  getOrderShipmentsController,
  createOrderFromSheet,
  createCompleteOrder,
  removeDraftOrder,
  generateSampleSheetController,
  generateFullStockSheetController,
  getAllBudgetAndPreOrderValues,
  getPendingOrdersSheet,
} from "../controller/order.controller.js";
import { notifyStatusChange } from "../controller/common/index.js";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import { generateRandomNumber } from "../utils/helperFunction.js";
import { getUserToken } from "../controller/distributor.controller.js";
import { authenticateDepttPeopleAccessModuleWise } from "../middlewares/authenticateDepttPeople.js";
const router = express.Router();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "shipment"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".")[1];
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Retains the original file name
  },
});

const upload = multer({ storage });

const setStatusUpdateType = async (req, res, next) => {
  req.body.middlewareStatusCategory = "Order";
  next();
};

router
  .route("/")
  .get(
    authenticateDepttPeopleAccessModuleWise("order", "list", "read"),
    getDeptWiseOrders
  )
  .post(setStatusUpdateType, shopifyOrderDataInDB);

router
  .route("/:id/shipments")
  .get(
    authenticateDepttPeopleAccessModuleWise("shipment", "list", "read"),
    getOrderShipmentsController
  );

router.route("/report").get(orderReport);

router
  .route("/order-by-status")
  .get(
    authenticateDepttPeopleAccessModuleWise("order", "list", "read"),
    getOrderByStatus
  );

router.route("/cart/:cartId").post(convertCartToOrder);
router
  .route("/:id/cancel")
  .post(
    authenticateDepttPeopleAccessModuleWise("order", "cancelOrder"),
    cancelOrderController
  );
router.route("/:id/generate_sheet").get(generateOrderSheetNewController);

router.route("/:id/edit_sheet").post(upload.single("file"), editOrderFromSheet);
router
  .route("/:id/manualAllocate")
  .post(
    authenticateDepttPeopleAccessModuleWise("order", "manualAllocate"),
    upload.single("file"),
    manualAllocateOrderSheetController
  );

router.route("/storeOrderInDB").post(storeOrderInDB);
router.route("/upload").post(upload.single("file"), createOrderFromSheet);
router.route("/sample-sheet").get(generateSampleSheetController);
router
  .route("/full-stock-sheet")
  .get(authenticateDepttPeopleAccessModuleWise("order", "list", "read"), generateFullStockSheetController);
router.route("/filter-values").get(getAllBudgetAndPreOrderValues);
router
  .route("/pending/orders")
  .get(
    authenticateDepttPeopleAccessModuleWise("order", "list", "read"),
    getPendingOrdersSheet
  );

router
  .route("/:id")
  .get(getOneOrder)
  .patch(createCompleteOrder)
  .delete(removeDraftOrder);

export default router;
