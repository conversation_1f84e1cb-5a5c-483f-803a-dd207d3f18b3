import mongoose from "mongoose";
import { type } from "os";

const distributorSchema = new mongoose.Schema(
  {
    shopifyCustomerId: {
      type: Number,
      required: true,
    },
    customerNumber: {
      type: String,
    },
    name: {
      type: String,
      required: true,
    },
    firstName: {
      type: String,
    },
    lastName: {
      type: String,
    },
    email: {
      type: String,
      required: true,
    },
    phone: {
      type: String,
      required: true,
    },
    country: {
      type: String,
    },
    currentStep: {
      type: Number,
      // required: true,  // ! -> uncomment this , this will be required true for customer status update
    },
    shopifyCompanyId: {
      type: Number,
      required: true,
    },
    companyEmail: {
      type: String,
    },
    companyPhone: {
      type: String,
    },
    companyName: {
      type: String,
    },
    status: {
      type: String,
      default: "New Customer",
      required: true,
    },
    companyDetails: {
      tin: {
        type: String,
      },
      brn: {
        type: String,
      },
      nricNumber: {
        type: String,
      },
      obrn: {
        type: String,
      },
      sst: {
        type: String,
      },
      businessAddress: {
        type: String,
      },
      pincode: {
        type: String,
      },
      country:{
        type:String,
      },
      city:{
        type:String
      },
      state:{
        type:String
      },
      businessDescription: {
        type: String,
      },
      msicCodes: {
        type: String,
      },
      entityType: {
        type: String,
        enum: ["Limited Liability Company", "Sole Proprietor"],
      },
      documents: {
        form9: {
          type: String,
        },
        form24: {
          type: String,
        },
        form49: {
          type: String,
        },
        memArticle: {
          type: String,
        },
        icCard: {
          type: String,
        },
        lan: {
          type: String,
        },
        bankStatement: {
          type: String,
        },
        formD: {
          type: String,
        },
        form1: {
          type: String,
        },
      },
    },
    countryManager: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "DepartmentType",
      },
    ],
    customerSince: {
      type: String,
    },
    adresses: {
      type: Array,
    },
    timeline: [
      {
        date: {
          type: Date,
          default: new Date(),
        },
        comment: {
          type: String,
        },
      },
    ],
    salespersonId: {
      type: String,
    },
  },
  { timestamps: true }
);

const Distributor = mongoose.model("Distributor", distributorSchema);
export default Distributor;
