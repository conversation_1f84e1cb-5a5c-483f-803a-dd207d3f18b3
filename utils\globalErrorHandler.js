import AppError from "./appError.js";
import NewAppError from "./newAppError.js";

const handleCastErrorDb = (error) => {
  const message = `Invalid ${error.path}: ${error.value}`;
  return new AppError(message, 400);
};

const handleDuplicateValueDb = (error) => {
  const value = error.errmsg.match(/(["'])(?:(?=(\\?))\2.)*?\1/)[0];
  const message = `The value ${value} already Exist. Please use another value`;

  return new AppError(message, 400);
};

const handleValidationErrorDb = (error) => {
  const errors = Object.values(error.errors).map((el) => el.message);
  const message = `Invalid input Data. ${errors.join(". ")}`;
  return new AppError(message, 400);
};

const handleJwtError = (err) =>
  new AppError("Invalid Token Please Login Again ! ", 401);

const handleJWTExpiredError = (err) =>
  new AppError("JWT Expired Please Login Again !", 401);

const sendErrorDev = (error, res) => {
  res.status(error.statusCode).json({
    status: error.status,
    error: error,
    message: error.message,
    stack: error.stack,
  });
};

const sendErrorDevFormatted = (error, res) => {
  const formattedError = error.format();
  res.status(error.statusCode).json(formattedError);
};

const sendErrorProd = (error, res) => {
  if (error.isOperational) {
    res.status(error.statusCode).json({
      status: error.status,
      message: error.message,
    });
  } else {
    res.status(500).json({
      status: "error",
      message: "Something went wrong!",
    });
  }
};

const sendErrorProdFormatted = (error, res) => {
  if (error.isOperational) {
    const formattedError = error.format();
    res.status(error.statusCode).json(formattedError);
  } else {
    res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: "Something went wrong.",
        },
      ],
    });
  }
};

export default (error, req, res, next) => {
  error.statusCode = error.statusCode || 500;
  error.status = error.status || "error";

  if (process.env.NODE_ENV == "production") {
    if (error instanceof NewAppError) {
      sendErrorDevFormatted(error, res);
    } else {
      sendErrorDev(error, res);
    }
  }
  //  else if (process.env.NODE_ENV == "production") {
  //   if (error.name == "CastError") error = handleCastErrorDb(error);
  //   if (error.code == 11000) error = handleDuplicateValueDb(error);
  //   if (error.name == "ValidationError") error = handleValidationErrorDb(error);
  //   if (error.name === "JsonWebTokenError") error = handleJwtError(error);
  //   if (error.name === "TokenExpiredError")
  //     error = handleJWTExpiredError(error);
  //   sendErrorProd(error, res);
  // }
};
