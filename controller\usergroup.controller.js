import UserGroup from "../model/usergroup.model.js";
import { getAll } from "../utils/controllerFactory.js";

const allowedKeys = [
  "department",
  "designation",
  "distributor",
  "organization",
  "order",
  "shipment",
  "status",
  "statusFlow",
  "user",
  "user_group",
  "deptt_people"
];

export const getUserGroups = getAll(UserGroup);

export const getOneUserGroup = async (req, res) => {
  try {
    const userGroup = await UserGroup.findById(req.params.id);
    res.status(200).json(userGroup);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

export const createUserGroup = async (req, res) => {
  try {
    let validate = validateAccessScopes(req.body.access_scopes);
    if (validate) {
      return res.status(400).json(validate);
    }
    const userGroup = new UserGroup(req.body);
    const savedUserGroup = await userGroup.save();
    res.status(201).json(savedUserGroup);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};

export const updateUserGroup = async (req, res) => {
  try {
    if (req.params.id === "663b0f8c8ac71a1dbd8bf942")
      throw new Error("Can't edit this ");
    if (req.body.access_scopes) {
      let validate = validateAccessScopes(req.body.access_scopes);
      if (validate) {
        return res.status(400).json(validate);
      }
    }

    const removeIdFromReqBody = req.body;
    delete removeIdFromReqBody._id;

    const userGroup = await UserGroup.findByIdAndUpdate(
      req.params.id,
      removeIdFromReqBody,
      {
        new: true,
        runValidators: true,
      }
    );
    if (!userGroup) {
      return res.status(404).json({ error: "UserGroup not found" });
    }
    res.json(userGroup);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};


export const deleteUserGroup = async (req, res) => {
  try {
    if (req.params.id === "663b0f8c8ac71a1dbd8bf942")
      throw new Error("Can't delete this ");
    const userGroup = await UserGroup.findByIdAndDelete(req.params.id);
    if (!userGroup) {
      return res.status(404).json({ error: "UserGroup not found" });
    }
    res.json({ message: "UserGroup deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

function validateAccessScopes(access_scopes) {
  if (!access_scopes) {
    return { error: "Role must have at least one scope" };
  }
  let providedKeys = Object.keys(access_scopes);
  let isAnyOtherKeyPresent = providedKeys.filter(
    (key) => !allowedKeys.includes(key)
  );

  if (isAnyOtherKeyPresent.length) {
    return { error: `${isAnyOtherKeyPresent[0]} is not allowed` };
  }

  let oneSelected = true;
  for (let i = 0; i < providedKeys.length; i++) {
    if (access_scopes[providedKeys[i]].length) {
      oneSelected = false;
      break;
    }
  }
  if (oneSelected) {
    return { error: "Role must have at least one scope" };
  }
}
