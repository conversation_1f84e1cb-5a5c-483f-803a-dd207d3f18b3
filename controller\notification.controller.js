import EmailNotification from '../model/emailNotification.model.js';
import { createOne, deleteOne, getAll, getOne, updateOne } from '../utils/controllerFactory.js';

export const getEmailNotifications = getAll(EmailNotification);
export const getOneEmailNotification = getOne(EmailNotification);
export const createEmailNotification = createOne(EmailNotification);
export const updateEmailNotification = updateOne(EmailNotification);
export const deleteEmailNotification = deleteOne(EmailNotification);