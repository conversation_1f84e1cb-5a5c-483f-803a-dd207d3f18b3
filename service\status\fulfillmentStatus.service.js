import { v4 as uuidv4 } from 'uuid';
import fullfillmentStatus from "../../model/status/fulfillmentStatus.model.js";

export async function createFullfillmentStatus(newFullfillmentStatus) {
  const uniqId = uuidv4(); 
  const createFullfillmentStatus = fullfillmentStatus.create({
    id: `${uniqId}`,
    ...newFullfillmentStatus
  })
  return createFullfillmentStatus;
}


export async function updateFullfillmentStatus(updateFullfillmentStatus) {
  console.log(`${updateFullfillmentStatus.id} => `, " updateFullfillmentStatus.idupdateFullfillmentStatus.id")
  const updateFullfillmentStatusData = fullfillmentStatus.findOneAndUpdate(
    {id: `${updateFullfillmentStatus.id}`},
    {$set:{
      ...updateFullfillmentStatus
    }},
  )
  return updateFullfillmentStatusData;
}


export async function deleteFullfillmentStatus(deleteFullfillmentStatus){
  const { id } = deleteFullfillmentStatus;
  const deleteFullfillmentStatusData = await fullfillmentStatus.deleteOne({ id });

  return deleteFullfillmentStatusData;
}