name: Deploy to Dev EC2

on:
  push:
    branches: [dev]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: 🛠 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20" #node version to be used

      - name: Deploy via SSH to EC2
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.EC2_DEV_HOST }}
          username: ${{ secrets.EC2_DEV_USERNAME }}
          key: ${{ secrets.EC2_DEV_SSH_KEY }}
          script: |
            cd /home/<USER>/dev/Sunrise-trade-OMS-backend

            echo "${{ secrets.DEV_ENV_FILE }}" > .env

            git pull origin dev
            npm install
            pm2 restart Sunrise-B2B-Backend-Dev || pm2 start server.js --name "Sunrise-B2B-Backend-Dev"
            pm2 flush Sunrise-B2B-Backend-Dev
