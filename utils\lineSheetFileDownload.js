import ExcelJS from "exceljs";
import axios from "axios";
import fs from "fs";
import path from "path";
import { uploadToS3 } from "./helperFunction.js";
import LinesheetRecord from "../model/linesheet.model.js";

const __filename = decodeURI(new URL(import.meta.url).pathname).slice(1);
const __dirname = path.dirname(__filename);

const getImageBase64 = async (imageUrl) => {
  const response = await axios.get(imageUrl, {
    responseType: "arraybuffer",
  });
  const imageBuffer = Buffer.from(response.data, "binary");

  //to resize the image

  // const resizedBuffer = await sharp(imageBuffer)
  //   .resize({ width: 200 }) // Adjust the width as needed
  //   .png({ quality: 80 }) // Adjust the quality as needed (0-100)
  //   .toBuffer();
  // return resizedBuffer.toString("base64");

  return imageBuffer.toString("base64");
};

export const generateXlsxSheet = async (
  products,
  bucketName,
  type = "order"
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Images");
    const fetchedJsonData = products;
    let formattedJsonData;
    if (type === "shipment") {
      formattedJsonData = fetchedJsonData.map((item) => {
        return {
          title: item.productTitle,
          image: item.image ? item.image : "no image",
          productCode: item.sku,
          requested: item.requested || 0,
          fulfilled: item.fulfilled || 0,
          price: item.price || 0,
        };
      });
    } else {
      formattedJsonData = fetchedJsonData.map((item) => {
        return {
          title: item.productTitle,
          image: item.image ? item.image : "no image",
          productCode: item.sku,
          quantity: item.quantity || 0,
          price: item.price || 0,
        };
      });
    }
    if (!formattedJsonData.length) {
      throw new Error("Data not found");
    }

    const columns = Object.keys(formattedJsonData[0]);
    for (let i = 0; i < columns.length; i++) {
      const key = columns[i];
      const width = key === "image" ? 30 : 20; // Set width to 30 for 'image' column and 20 for others
      worksheet.getColumn(i + 1).width = width; // Set column width
      worksheet.getCell(1, i + 1).value =
        key.charAt(0).toUpperCase() + key.slice(1); // Set column header
    }

    const batchSize = 50; // Smaller batch size for parallel image fetching
    for (let i = 0; i < formattedJsonData.length; i += batchSize) {
      const batch = formattedJsonData.slice(i, i + batchSize);
      await Promise.all(
        batch.map(async (item, batchIndex) => {
          for (const [index, key] of Object.keys(item).entries()) {
            const cell = worksheet.getCell(
              `${String.fromCharCode(65 + index)}${i + batchIndex + 2}`
            );
            if (key === "image" && item[key] !== "no image") {
              const base64Image = await getImageBase64(item[key]);
              const imageId = workbook.addImage({
                base64: base64Image,
                extension: "png",
              });
              const range = `${String.fromCharCode(65 + index)}${
                i + batchIndex + 2
              }:${String.fromCharCode(65 + index)}${i + batchIndex + 2}`;
              worksheet.addImage(imageId, range);
            } else {
              cell.value = item[key];
            }
          }
        })
      );
    }

    // worksheet.getRow(1).height = 20; // Set height for heading tab
    // for (let i = 2; i <= formattedJsonData.length + 1; i++) {
    //   worksheet.getRow(i).height = 240; // Set height for other rows
    // }

    const linesheetDir = path.resolve(__dirname, "..", "asset", "linesheet");
    // console.log(`linesheetDir: ${linesheetDir}`);

    if (!fs.existsSync(linesheetDir)) {
      fs.mkdirSync(linesheetDir, { recursive: true });
    }

    const linesheetPath = path.join(
      linesheetDir,
      `linesheet_${Date.now()}.xlsx`
    );
    // console.log(`Saving file to: ${linesheetPath}`);

    await workbook.xlsx.writeFile(linesheetPath);

    const uploadResponse = await uploadToS3(linesheetPath, bucketName);
    // console.log("uploadResponse =======>", uploadResponse);

    if (uploadResponse.status !== "fail") {
      await LinesheetRecord.deleteMany({});
      await LinesheetRecord.create({
        downloadUrl: uploadResponse.Location,
        key: uploadResponse.key,
        etag: uploadResponse.ETag,
      });
    }
    fs.unlinkSync(linesheetPath); // Delete the temporary file after streaming
    return {
      status: "success",
      downloadUrl: uploadResponse.Location,
    };
  } catch (error) {
    console.error("(generateXlsxSheet) Error:", error);
  }
};
