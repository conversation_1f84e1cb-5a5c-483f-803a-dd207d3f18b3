import Inventory from '../model/inventory.model.js';
import { createOne, deleteOne, getAll, getOne, updateOne } from '../utils/controllerFactory.js';

export const getInventories = getAll(Inventory);
export const getOneInventory = getOne(Inventory);
export const createInventory = createOne(Inventory);
export const updateInventory = updateOne(Inventory);
export const deleteInventory = deleteOne(Inventory);
