import CronLog from "../model/cronData.model.js";
import Status from "../model/status.model.js";
import { syncERPProducts } from "../service/ERPSync/productSyncFlow.js";
import catchAsync from "../utils/catchAsync.js";
import APIFeatures from "../utils/apiFeatures.js";
import moment from "moment-timezone";
import { createCollectionsFromProductMetafields } from "./createCollectionsFromProductMetafields.controller.js";
import { checkAndRemovePreorderTag } from "./preOrderCheck.controller.js";

export async function runCronJob(triggeredBy = "cron", res = null) {
  // Check if a run is already in progress
  const latestRun = await CronLog.findOne().sort({ createdAt: -1 });

  if (latestRun && !latestRun.endTime) {
    return { status: "skipped", message: "Skipped already running" };
  }

  // Insert a new cron log with status "running"
  const newRun = await CronLog.create({
    startTime: new Date(),
    triggeredBy,
    status: "running",
  });

  if (res) {
    res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        message: "Cron job started",
      },
    });
  }

  try {
    const {
      totalProducts = null,
      processedProducts = null,
      completedPages = null,
    } = await syncERPProducts();
    const endTime = new Date();
    const duration = endTime - newRun.startTime;

    await CronLog.findByIdAndUpdate(newRun._id, {
      endTime,
      duration,
      status: "success",
      totalProducts,
      processedProducts,
      completedPages,
    });

    await createCollectionsFromProductMetafields();
   
    await checkAndRemovePreorderTag();

      
    const msg = `Success: completed in ${duration} ms`;
    return { status: "success", message: msg, duration };
  } catch (err) {
    const endTime = new Date();
    const duration = endTime - newRun.startTime;

    await CronLog.findByIdAndUpdate(newRun._id, {
      endTime,
      duration,
      status: "failed",
    });

    const msg = `Failed: ${err.message}`;
    return { status: "failed", message: msg, duration };
  }
}

export const runCronManually = catchAsync(async (req, res, next) => {
  const result = await runCronJob("manual", res);

  if (result.status === "skipped") {
    return res.status(207).json({
      responseCode: 0,
      status: "success",
      data: {
        message: "Cron job already running",
      },
    });
  }

  if (result.status === "failed") {
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: `Something went wrong `,
        },
      ],
    });
  }

  return res.status(200).json({
    responseCode: 0,
    status: "success",
    data: {
      message: "Cron job executed successfully",
      duration: result.duration,
    },
  });
});

export const getLatestCronStatus = catchAsync(async (req, res) => {
  const latest = await CronLog.findOne().sort({ createdAt: -1 });
  const isRunning = latest && !latest.endTime;

  return res.status(200).json({
    responseCode: 0,
    success: true,
    data: {
      loading: isRunning,
    },
  });
});

export const getAllCronStatus = catchAsync(async (req, res) => {
  const features = new APIFeatures(CronLog.find(), req.query)
    .filter()
    .sort()
    .limitFields()
    .paginate();
  const doc = await features.query;
  const totalCount = await CronLog.countDocuments();
  
  const processedData = doc.map(record => {
    const recordObj = record.toObject();
  return {
    ...recordObj,
    totalProducts: recordObj.totalProducts || 0,
    startTimeMY: recordObj.startTime
      ? moment(recordObj.startTime).tz("Asia/Kuala_Lumpur").format()
      : null,
    endTimeMY: recordObj.endTime
      ? moment(recordObj.endTime).tz("Asia/Kuala_Lumpur").format()
      : null,
  };
  });
  
  return res.status(200).json({
    responseCode: 0,
    success: true,
    data: {
      data: processedData,
      totalCount,
    },
  });
});
