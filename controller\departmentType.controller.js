import DepartmentType from '../model/departmentType.model.js';
import { createOne, deleteOne, getAll, getOne, updateOne } from '../utils/controllerFactory.js';


export const getDepartmentsType = getAll(DepartmentType);
export const getOneDepartmentType = getOne(DepartmentType);
export const createDepartmentType = createOne(DepartmentType);
export const updateDepartmentType = updateOne(DepartmentType);
export const deleteDepartmentType = deleteOne(DepartmentType);