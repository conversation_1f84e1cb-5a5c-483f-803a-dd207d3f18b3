import { CHECK_CUSTOMER_EXISTENCE_BY_ID } from "../../controller/graphqlApis.js";
import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";

export async function GetCustomerCompanyContactProfileByCustId(customerId) {
  try {
    const response = await fetchGraphqlDataShopify(
      CHECK_CUSTOMER_EXISTENCE_BY_ID,
      {
        id: customerId,
      }
    );

    if (!response) return null;

    const customer = response?.data?.customer;
    if (customer) {
      const customerId = customer?.id;
      const email = customer?.email;
      const companyContactId = customer?.companyContactProfiles[0]?.id;
      const companyId = customer?.companyContactProfiles[0]?.company?.id;
      const isMainContact =
        customer?.companyContactProfiles[0]?.company?.mainContact
          ?.isMainContact;
      return {
        customerId: customerId,
        email: email,
        companyId: companyId,
        isMainContact: isMainContact,
        companyContactId: companyContactId,
      };
    }
    return null;
  } catch (error) {
    console.log("GetCustomerCompanyContactProfileByCustId Error : ", error);
    throw error;
  }
}
