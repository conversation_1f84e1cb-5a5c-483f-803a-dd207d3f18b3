const detailsKeysWithSteps = {
  step1: ["email", "companyName", "name", "phone", "tin"],
  step2: [
    "brn",
    "nricNumber",
    "obrn",
    "sst",
    "businessAddress",
    "businessDescription",
    "msicCodes",
    "pincode",
    "city",
    "state",
    "country"
  ],
  step3: [
    "entityType",
    "form9",
    "form24",
    "form49",
    "memArticle",
    "icCard",
    "lan",
    "bankStatement",
    "formD",
    "form1",
  ],
  step4: [],
};

const metafieldKeys = [
  "currentStep",
  "companyName",
  "email",
  "name",
  "phone",
  "tin",
  "brn",
  "nricNumber",
  "obrn",
  "sst",
  "businessAddress",
  "businessDescription",
  "msicCodes",
  "entityType",
  "form9",
  "form24",
  "form49",
  "memArticle",
  "icCard",
  "lan",
  "bankStatement",
  "formD",
  "form1",
];

const stepRequirements = {
  step1: {
    mandatory: ["email", "companyName", "name", "phone"],
    optional: ["tin"],
  },
  step2: {
    mandatory: ["brn", "businessAddress", "businessDescription", "msicCodes"],
    optional: ["nricNumber", "obrn", "sst"],
  },
  step3: {
    mandatory: ["entityType"], // Entity type is mandatory
    entityTypeValidation: {
      "Limited Liability Company": {
        mandatory: ["form9", "form24", "form49", "memArticle"],
        optional: ["icCard", "lan", "bankStatement"],
      },
      "Sole Proprietor": {
        mandatory: ["formD", "form1", "icCard"],
        optional: [],
      },
    },
  },
  step4: {},
};

function CustomCompanyDetails(company) {
  try {
    const companyDetails = CreateDetailsFromCompany(company);
    const customCompany = {
      currentStep: company.currentStep,
      companyId: company.shopifyCompanyId,
      details: companyDetails,
    };

    return customCompany;
  } catch (error) {
    console.log("CustomCompanyDetails Error : ", error);
    throw error;
  }
}

function CreateDetailsFromCompany(company) {
  try {
    const { email, phone, name, companyName, companyDetails } = company;
    const details = { ...companyDetails, email, phone, name, companyName };
    // Map of metafields grouped by steps
    const stepwiseDetails = {};

    for (const [step, keys] of Object.entries(detailsKeysWithSteps)) {
      stepwiseDetails[step] = {};
      keys.forEach((key) => {
        // Check if key is part of the documents object in details
        if (details?.documents && details?.documents[key]) {
          stepwiseDetails[step][key] = details?.documents[key] || null;
        } else if (details[key]) {
          stepwiseDetails[step][key] = details[key] || null;
        } else {
          stepwiseDetails[step][key] = null;
        }
      });
    }
    return stepwiseDetails;
  } catch (error) {
    console.error("CreateDetailsFromCompany Error:", error);
    throw error;
  }
}

function splitName(completeName) {
  const [firstName, ...rest] = completeName.trim().split(" ");
  const lastName = rest.join(" ");

  return {
    firstName: firstName || "",
    lastName: lastName || "",
  };
}

function ConvertToGID(id, identifier) {
  return `gid://shopify/${identifier}/${id}`;
}

function ConvertFromGID(gid) {
  // Split the GID string by "/" and return the last part (the numerical ID)
  const parts = gid.split("/");
  return parts[parts.length - 1];
}

export {
  stepRequirements,
  CustomCompanyDetails,
  splitName,
  ConvertToGID,
  ConvertFromGID,
};
