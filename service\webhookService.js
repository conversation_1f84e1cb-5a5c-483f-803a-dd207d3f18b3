import axios from "axios";

export default async function orderCreationWebhook() {
  await axios
    .request({
      method: "post",
      url: `${process.env.SHOP_URL}/admin/api/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        query: `mutation MyMutation {
        webhookSubscriptionCreate(
          webhookSubscription: {callbackUrl: "${process.env.APP_URL}/api/order/storeOrderInDB", format: JSO<PERSON>}
          topic: ORDERS_CREATE
        ) {
          userErrors {
            field
            message
          }
          webhookSubscription {
            id
            updatedAt
            includeFields
            createdAt
            callbackUrl
          }
        }
      }`,
        variables: {},
      }),
    })
    .then((response) => {
      console.log(
        "ORDER CREATED WEBHOOK",`${process.env.APP_URL}/api/order/storeOrderInDB", format: JSO<PERSON>}`,
        response.data.data.webhookSubscriptionCreate
      );
      return true;
    })
    .catch((error) => {
      console.log(error, " error during webhook subscription");
      return error;
    });
}
