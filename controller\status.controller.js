import Status from '../model/status.model.js';
import catchAsync from '../utils/catchAsync.js';
import { createOne, deleteOne, getAll, getOne, updateOne } from '../utils/controllerFactory.js';
import { filterObj, removeObjectKeys } from '../utils/helperFunction.js';


export const getStatus = getAll(Status);
export const getOneStatus = getOne(Status);
export const createStatus = createOne(Status);
export const updateStatus = updateOne(Status);
export const deleteStatus = deleteOne(Status);

export const filterStatusPayloadKeys = async (req, res, next) => {
  if(!req.body.forceUpdate) {
    req.body = removeObjectKeys(req.body, "isInitialStatus");
  }
  next();
};

export const updateStatusCustomField = catchAsync(async (req, res, next) => {
  const statusId = req.params.id;
  // const fieldId = req.params.fieldId;
  const customFields = req.body.customFields;
  const isCustomFieldUpdateRequired = req.body.customFields?.length > 0;
  if (isCustomFieldUpdateRequired) {
    const setOperations = {}; // To store $set operations
    const arrayFilters = []; // To store array filters
    customFields.forEach((update) => {
      console.logupdate
      const { fieldId, ...fieldsToUpdate } = update;
      const filterName = `cf${fieldId.toLowerCase().replace(/[^a-z0-9]+/g, '')}`;
      // Unique name for array filter
      arrayFilters.push({ [`${filterName}.fieldId`]: fieldId }); // Define array filter
      // Create $set operations for each key-value pair in the update
      Object.entries(fieldsToUpdate).forEach(([key, value]) => {
        setOperations[`customFields.$[${filterName}].${key}`] = value;
      });
    });
    console.log('arrayFilters', arrayFilters);
    console.log('customFields', customFields);
    const updateResult = await Status.updateOne(
      { _id: statusId }, // Find the document by ID
      { $set: setOperations }, // Apply the set operations
      { arrayFilters } // Apply the defined array filters
    );
  }
  req.body.customFields = undefined;
  next();
  // res.status(200).json({
  //   status: 'success',
  //   data: updateResult
  // });
});

export const deleteStatusCustomField = catchAsync(async (req, res, next) => {
  const statusId = req.params.id;
  const fieldId = req.params.fieldId;
  const updateResult = await Status.updateOne(
    { _id: statusId },
    { $pull: { customFields: { fieldId: fieldId } } }
  );
  res.status(200).json({
    status: 'success',
    data: updateResult
  });
});