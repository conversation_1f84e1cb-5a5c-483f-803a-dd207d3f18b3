import mongoose from "mongoose";
import Distributor from "./distributor.model.js";
import { UUID } from "mongodb";

const orderSchema = new mongoose.Schema(
  {
    order_id: {
      type: Number,
      required: true,
    },
    subOrderId: {
      type: String,
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    productCategory: {
      type: String,
    },
    current_subtotal_price: {
      type: String,
    },
    totalQuantity: {
      type: String,
    },
    shopifyCompanyId: {
      type: Number,
      required: true,
    },
    shopifyCompanyLocationId: {
      type: Number,
    },
    cartId: {
      type: String,
    },
    status: {
      type: String,
      required: true,
      enum: [
        "pending",
        "created",
        "order placed",
        "rejected",
        "invoiced",
        "on hold",
        "order completed",
        "out for drlivery",
        "order deleted",
      ],
    },
    distributor: {
      type: mongoose.Schema.ObjectId,
      ref: "Distributor",
      // required: true,
    },
    financial_status: {
      type: String,
    },
    customer: {
      type: Object,
    },
    isFuture: {
      type: String,
      default: "false",
    },
    erpIdentifier: {
      type: String,
    },
    sageStatus: {
      type: String,
    },
    sageOrderNumber: {
      type: String,
    },
    line_items: {
      type: Array,
    },
    type: {
      type: String,
      enum: ["pre-order", "budget-category"],
    },
    budgetCategoryValue: {
      type: String,
    },
    preOrderValue: {
      type: String,
    },
    created_at: {
      type: String,
    },
    sku: {
      type: String,
    },
    shipping_address: {
      type: mongoose.Schema.ObjectId,
      ref: "Address",
      // required: true,
    },
    billing_address: {
      type: mongoose.Schema.ObjectId,
      ref: "Address",
      // required: true,
    },
    attributes: [
      {
        attributeId: {
          type: String,
          required: true,
          default: new UUID(),
        },
        name: {
          type: String,
        },
        type: {
          type: String,
        },
        value: {
          type: Array,
        },
      },
    ],
    timeline: {
      type: Array,
      default: [
        {
          time: new Date(),
          comment: "Order created",
        },
      ],
    },
  },
  { timestamps: true }
);

orderSchema.pre(/^find/, function (next) {
  this.populate({
    path: "status",
    select: "status pseudoId colorCode",
  })
    .populate({
      path: "distributor",
      // select:
      //   "name shopifyCompanyId email phone priority customerNumber salespersonId",
    })
    .populate({
      path: "shipping_address",
      // select: "name shopifyCompanyId email priority",
    })
    .populate({
      path: "billing_address",
      // select: "name shopifyCompanyId email priority",
    })
    .lean();
  next();
});

const Order = mongoose.model("Order", orderSchema);
export default Order;
