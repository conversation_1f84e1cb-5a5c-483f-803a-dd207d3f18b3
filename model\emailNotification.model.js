import mongoose from "mongoose";

const emailNotificationSchema = new mongoose.Schema(
  {
    emailCategory: {
      type: String,
    },
    emailType: {
      type: String,
      enum: [
        "ORDER_CREATE",
        "PASSWORD_RESET",
        "ORDER_CREATED_IN_SAGE",
        "ORDER_PLACED",
        "ORDER_INVOICED",
        "ORDER_OUT_FOR_DELIVERY",
        "ORDER_DELIVERED",
        "ORDER_CANCELLED",
        "CUSTOMER_APPROVAL_1",
        "CUSTOMER_APPROVAL_2",
        "STOCK_SHEET_ERROR",
        "STOCK_SHEET_READY",
      ],
    },
    isProcessed: {
      type: Boolean,
      default: false,
    },
    reciepient: {
      required: true,
      type: Object,
      name: {
        type: String,
      },
      email: {
        type: String,
      },
    },
    emailPayload: {
      type: Object,
    },
    note: {
      type: String,
    },
    tag: {
      type: String,
    },
  },
  { timestamps: true }
);

const EmailNotification = mongoose.model(
  "EmailNotification",
  emailNotificationSchema
);
export default EmailNotification;
