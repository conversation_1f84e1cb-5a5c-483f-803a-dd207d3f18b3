import express from 'express';
import {
  getCarts,
  getOneCart,
  updateCart,
  deleteCart,
  updateCartLineItem,
  deleteCartLineItem,
  createCartFromSheet,
  createCustomProductCart,
} from '../controller/cart.controller.js';
import multer from "multer"
import { fileURLToPath } from 'url';
import path from 'path';
import { generateRandomNumber } from '../utils/helperFunction.js';
// import { downloadShipmentExcel, uploadShipmentExcel } from '../../controller/shipment/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);


const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '..', 'asset', 'upload', 'order'));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split('.')[1]
    const originalFileName = file.originalname.split('.')[0]
    cb(null, `${originalFileName.split(" ").join("_")}_${generateRandomNumber()}.${fileExtension}`); // Retains the original file name
  },
});

const upload = multer({ storage });

const router = express.Router();

router.post('/upload', upload.single('file'), createCartFromSheet);

router
  .route('/')
  .get(getCarts)
  .post(getOneCart);

  router
  .route('/custom_product')
  .post(createCustomProductCart);

  router
  .route('/line_item/:cartId')
  .patch(updateCartLineItem);
  
  router
  .route('/line_item/:cartId/:lineItemId')
  .delete(deleteCartLineItem);
  
router
  .route('/:id')
  .get(getOneCart)
  .patch(updateCart)
  .delete(deleteCart);

export default router;