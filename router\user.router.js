import express from "express";

import {
  getUsers,
  getOneUser,
  createUser,
  updateUser,
  deleteUser,
  loginUser,
  forgotPassword,
  resetPassword,
  verifyResetToken
} from "../controller/user.controller..js";

const router = express.Router();

router.route("/").get(getUsers).post(createUser);
router.route("/login").post(loginUser);
router.route("/forgot-password").post(forgotPassword);
router.route("/reset-password").post(resetPassword);
router.route("/verify-reset-token/:token").get(verifyResetToken);
router.route("/:id").get(getOneUser).patch(updateUser).delete(deleteUser);

export default router;
