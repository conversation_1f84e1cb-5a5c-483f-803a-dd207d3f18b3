import { getProductsQuery, updateProductTagsMutation, updateProductMetafieldMutation } from './shopify.controller.js';

const checkAndUpdateAllProducts = async () => {
  const updatedProducts = [];
  let cursor = null;
  let hasNextPage = true;

  try {
    while (hasNextPage) {
      // Use Shopify controller function to fetch products
      const response = await getProductsQuery({ cursor });
      const edges = response.data.products.edges;
      hasNextPage = response.data.products.pageInfo.hasNextPage;
      if (!edges.length) break;
      const today = new Date().toISOString().split('T')[0];

      // Prepare all metafield/tag update promises for this batch
      const updatePromises = edges.map(async (edge) => {
        const product = edge.node;
        // Find expiry date from 'preorder_expiry_date' metafield
        let expiryDate = null;
        if (product.metafields && Array.isArray(product.metafields.edges)) {
          const expiryMeta = product.metafields.edges.find(m => m.node.key === 'preorder_expiry_date' && m.node.namespace === 'custom');
          if (expiryMeta) expiryDate = expiryMeta.node.value;
        } else {
          expiryDate = product.metafield?.value; // fallback for old query
        }
        let newTags = [...product.tags];
        let action = null;
        let metafieldUpdated = false;
        let metafieldUserErrors = null;
        // Parse expiryDate to YYYY-MM-DD for comparison
        let parsedExpiryDate = expiryDate;
        if (expiryDate && !/^\d{4}-\d{2}-\d{2}$/.test(expiryDate)) {
          // Try to parse formats like 'Jul 15, 2025'
          const dateObj = new Date(expiryDate);
          if (!isNaN(dateObj)) {
            parsedExpiryDate = dateObj.getFullYear() + '-' + String(dateObj.getMonth() + 1).padStart(2, '0') + '-' + String(dateObj.getDate()).padStart(2, '0');
          }
        }

        if (product.tags.includes('pre_order') && parsedExpiryDate && today >= parsedExpiryDate) {
          // Expired: Remove 'pre_order' tag, set PRODUCT_TYPE to 'regular_order', and add 'regular_order' tag if not present
          newTags = newTags.filter(tag => tag !== 'pre_order');
          if (!newTags.includes('regular_order')) {
            newTags.push('regular_order');
          }
          action = 'remove_pre_set_type_regular';
          const metafieldResponse = await updateProductMetafieldMutation({
            metafields: [{
              ownerId: product.id,
              namespace: 'custom',
              key: 'PRODUCT_TYPE',
              type: 'single_line_text_field',
              value: 'regular_order',
            }],
          });
          metafieldUpdated = true;
          metafieldUserErrors = metafieldResponse.data.metafieldsSet.userErrors;
        } else if (product.tags.includes('pre_order') && parsedExpiryDate && today < parsedExpiryDate) {
          const metafieldResponse = await updateProductMetafieldMutation({
            metafields: [{
              ownerId: product.id,
              namespace: 'custom',
              key: 'PRODUCT_TYPE',
              type: 'single_line_text_field',
              value: 'pre_order',
            }],
          });
          metafieldUpdated = true;
          metafieldUserErrors = metafieldResponse.data.metafieldsSet.userErrors;
        } else if (!expiryDate && product.tags.includes('pre_order')) {
          // If expiry date is empty, set PRODUCT_TYPE to 'pre_order'
          action = 'set_type_preorder_no_expiry';
          const metafieldResponse = await updateProductMetafieldMutation({
            metafields: [{
              ownerId: product.id,
              namespace: 'custom',
              key: 'PRODUCT_TYPE',
              type: 'single_line_text_field',
              value: 'pre_order',
            }],
          });
          metafieldUpdated = true;
          metafieldUserErrors = metafieldResponse.data.metafieldsSet.userErrors;
        } else if (!product.tags.includes('pre_order')) {
          // No pre_order tag: set PRODUCT_TYPE to 'regular_order' and add 'regular_order' tag if not present
          action = 'set_type_regular_no_tag';
          if (!newTags.includes('regular_order')) {
            newTags.push('regular_order');
          }
          const metafieldResponse = await updateProductMetafieldMutation({
            metafields: [{
              ownerId: product.id,
              namespace: 'custom',
              key: 'PRODUCT_TYPE',
              type: 'single_line_text_field',
              value: 'regular_order',
            }],
          });
          metafieldUpdated = true;
          metafieldUserErrors = metafieldResponse.data.metafieldsSet.userErrors;
        }

        // Only update if tags actually change
        if (JSON.stringify(newTags.sort()) !== JSON.stringify(product.tags.sort())) {
          // Use Shopify controller function to update tags
          const mutationResponse = await updateProductTagsMutation({
            input: {
              id: product.id,
              tags: newTags,
            },
          });
          const updated = mutationResponse.data.productUpdate.product;
          const userErrors = mutationResponse.data.productUpdate.userErrors;

          updatedProducts.push({
            id: product.id,
            oldTags: product.tags,
            newTags,
            userErrors,
            action,
            expiryDate,
            today,
            metafieldUpdated,
            metafieldUserErrors
          });
        } else if (metafieldUpdated) {
          // Log metafield update even if tags didn't change
          updatedProducts.push({
            id: product.id,
            oldTags: product.tags,
            newTags,
            userErrors: metafieldUserErrors,
            action: 'metafield_only',
            expiryDate,
            today,
            metafieldUpdated,
            metafieldUserErrors
          });
        }
      });

      // Run all updates in parallel for this batch
      await Promise.all(updatePromises);

      // 📌 3️⃣ Move cursor forward for next batch
      cursor = edges[edges.length - 1].cursor;
    }

    // return res.status(200).json({
    //   message: `✅ Done. Updated products: ${updatedProducts.length}`,
    //   updatedProducts,
    // });

    console.log(`✅ Done. Updated products: ${updatedProducts.length}`)
    return;

  } catch (error) {
    console.error('Error:', error);
    // return res.status(500).json({
    //   message: 'Internal server error',
    //   error: error.message || error,
    // });
  }
};

export { checkAndUpdateAllProducts as checkAndRemovePreorderTag };