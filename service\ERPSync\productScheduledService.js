import axios from "axios";
import SyncRecord from "../../model/syncRecordModel.js";
import ProductMap from "../../model/erp.productmap.model.js";
import FailedProductLog from "../../model/erp.product.error.model.js";
import {
  UPDATE_PRODUCT_VARIANTS,
  GET_SHOPIFY_LOCATIONS,
  CREATE_LOCATION_SHOPIFY,
  INVENTORY_SET_QUANTITIES,
} from "../../queries/internalQueries.js";
import {
  PRODUCT_DUMMY_DATA,
  PRODUCT_STATUS,
  NAMESPACE,
  ITEM_LOCATION_DUMMY,
  ITEM_PRICE_DUMMY,
  PRODUCT_DUMMY_DATA_INITIAL,
  ITEM_LOCATION_DUMMY_INITIAL,
} from "../../constants/erpConstants.js";
import { getSyncRecordFromDB, saveSyncRecord } from "../ERPSync/syncRecord.js";

import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";

const productScheduledService = async (manual = false, initial = false) => {
  try {
    const itemsLocation = await getERPItemsAndLocations(manual, initial);
    if (!itemsLocation) {
      throw Error(`Error when fetching itemLocations from ERP.`);
    }
    if (!itemsLocation?.length) {
      throw Error(`No item fetched from itemLocation ERP.`);
    }

    const locationsShopify = await handleNewLocation(itemsLocation);

    if (!locationsShopify) {
      throw Error(`Error when handeling new itemLocations.`);
    }

    const products = await getERPItemsAndCreate(
      itemsLocation,
      locationsShopify,
      manual,
      initial
    );
    if (manual) {
      return products;
    } else if (initial) {
      console.log(
        products?.data?.length,
        "initial sync. Finding already committed items..."
      );

      await adjustInitialSyncInventory(products?.data, itemsLocation);
    }
  } catch (error) {
    //log
    console.log("Error during  product sync:", error?.message);
    if (manual) {
      throw error;
    }
  }
};

const getERPItemsAndCreate = async (
  itemsLocation = [],
  locationsShopify = [],
  manual,
  initial
) => {
  const syncRecord = await getSyncRecordFromDB();
  const lastSyncDate = syncRecord?.customerlastSyncDate || new Date(0);
  const currentSyncDate = new Date().toISOString();

  let retryCount = 0;
  let skipValue = 0;
  let nextLink;
  const select =
    "$select=UnformattedItemNumber,ItemNumber,Description,Segment1,Segment2,Segment3,Status,QuantityAvailable,AccountSetCode,DefaultPriceListCode,StockingUnitOfMeasure,CostingMethod,DefaultPriceListCode,ItemOptionalFields";

  let baseURL = `${process.env.SAGE_API_ENDPOINT}/IC/ICItems?${select}`;

  let products;
  try {
    while (true) {
      nextLink = `${baseURL}&$skip=${skipValue}`;
      let config = {
        method: "get",
        maxBodyLength: Infinity,
        url: nextLink,
        headers: {
          "Content-Type": `${process.env.API_CONTENT_TYPE}`,
          Accept: `${process.env.API_CONTENT_TYPE}`,
          Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
        },
      };

      let response;

      try {
        // response = initial ? PRODUCT_DUMMY_DATA_INITIAL : PRODUCT_DUMMY_DATA;
        response = await axios.request(config);
      } catch (error) {
        console.error("Error fetching products:", error.message);
        retryCount++;
        //log
        let message = `Error fetching Batch. Skipping this Batch Link:${nextLink}.Error:${error.message}`;
        await saveFailedProduct(
          null,
          null,
          "getERPItemsAndCreate",
          { manual },
          message
        );

        if (retryCount > 2) {
          throw Error(`Unable to fetch Items.Logs Saved.Aborting..`);
          break;
        }

        continue;
      }

      products = response?.data?.value || [];
      if (!products?.length) {
        console.log(`No items fetched from ERP IC items.`);
        break;
      }
      console.log(
        `Retrieved ${products.length} products in the current batch.`
      );

      // Process the current batch of products using processProductData
      const processed = await processProductData(
        products,
        itemsLocation,
        locationsShopify,
        manual
      );

      if (!processed) {
        console.log(
          `Error while processing for link :${nextLink}.SyncDate:${currentSyncDate}`
        );
      }

      const nextLinkURL = response.data["@odata.nextLink"];

      if (!nextLinkURL) {
        console.log("No more data to fetch. Stopping the loop.");
        break;
      }

      const urlParams = new URLSearchParams(nextLinkURL.split("?")[1]);
      skipValue = urlParams.get("$skip") || skipValue;

      retryCount = 0;
    }

    await saveSyncRecord(currentSyncDate, "product");
    if (manual) {
      return {
        status: 200,
        message: `Sync complete.Sync time:${currentSyncDate}.Manual:${manual}`,
      };
    }
    if (initial) {
      const productData = products.map((ele) => {
        return {
          UnformattedItemNumber: ele?.UnformattedItemNumber,
        };
      });
      return {
        data: productData,
      };
    }
  } catch (error) {
    const message = `Error fetching products from ERP. Message: ${error?.message}.Link:${nextLink}`;
    await saveFailedProduct(
      null,
      null,
      "getERPItemsAndCreate",
      { manual },
      message
    );
    throw new Error(message);
  }
};

const processProductData = async (
  productDataArray,
  itemsLocation,
  locationsShopify,
  manual
) => {
  try {
    const batchSegmentsMapping = await getERPItemSegmentsBatch(
      productDataArray
    );
    for (const productData of productDataArray) {
      const {
        Segment1: segment1,
        Segment2,
        Segment3,
        UnformattedItemNumber: sku,
        ItemNumber,
        // QuantityAvailable,
        Description: title,
        ItemOptionalFields,
      } = productData;
      console.log(
        "===============================start prosessing item============================================"
      );

      try {
        //get location
        const getItemLocation = itemsLocation.find(
          (item) => item?.ItemNumber === sku
        );

        const itemLocation = getItemLocation?.Location;

        const itemLocationGId = locationsShopify.find(
          (item) => item.name === itemLocation
        )?.id;
        console.log(
          `itemLocation:${itemLocation},itemLocationId:${itemLocationGId}`
        );

        const itemLocationId = itemLocationGId?.split("/").pop();

        if (!itemLocation || !itemLocationGId) {
          console.log(
            `No matching item Location Gid  or associated shopifyLocation for UnformattedItemNumber:${sku}`
          );
          continue;
        }

        // Segment color size

        const segmentData =
          batchSegmentsMapping?.success &&
          batchSegmentsMapping.data.find((items) => items.Item === ItemNumber);

        const color = (segmentData?.Color?.trim() === "ZZZZ" || !segmentData?.Color?.trim()) ? "NA" : (segmentData?.Color || Segment2);
        const size = (segmentData?.Size?.trim() === "ZZZZ" || !segmentData?.Size?.trim()) ? "NA" : (segmentData?.Size || Segment3);
        //quantity
        const quantityAvailable = getItemLocation?.QuantityAvailableToShip || 0;
        const quantityOnHand = getItemLocation?.QuantityOnHand || 0;
        // Get the price from the optional fields
        const optionalFields = getOptionalFields(ItemOptionalFields || []);

        const itemPriceData1 = await getERPItemPrice(
          sku,
          "MYR",
          `${process.env.SAGE_PRICE_LIST_CODE_DEALER}`
        );

        const itemPriceData2 = await getERPItemPrice(
          sku,
          "MYR",
          `${process.env.SAGE_PRICE_LIST_CODE_RSP}`
        );

        const itemPriceData = Math.min(itemPriceData1, itemPriceData2);
        const itemComparedAtPriceData = Math.max(
          itemPriceData1,
          itemPriceData2
        );

        const existingMapping = await ProductMap.findOne({
          segment1: segment1,
        });
        // Check if segment1 already exists in the productMap
        if (existingMapping) {
          console.log(
            `Product exists for segment1: ${segment1}. Adding or updating variant...`
          );
          await addOrUpdateVariant(
            existingMapping.productId,
            {
              size,
              color,
              sku,
              itemLocation,
              itemLocationId,
              quantityAvailable,
              quantityOnHand,
              itemPriceData,
              itemComparedAtPriceData,
              optionalFields,
            },
            existingMapping.variants
          );
        } else {
          console.log(
            `No product found for segment1: ${segment1}. Creating new product...`
          );
          await createProductWithVariant(
            {
              segment1,
              size,
              color,
              sku,
              itemLocation,
              itemLocationId,
              quantityAvailable,
              quantityOnHand,
              title,
              itemPriceData,
              itemComparedAtPriceData,
              optionalFields,
            },
            manual
          );
        }

        console.log(
          "===============================End prosessing item============================================"
        );
      } catch (error) {
        await saveFailedProduct(
          segment1,
          sku,
          "processProductData",
          { manual },
          error.message
        );
        console.error(
          `Error processing product (Segment1: ${segment1}, SKU: ${sku}). Error: ${error.message}`
        );

        continue;
      }
    }
    return true;
  } catch (error) {
    console.log(error?.message);
    return null;
  }
};

const createProductWithVariant = async (productData, manual) => {
  try {
    const response = await axios.post(
      `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/products.json`,
      {
        product: {
          title: productData?.title,
          status: PRODUCT_STATUS,
          metafields: [
            { namespace: NAMESPACE, key: "model", value: productData.segment1 },
            {
              namespace: NAMESPACE,
              key: "budget_category",
              value: productData.optionalFields.budgetCategory,
            },
            {
              namespace: NAMESPACE,
              key: "compare_at_price",
              value: String(productData?.itemComparedAtPriceData || 0),
            },
          ],
          options: [
            { name: "Color", values: [(productData.color?.trim() === "ZZZZ" || !productData.color?.trim()) ? "NA" : productData.color?.trim()] },
            { name: "Size", values: [(productData.size?.trim() === "ZZZZ" || !productData.size?.trim()) ? "NA" : productData.size?.trim()] },
          ],
          variants: [
            {
              option1: (productData.color?.trim() === "ZZZZ" || !productData.color?.trim()) ? "NA" : productData.color?.trim(),
              option2: (productData.size?.trim() === "ZZZZ" || !productData.size?.trim()) ? "NA" : productData.size?.trim(),
              price: productData?.itemPriceData || 0,
              compare_at_price: productData?.itemComparedAtPriceData || 0,
              // price: productData.optionalFields.retailSellingPrice,
              sku: productData.sku,
              inventory_management: "shopify",
              inventory_policy: "continue",
              metafields: [
                {
                  namespace: NAMESPACE,
                  key: "countryoforigin",
                  value: productData.optionalFields.countryOfOrigin,
                },
              ],
            },
          ],
        },
      },
      {
        headers: {
          "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data?.errors) {
      throw new Error(
        `Create product request failed  for product:${productData?.title},variant sku:${productData.sku}.Error:${response.data.errors?.product}`
      );
    }

    const newProduct = response.data.product;

    const quantityAvailable = productData.quantityAvailable;

    const inventoryQuantity = productData.quantityOnHand;

    // Add inventory for each variant in the product

    const inventoryItemId = newProduct.variants[0].inventory_item_id;

    const inventoryResponse = await setInventoryLevel(
      inventoryItemId,
      productData.itemLocationId,
      inventoryQuantity
    );
    const newProductMapEntry = new ProductMap({
      segment1: productData?.segment1,
      productId: newProduct?.id,
      variants: newProduct?.variants.map((variant) => ({
        sku: variant?.sku,
        variantId: variant?.id,
        location: productData?.itemLocationId,
        locationName: productData?.itemLocation,
      })),
    });
    await newProductMapEntry.save();

    console.log(`Product created with ID: ${newProduct.id}`);
  } catch (error) {
    console.error("Error creating product:", error.message);
    throw error;
  }
};

const addOrUpdateVariant = async (productId, variantData, existingVariants) => {
  try {
    const existingVariant = existingVariants.find(
      (v) => v.sku === variantData.sku
    );

    if (existingVariant) {
      // Update the variant if SKU already exists
      console.log(
        `Updating existing variant with ID: ${existingVariant.variantId}`
      );
      await updateVariant(productId, existingVariant.variantId, variantData);
    } else {
      // Create a new variant if SKU does not exist
      await createNewVariant(productId, variantData);
    }
  } catch (error) {
    console.log("Error when adding Or Updating Variant", error?.message);
    throw error;
  }
};

const updateVariant = async (productId, variantId, updatedData) => {
  try {
    const variantUpdateResponse = await axios.put(
      `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/variants/${variantId}.json`,
      {
        variant: {
          id: variantId,
          price: updatedData?.itemPriceData || 0,
          compare_at_price: updatedData?.itemComparedAtPriceData || 0,
          // price: updatedData?.optionalFields?.retailSellingPrice,
          inventory_policy: "continue",
          metafields: [
            {
              namespace: NAMESPACE,
              key: "countryoforigin",
              value: updatedData.optionalFields.countryOfOrigin,
            },
            {
              namespace: NAMESPACE,
              key: "compare_at_price",
              value: String(updatedData?.itemComparedAtPriceData || 0),
            },
          ],
        },
      },
      {
        headers: {
          "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
          "Content-Type": "application/json",
        },
      }
    );

    if (variantUpdateResponse?.data?.errors) {
      throw new Error(
        `Update variant request failed  for productId :${productId}, variantId:${variantId}.Error:${response.data.errors?.variant}`
      );
    }

    const updatedVariant = variantUpdateResponse.data.variant;

    console.log(`Variant updated with ID: ${updatedVariant?.id}`);

    console.log(
      updatedData?.itemLocationId,
      "locationId",
      updatedVariant.inventory_item_id
    );

    const inventoryItemId = updatedVariant.inventory_item_id; // Retrieve inventory_item_id from REST API response
    const inventoryQuantity = updatedData.quantityOnHand;

    await setInventoryLevelOnHandGraphQL(
      inventoryItemId,
      updatedData?.itemLocationId,
      inventoryQuantity
    );
    console.log(`Inventory updated for variant ID: ${updatedVariant.id}`);

    return updatedVariant;
  } catch (error) {
    console.log("Error updating variant and inventory:", error.message);
    throw error;
  }
};

const createNewVariant = async (productId, variantData) => {
  try {
    console.log(`Adding new variant for SKU: ${variantData?.sku}`);

    const response = await axios.post(
      `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/products/${productId}/variants.json`,
      {
        variant: {
          option1: (variantData.color?.trim() === "ZZZZ" || !variantData.color?.trim()) ? "NA" : variantData.color?.trim(),
          option2: (variantData.size?.trim() === "ZZZZ" || !variantData.size?.trim()) ? "NA" : variantData.size?.trim(),
          sku: variantData?.sku,
          price: variantData?.itemPriceData || 0,
          compare_at_price: variantData?.itemComparedAtPriceData || 0,
          inventory_policy: "continue",
          metafields: [
            {
              namespace: NAMESPACE,
              key: "countryoforigin",
              value: variantData.optionalFields.countryOfOrigin,
            },
            {
              namespace: NAMESPACE,
              key: "compare_at_price",
              value: String(variantData?.itemComparedAtPriceData || 0),
            },
          ],
        },
      },
      {
        headers: {
          "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
          "Content-Type": "application/json",
        },
      }
    );

    if (response?.data?.errors) {
      throw new Error(
        `Create new variant request failed  for productId :${productId}, for ERP itemNumber:${variantData.sku}.Error:${response?.data?.errors?.variant}`
      );
    }

    const newVariant = response.data.variant;
    console.log(`New variant created with ID: ${newVariant?.id}`);

    console.log(
      variantData.itemLocationId,
      "locationId",
      newVariant.inventory_item_id
    );

    const inventoryQuantity = variantData?.quantityOnHand;
    const inventoryResponse = await setInventoryLevel(
      newVariant.inventory_item_id,
      variantData.itemLocationId,
      inventoryQuantity
    );

    // Update the ProductMap with the new SKU and variantId
    await ProductMap.updateOne(
      { productId },
      {
        $push: {
          variants: {
            sku: newVariant.sku,

            variantId: newVariant.id,
            location: variantData?.itemLocationId,
            locationName: variantData?.itemLocation,
          },
        },
      }
    );
  } catch (error) {
    console.log("Error in createNewVariant:", error?.message);
    throw Error(`Error in createNewVariant.Message:${error?.message}`);
  }
};

const saveFailedProduct = async (
  segment1,
  sku,
  event,
  data = {},
  errorMessage
) => {
  try {
    const failedProduct = new FailedProductLog({
      segment1,
      sku,
      event,
      data,
      errorMessage,
    });
    await failedProduct.save();
    console.log(`Saved failed product with segment1: ${segment1}`);
  } catch (error) {
    throw error;
    console.error(`Error saving failed product: ${error.message}`);
  }
};

const getOptionalFields = (optionalFields) => {
  const retailSellingPrice = optionalFields.find(
    (field) => field.OptionalField === "RSP"
  )?.Value;
  const countryOfOrigin = optionalFields.find(
    (field) => field.OptionalField === "COORIGIN"
  )?.Value;

  const budgetCategory = optionalFields.find(
    (field) => field.OptionalField === "BUDSUBGRP"
  )?.Value;

  const response = { retailSellingPrice, countryOfOrigin, budgetCategory };
  return response;
};

const setInventoryLevel = async (
  inventoryItemId,
  locationId,
  availableQuantity
) => {
  try {
    const response = await axios.post(
      `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/inventory_levels/set.json`,
      {
        location_id: locationId,
        inventory_item_id: inventoryItemId,
        available: availableQuantity,
      },
      {
        headers: {
          "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
          "Content-Type": "application/json",
        },
      }
    );
    console.log(
      `Inventory set to ${availableQuantity} at location ${locationId} for inventoryitemId: ${inventoryItemId}`
    );
    return true;
  } catch (error) {
    console.error("Error setting inventory level:", error?.message);
    throw Error(`Error setting inventory level:${error?.message}`);
  }
};

const setInventoryLevelOnHandGraphQL = async (
  inventoryItemId,
  locationId,
  onHandQuantity
) => {
  console.log(
    inventoryItemId,
    locationId,
    onHandQuantity,
    "inventoryItemId,locationId,onHandQuantity"
  );
  try {
    const gidInventoryItemId = `gid://shopify/InventoryItem/${inventoryItemId}`;
    const gidLocationId = `gid://shopify/Location/${locationId}`;

    const variables = {
      input: {
        name: "on_hand",
        reason: "correction",
        ignoreCompareQuantity: true,
        quantities: [
          {
            inventoryItemId: gidInventoryItemId,
            locationId: gidLocationId,
            quantity: onHandQuantity,
          },
        ],
      },
    };

    const response = await fetchGraphqlDataShopify(
      INVENTORY_SET_QUANTITIES,
      variables
    );
    if (response.error) {
      throw Error(
        `Error when creating  inventory for shopify locationId:${locationId}.Message:${response.error.response.data.errors[0].message}.Event:updating item.`
      );
    }

    console.log(
      `Inventory set for inventory item id:${gidInventoryItemId}.Quantity:${onHandQuantity}`
    );
    return true;
  } catch (error) {
    console.error("Error setting inventory level:", error?.message);
    throw error;
  }
};

const getERPItemsAndLocations = async (manual, initial) => {
  const itemsWithLocations = [];

  let retryCount = 0;
  let skipValue = 0;
  const select =
    "$select=ItemNumber,Location,QuantityAvailableToShip,QuantityOnHand";
  let baseURL = `${process.env.SAGE_API_ENDPOINT}/IC/ICLocationDetails?${select}`;
  let nextLink;
  try {
    while (true) {
      nextLink = `${baseURL}&$skip=${skipValue}`;
      let config = {
        method: "get",
        maxBodyLength: Infinity,
        url: nextLink,
        headers: {
          "Content-Type": `${process.env.API_CONTENT_TYPE}`,
          Accept: `${process.env.API_CONTENT_TYPE}`,
          Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
        },
      };

      let response;

      try {
        // Attempt to fetch data
        // response = initial ? ITEM_LOCATION_DUMMY_INITIAL : ITEM_LOCATION_DUMMY;
        response = await axios.request(config);
      } catch (error) {
        console.error("Batch fetching item locations:", error.message);
        retryCount++;
        await saveFailedProduct(
          null,
          null,
          "getERPItemsAndLocations",
          { manual, nextLink },
          `Location Batch fetch error: ${error.message}.Link:${nextLink}`
        );
        if (retryCount > 1) {
          throw Error(`Unable to fetch Items.Logs Saved.Aborting..`);
        }

        continue;
      }

      const items = response?.data?.value || [];

      console.log(
        `Retrieved ${items.length} items location in the current batch.`
      );

      itemsWithLocations.push(...items);

      const nextLinkURL = response.data["@odata.nextLink"];
      if (!nextLinkURL) {
        console.log("No more data to fetch. Stopping the loop.");
        break;
      }

      const urlParams = new URLSearchParams(nextLinkURL.split("?")[1]);
      skipValue = urlParams.get("$skip") || skipValue;

      retryCount = 0;
    }

    return itemsWithLocations;
  } catch (error) {
    console.error("Error fetching items and locations:", error.message);
    await saveFailedProduct(
      null,
      null,
      "getERPItemsAndLocations",
      { manual, nextLink },
      `General fetch error when fetching items and locations: ${error.message}.`
    );
    return null;
  }
};

const getERPItemPrice = async (
  UnformattedItemNumber,
  CurrencyCode = "MYR",
  PriceListCode
) => {
  try {
    const url = `${process.env.SAGE_API_ENDPOINT}/IC/ICItemPricing(CurrencyCode='${CurrencyCode}',UnformattedItemNumber='${UnformattedItemNumber}',PriceListCode='${PriceListCode}')?$select=CurrencyCode,UnformattedItemNumber,BasePrice,SalePrice`;
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: url,
      headers: {
        "Content-Type": `${process.env.API_CONTENT_TYPE}`,
        Accept: `${process.env.API_CONTENT_TYPE}`,
        Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
      },
    };
    let response;
    try {
      // response = ITEM_PRICE_DUMMY;
      response = await axios.request(config);
    } catch (error) {
      console.error("Error fetching item price:", error.message);
      await saveFailedProduct(
        null,
        null,
        "getERPItemPrice",
        { UnformattedItemNumber },
        `Item Price fetch error for ${UnformattedItemNumber}.Error: ${error.message}`
      );
      return null;
    }

    const itemPriceData = response?.data || "";
    console.log(
      `Retrieved ${UnformattedItemNumber} items price for ${PriceListCode} :${itemPriceData?.BasePrice}.`
    );
    return itemPriceData?.BasePrice || 0;
  } catch (error) {
    await saveFailedProduct(
      null,
      null,
      "getERPItemPrice",
      { UnformattedItemNumber },
      `Error in Fn getERPItemPrice for ${UnformattedItemNumber}.Error: ${error.message}`
    );

    return null;
  }
};

const adjustInitialSyncInventory = async (products, itemsLocation) => {
  try {
    //filter ICItemLocations and find those items which also exists in ICItems and QuantityOnSO > 0
    const matchedItems = itemsLocation.filter(
      (item) =>
        products.some(
          (product) => product?.UnformattedItemNumber === item.ItemNumber
        ) && item.QuantityOnSO > 0
    );

    const itemNumbers = matchedItems.map((item) => item.ItemNumber);

    //ProductMap DB get variants which match the itemNumber
    const matchedVariants = await ProductMap.aggregate([
      { $unwind: "$variants" },

      { $match: { "variants.sku": { $in: itemNumbers } } },

      {
        $project: {
          _id: 0,
          "variants.sku": 1,
          "variants.variantId": 1,
        },
      },
    ]);

    const results = matchedItems.reduce((acc, item) => {
      const matchingVariant = matchedVariants.find(
        (variant) => variant.variants.sku === item.ItemNumber
      );

      if (matchingVariant) {
        acc.push({
          variant_id: matchingVariant.variants.variantId,
          quantity: item.QuantityOnSO,
        });
      }

      return acc;
    }, []);

    if (results.length > 0) {
      const shopifyOrderId = await createInitialOrder(results);
    }
  } catch (error) {
    console.log(error.message, "Error in adjustInitialSyncInventory");

    await saveFailedProduct(
      null,
      null,
      "adjustInitialSyncInventory",
      { manual },
      error.message
    );
    throw error;
  }
};

const createInitialOrder = async (matchedItems) => {
  try {
    let data = JSON.stringify({
      order: {
        line_items: matchedItems,
        inventory_behaviour: "decrement_obeying_policy",
      },
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/2024-10/orders.json`,
      headers: {
        "Content-Type": process.env.API_CONTENT_TYPE,
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      },
      data: data,
    };

    const response = await axios.request(config);
    if (response?.status !== 201) {
      throw Error(`Error while creating initial order.`);
    }
    const orderId = response?.data?.order?.id;
    if (!orderId) {
      throw Error(`Error while creating initial order.No order ID found.`);
    }
    console.log(`Initial order created with id:${response?.data?.order?.id}`);
    return orderId;
  } catch (error) {
    console.log(error.message, "error in createInitialOrder");
    throw error;
  }
};

const handleNewLocation = async (itemsLocation) => {
  try {
    const uniqueLocations = [
      ...new Set(itemsLocation.map((item) => item.Location)),
    ];
    let existingLocation = await getExistingLocationsShopify();
    const newLocations = uniqueLocations.filter(
      (location) =>
        !existingLocation.some(
          (shopifyLocation) => shopifyLocation?.name === location
        )
    );

    if (newLocations.length > 0) {
      for (const location of newLocations) {
        const newLocation = await createLocationOnShopify(location);
        if (newLocation) {
          existingLocation?.push(newLocation);
        }
      }
    }

    return existingLocation;
  } catch (error) {
    await saveFailedProduct(null, null, "handleNewLocation", {}, error.message);
    return null;
  }
};

const getExistingLocationsShopify = async () => {
  try {
    const response = await fetchGraphqlDataShopify(GET_SHOPIFY_LOCATIONS, {});
    if (response.error) {
      throw Error(
        `Error when fetching existing locations.Error:${response.error.response.data.errors}`
      );
    }

    const responseData =
      response.data.locations.edges.map((location) => {
        return { id: location?.node?.id, name: location?.node?.name };
      }) || [];

    //responseData =[{id:"75220123748",name:"1SR"}]
    return responseData;
  } catch (error) {
    console.log(error?.message, `Error when fetching existing locations`);
    throw error;
  }
};

const createLocationOnShopify = async (location) => {
  try {
    const variables = {
      input: { name: location, address: { countryCode: "MY" } },
    };

    const response = await fetchGraphqlDataShopify(
      CREATE_LOCATION_SHOPIFY,
      variables
    );
    if (response.error) {
      throw Error(
        `Error when creating new shopify location name:${location.name}.Message:${response.error.response.data.errors[0].message}`
      );
    }
    const locationId = response.data.locationAdd.location.id;

    return {
      id: locationId,
      name: location,
    };
  } catch (error) {
    //Log
    await saveFailedProduct(
      null,
      null,
      "createLocationOnShopify",
      { manual },
      `Error when creating location.Error:${error.message}`
    );
    console.log(error.message, `Error when creating location`);
    return null;
  }
};

// ============Other Services :Non related to Sync==================

const getERPItemsInventory = async (skus) => {
  try {
    const url = `${process.env.SAGE_API_ENDPOINT}/IC/ICLocationDetails`;

    const selectQuery = `ItemNumber,Location,QuantityOnPO,QuantityOnSO,QuantityAvailableToShip,QuantityOnHand`;

    const filterQuery = skus
      .map((item) => `ItemNumber eq '${item}'`)
      .join(" or ");

    const baseUrl = `${url}?$filter=${filterQuery}&$select=${selectQuery}`;
    let skipValue = 0;
    let allItems = [];
    let nextLink;

    while (true) {
      nextLink = `${baseUrl}&$skip=${skipValue}`;
      const config = {
        method: "get",
        url: nextLink,
        headers: {
          "Content-Type": `${process.env.API_CONTENT_TYPE}`,
          Accept: `${process.env.API_CONTENT_TYPE}`,
          Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
        },
      };
      // const response = ITEM_LOCATION_DUMMY;

      const response = await axios.request(config);

      const responseData = response.data?.value || [];

      if (!responseData.length) {
        break;
      }
      allItems = allItems.concat(responseData);

      const nextLinkURL = response.data["@odata.nextLink"];

      if (!nextLinkURL) {
        console.log("No more data to fetch. Stopping the loop.");
        break;
      }

      // Extract skip value from nextLinkURL
      const urlParams = new URLSearchParams(nextLinkURL.split("?")[1]);
      skipValue = urlParams.get("$skip") || skipValue;
    }

    return {
      status: 200,
      data: { items: allItems },
    };
  } catch (error) {
    console.error("Error:", error.message);
    return {
      status: 500,
      message: `Error fetching itemsInventory from ERP.`,
      code: `SERVER_ERROR`,
    };
  }
};

const chunkArray = (array, size) => {
  return Array.from({ length: Math.ceil(array.length / size) }, (_, index) =>
    array.slice(index * size, index * size + size)
  );
};

const getERPItemSegmentsBatch = async (productBatch) => {
  try {
    const itemNumbers = productBatch.map((item) => item.ItemNumber);
    const batchSize = 25;
    const itemBatches = chunkArray(itemNumbers, batchSize);
    let results = [];

    for (const batch of itemBatches) {
      try {
        const itemParam = encodeURIComponent(
          JSON.stringify(batch.map((Item) => ({ Item })))
        );

        const url = `${
          process.env.SAGE_CUSTOM_API_ENDPOINT
        }/api/ICSegment/GetItemSegment?Item=${itemParam}&company=${encodeURIComponent(
          process.env.SAGE_COMPANY
        )}&APIKey=${encodeURIComponent(process.env.SAGE_CUSTOM_API_KEY)}`;

        const response = await axios.get(url, {
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        });

        const segmentData = JSON.parse(response.data);
        results = results.concat(segmentData);
      } catch (error) {
        continue; // Continue with the next batch even if one fails
      }
    }

    // If all batches fail, return a failure response
    if (results.length === 0) {
      return {
        success: false,
        message: "All batch requests failed. No data retrieved.",
        code: "SERVER_ERROR",
      };
    }

    return {
      success: true,
      data: results,
    };
  } catch (error) {
    return {
      success: false,
      message: "Error fetching item segments from ERP.",
      code: "SERVER_ERROR",
    };
  }
};

export { productScheduledService, saveFailedProduct, getERPItemsInventory };
