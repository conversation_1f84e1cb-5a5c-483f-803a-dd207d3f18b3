class AppError extends Error {
  constructor(payload, statusCode) {
    // If payload is a string, use old behavior
    if (typeof payload === "string") {
      super(payload);
      this.message = payload;
    }
    // If payload is an object, use new structured format
    else {
      super(payload.message);
      this.data = payload.data;
      this.status = payload.status;
    }

    this.responseCode = 1;
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith("4") ? "fail" : "error";
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export default AppError;
