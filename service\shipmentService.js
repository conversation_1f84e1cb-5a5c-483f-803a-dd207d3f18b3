import mongoose from "mongoose";
import NewAppError from "../utils/newAppError.js";
import { getDepartmentNotifiedEmails } from "../utils/findNotifiedDepartment.js";
import Department from "../model/department.model.js";
import Distributor from "../model/distributor.model.js";
import Shipment from "../model/shipment.model.js";
import Order from "../model/order.model.js";
import Status from "../model/status.model.js";
import EmailNotification from "../model/emailNotification.model.js";
import { endEscalationStatus } from "../controller/action.controller.js";
import { createERPOrderController } from "../controller/erp.order.controller.js";
import { getErpItemsAvailableInventoryUtility } from "../utils/ERPUtils/erpItemUtils.js";
import {
  getOrderFulfillmentsService,
  processMatchedERPFulfillmentsforRefund,
  refundLineItemsService,
} from "../service/ERPSync/orderService.js";
import {
  orderMarkAsPaidService,
  getOrderFinancialStatusService,
} from "../service/orderService.js";

export function shipmentUploadValidateService(data) {
  try {
    const requiredKeys = [
      "Title",
      "OrderedQuantity",
      "Sku",
      "AllocatedQuantity",
    ];

    const skuCount = {};
    const titleCount = {};

    data.forEach((lineItem) => {
      skuCount[lineItem.Sku] = (skuCount[lineItem.Sku] || 0) + 1;
      titleCount[lineItem.Title] = (titleCount[lineItem.Title] || 0) + 1;
    });

    for (let index = 0; index < data.length; index++) {
      const lineItem = data[index];
      // Check for missing keys
      const missingKeys = requiredKeys.filter((key) => !(key in lineItem));

      if (missingKeys.length > 0) {
        return {
          valid: false,
          error: `Line item ${index + 1}: Missing Columns.`,
        };
      }

      const emptyValues = Object.entries(lineItem).filter(
        ([key, value]) => value === null || value === "" || value === undefined
      );
      if (emptyValues.length > 0) {
        return {
          valid: false,
          error: `Line item ${index + 1}: Empty Entry Found.`,
        };
      }

      // Check for duplicate Sku
      if (skuCount[lineItem.Sku] > 1) {
        return {
          valid: false,
          error: `Line item ${index + 1}: Duplicate Sku found.`,
        };
      }
    }

    return { valid: true };
  } catch (error) {
    return {
      valid: false,
      error: `Error while validiting Upload.`,
    };
  }
}

export function compareShipmentItems(requestedItems, existingItems) {
  try {
    const existingItemsMap = new Map();
    existingItems.forEach(function (doc) {
      existingItemsMap.set(doc.sku, doc);
    });

    const updatedShipment = [];
    const remainingItemsShipment = [];

    let totalQuantity = 0;

    requestedItems.forEach((reqItem) => {
      const existingItem = existingItemsMap.get(reqItem.Sku);

      totalQuantity += reqItem.AllocatedQuantity;
      if (!existingItem) {
        return;
      }
      const decrement = existingItem.requested - reqItem.AllocatedQuantity;

      if (decrement === 0) {
        updatedShipment.push({
          ...existingItem,
          requested: reqItem.AllocatedQuantity,
          fulfilled: reqItem.AllocatedQuantity,
        });
      } else {
        updatedShipment.push({
          ...existingItem,
          requested: reqItem.AllocatedQuantity,
          fulfilled: reqItem.AllocatedQuantity,
        });
        remainingItemsShipment.push({
          ...existingItem,
          requested: decrement,
          fulfilled: 0,
        });
      }
    });

    if (totalQuantity === 0) {
      return new NewAppError(
        "BAD_REQUEST",
        "Enter valid Quantity, at least 1 sku should have quantity greater than 0.",
        400
      );
    }

    return {
      updatedShipment: updatedShipment,
      remainingItemsShipment: remainingItemsShipment,
    };
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error while processing(comparing) uploaded shipment.`,
      500
    );
  }
}

export async function shipmentEmailNotification(
  order,
  updatedShipment,
  createdShipment,
  current_user,
  nextStatus,
  currentStatus
) {
  try {
    const distributor = await Distributor.findById(order.distributor);
    let salesPerson = await Department.findById(distributor?.salespersonId);
    if (salesPerson) {
      if (createdShipment) {
        const recipientsNonAligned = [
          {
            name: salesPerson.name,
            email: salesPerson.email,
            cc: [],
          },
        ];

        await EmailNotification.create({
          emailCategory: "SHIPMENT",
          emailType: "SHIPMENT_CREATE",
          reciepient: recipientsNonAligned,
          emailPayload: {
            orderName: order.name,
            distributorName: distributor.name,
            date: order.createdAt,
            currentStatus: "",
            nextStatus: nextStatus.status,
            shipmentName: createdShipment.name,
            shipmentId: createdShipment._id,
            shipmentStatus: createdShipment.status,
            shipmetRef: createdShipment.name,
            cartProducts: createdShipment.lineItems,
            actionByUser: current_user.name,
          },
        });
      }

      if (updatedShipment) {
        const recipientsAligned = [
          {
            name: distributor.name,
            email: distributor.email,
            cc: salesPerson.email ? [salesPerson.email] : [],
          },
        ];

        await EmailNotification.create({
          emailCategory: "SHIPMENT",
          emailType: "SHIPMENT_STATUS_CHANGE",
          reciepient: recipientsAligned,
          emailPayload: {
            orderName: order.name,
            distributorName: distributor.name,
            date: order.createdAt,
            shipmentName: updatedShipment.name,
            shipmentStatus: currentStatus.status,
            shipmetRef: updatedShipment.ref,
            currentStatus: currentStatus.status,
            nextStatus: nextStatus.status,
            cartProducts: updatedShipment.lineItems,
            actionByUser: current_user.name,
          },
        });
      }
    }
  } catch (error) {
    console.log(error);
    return new NewAppError(
      "SERVER_ERROR",
      "Error while notifying departments.",
      500
    );
  }
}

export async function processShipmentService(
  existingShipment,
  current_user,
  nextStatus,
  currentStatus,
  statusToAlotOnOrder
) {
  try {
    //Reference:Awaiting Courier Partial.Check as only non aligned shipment can split again.
    //hardcoded status id for shipment not aligned,needs to be dynamic.
    if (
      existingShipment.status._id.toString() !== currentStatus._id.toString()
    ) {
      return new NewAppError(
        "CONFILCT",
        `Already processed or cancelled.`,
        409
      );
    }
    const shipmentId = existingShipment._id.toString();
    const shipmentLineSkus = existingShipment.lineItems.map((x) => x.sku);
    let createdShipment;
    let updatedShipment;
    //Items inventory from ERP ,in case of Auto allocation.
    let shipmentLineItemsInventoryAvailable =
      await getErpItemsAvailableInventoryUtility(shipmentLineSkus);

    if (shipmentLineItemsInventoryAvailable instanceof NewAppError) {
      return shipmentLineItemsInventoryAvailable;
    }
    const existingInventory = shipmentLineItemsInventoryAvailable.data.items;

    const comparison = compareShipmentItemsAutoAllocationService(
      existingInventory,
      existingShipment.lineItems
    );

    if (comparison instanceof NewAppError) {
      return comparison;
    }

    //for naming after process.
    const allShipments = await Shipment.find({
      order: existingShipment.order,
    });
    const order = await Order.findOne({ _id: existingShipment.order });

    if (!order || allShipments.length <= 0) {
      return new NewAppError("MISSING", "Missing required data.", 404);
    }

    const length = allShipments.length;
    let splitShipmentIds = [];

    if (comparison.updatedShipment.length > 0) {
      const updatedShipmentAmountTotal = comparison.updatedShipment.reduce(
        (acc, lineItem) => {
          let itemTotal = (lineItem.fulfilled || 0) * (lineItem.price || 0);
          return acc + itemTotal;
        },
        0
      );

      updatedShipment = await Shipment.findByIdAndUpdate(
        shipmentId,
        {
          $set: {
            lineItems: comparison.updatedShipment.filter((x) => x.requested),
            status: nextStatus._id,
            amount: updatedShipmentAmountTotal,
          },
          $push: {
            timeline: {
              time: new Date(),
              comment: `Shipment has been edited by ${current_user.name}`,
            },
            status_change_history: {
              status: nextStatus._id,
            },
          },
        },
        { new: true }
      );

      const createShipmentOnERP = await createERPOrderController(shipmentId);

      if (createShipmentOnERP instanceof NewAppError) {
        return createShipmentOnERP;
      }

      const addErpIdentifier = await addErpIdentifierShipmentDbService(
        shipmentId,
        createShipmentOnERP
      );
      if (addErpIdentifier instanceof NewAppError) {
        return addErpIdentifier;
      }
      splitShipmentIds.push({ updatedShipmentId: shipmentId });
    }

    if (comparison.remainingShipment.length > 0) {
      const amountTotal = comparison.remainingShipment.reduce(
        (acc, lineItem) => {
          let itemTotal = (lineItem.fulfilled || 0) * (lineItem.price || 0);
          return acc + itemTotal;
        },
        0
      );
      const nonAligned = {
        name: order.name + `_${length + 1}`,
        order: order._id,
        distributor: order.distributor,
        status: currentStatus._id,
        amount: amountTotal,
        initial_status: "non-aligned",
        lineItems: comparison.remainingShipment,
        timeline: [
          {
            time: new Date(),
            comment: `Non-Aligned shipment created from Shipment:${existingShipment.name}`,
          },
        ],
      };

      createdShipment = await new Shipment(nonAligned).save();
      const createdShipmentId = createdShipment._id.toString();
      const updateorder = await Order.findByIdAndUpdate(
        existingShipment.order,
        {
          $push: {
            timeline: {
              time: new Date(),
              comment: `Non-Aligned shipment:${nonAligned.name} created from Shipment:${existingShipment.name}`,
            },
            status_change_history: {
              status: statusToAlotOnOrder._id,
            },
          },
        }
      );
      splitShipmentIds.push({ createdShipmentId });
    }

    if (existingShipment.escalation_status) {
      await endEscalationStatus(existingShipment);
    }

    const notify = await shipmentEmailNotification(
      order,
      updatedShipment,
      createdShipment,
      current_user,
      nextStatus,
      currentStatus
    );

    if (notify instanceof NewAppError) {
      return notify;
    }

    return { processedData: splitShipmentIds };
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `error while processing shipments.${
        error?.message ? `Message:${error.message}` : ""
      }`,
      500
    );
  }
}

export async function processBulkShipmentsService(
  existingShipment,
  current_user
) {
  try {
    //Reference:Awaiting Courier Partial.Check as only non aligned shipment can split again.
    //hardcoded status id for shipment not aligned,needs to be dynamic.
    if (existingShipment.status._id.toString() !== "673afbb0a7f9a80fac8a4944") {
      return new NewAppError(
        "CONFILCT",
        `Already processed or cancelled.`,
        409
      );
    }
    const shipmentId = existingShipment._id.toString();
    const shipmentLineSkus = existingShipment.lineItems.map((x) => x.sku);
    let createdShipment;
    let updatedShipment;

    //Items inventory from ERP ,in case of Auto allocation.
    let shipmentLineItemsInventoryAvailable =
      await getErpItemsAvailableInventoryUtility(shipmentLineSkus);
    if (shipmentLineItemsInventoryAvailable instanceof NewAppError) {
      return shipmentLineItemsInventoryAvailable;
    }
    const existingInventory = shipmentLineItemsInventoryAvailable.data.items;

    const comparison = compareShipmentItemsAutoAllocationService(
      existingInventory,
      existingShipment.lineItems
    );

    if (comparison instanceof NewAppError) {
      return comparison;
    }

    //for naming after process.
    const allShipments = await Shipment.find({
      order: existingShipment.order,
    });
    const order = await Order.findOne({ _id: existingShipment.order });

    if (!order || allShipments.length <= 0) {
      return new NewAppError("MISSING", "Missing required data.", 404);
    }

    //Reference:Awaiting Courier Full.
    //Shipment is allocated inventory.
    const statusToAlotAligned = await Status.findOne({
      _id: new mongoose.Types.ObjectId("673afb29907394a7758ae393"),
    });

    //Reference:Awaiting Courier Partial.
    //Shipment  not yet allocated inventory.
    const statusToAlotNonAligned = await Status.findOne({
      _id: new mongoose.Types.ObjectId("673afbb0a7f9a80fac8a4944"),
    });

    //Reference InProgress
    //New Order status after shipment split.
    const statusToAlotOnOrder = await Status.findOne({
      _id: new mongoose.Types.ObjectId("674e8bf7ce77438d64f79c8f"),
    });

    const length = allShipments.length;
    let splitShipmentIds = [];

    if (comparison.updatedShipment.length > 0) {
      const updatedShipmentAmountTotal = comparison.updatedShipment.reduce(
        (acc, lineItem) => {
          let itemTotal = (lineItem.fulfilled || 0) * (lineItem.price || 0);
          return acc + itemTotal;
        },
        0
      );

      updatedShipment = await Shipment.findByIdAndUpdate(
        shipmentId,
        {
          $set: {
            lineItems: comparison.updatedShipment.filter((x) => x.requested),
            status: statusToAlotAligned._id,
            amount: updatedShipmentAmountTotal,
          },
          $push: {
            timeline: {
              time: new Date(),
              comment: `Shipment has been edited by ${current_user.name}`,
            },
            status_change_history: {
              status: statusToAlotAligned._id,
            },
          },
        },
        { new: true }
      );

      const createShipmentOnERP = await createERPOrderController(shipmentId);

      if (createShipmentOnERP instanceof NewAppError) {
        return createShipmentOnERP;
      }

      const addErpIdentifier = await addErpIdentifierShipmentDbService(
        shipmentId,
        createShipmentOnERP
      );
      if (addErpIdentifier instanceof NewAppError) {
        return addErpIdentifier;
      }

      splitShipmentIds.push({ updatedShipmentId: shipmentId });
    }
    if (comparison.remainingShipment.length > 0) {
      const amountTotal = comparison.remainingShipment.reduce(
        (acc, lineItem) => {
          let itemTotal = (lineItem.fulfilled || 0) * (lineItem.price || 0);
          return acc + itemTotal;
        },
        0
      );
      const nonAligned = {
        name: order.name + `_${length + 1}`,
        order: order._id,
        distributor: order.distributor,
        status: statusToAlotNonAligned._id,
        amount: amountTotal,
        initial_status: "non-aligned",
        lineItems: comparison.remainingShipment,
        timeline: [
          {
            time: new Date(),
            comment: `Non-Aligned shipment created from Shipment:${existingShipment.name}`,
          },
        ],
      };

      createdShipment = await new Shipment(nonAligned).save();
      const createdShipmentId = createdShipment._id.toString();
      const updateorder = await Order.findByIdAndUpdate(
        existingShipment.order,
        {
          $push: {
            timeline: {
              time: new Date(),
              comment: `Non-Aligned shipment:${nonAligned.name} created from Shipment:${existingShipment.name}`,
            },
            status_change_history: {
              status: statusToAlotOnOrder._id,
            },
          },
        }
      );
      splitShipmentIds.push({ createdShipmentId });
    }

    if (existingShipment.escalation_status) {
      await endEscalationStatus(existingShipment);
    }

    const notify = await shipmentEmailNotification(
      order,
      updatedShipment,
      createdShipment,
      current_user,
      statusToAlotAligned,
      statusToAlotNonAligned
    );

    if (notify instanceof NewAppError) {
      return notify;
    }

    return { processedData: splitShipmentIds };
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `error while processing shipments.${
        error?.message ? `Message:${error.message}` : ""
      }`,
      500
    );
  }
}

export function compareShipmentItemsAutoAllocationService(
  requestedItems,
  existingItems
) {
  //reuqestedItems is ERP inventory,existing items is existing shipment that is being processed.

  try {
    const existingItemsMap = new Map();
    existingItems.forEach((item) => {
      existingItemsMap.set(item.sku, item);
    });

    const updatedShipment = [];
    const remainingItemsShipment = [];
    let noAvailableQuantity = false;

    requestedItems.forEach((reqItem) => {
      const existingItem = existingItemsMap.get(reqItem.sku);

      if (reqItem.quantity >= existingItem.requested) {
        //can be fulfilled.
        updatedShipment.push({
          ...existingItem,
          requested: existingItem.requested,
          fulfilled: existingItem.requested,
        });
      } else if (reqItem.quantity > 0) {
        //cannot be fulfilled.
        const remainingItems = existingItem.requested - reqItem.quantity;
        updatedShipment.push({
          ...existingItem,
          requested: reqItem.quantity,
          fulfilled: reqItem.quantity,
        });
        remainingItemsShipment.push({
          ...existingItem,
          requested: remainingItems,
          fulfilled: 0,
        });
      } else {
        //zero quantity for the item in ERP.
        noAvailableQuantity = true;
        remainingItemsShipment.push({
          ...existingItem,
          requested: existingItem.requested,
          fulfilled: 0,
        });
      }
    });

    if (updatedShipment.length <= 0 && noAvailableQuantity) {
      return new NewAppError(
        "UNAVAILABLE",
        `Zero available inventory for lineItems in ERP for given shipment.`,
        409
      );
    }

    return {
      updatedShipment,
      remainingShipment: remainingItemsShipment,
    };
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error while processing(comparing) shipment.`,
      500
    );
  }
}

export async function cancelPendingShipmentService(shipment, nextStatus) {
  try {
    const dbOrder = await Order.findById(shipment.order).select("order_id");
    if (!dbOrder) {
      return new NewAppError(
        "ORDER_NOT_FOUND",
        `No order found for provided shipment.`,
        404
      );
    }
    const shopifyOrderId = dbOrder.order_id;
    if (!shopifyOrderId) {
      return new NewAppError(
        "MISSING",
        `No shopify order found for provided shipment.`,
        404
      );
    }

    //getorderFinantialStatus if not paid set to mark as paid.
    const orderFinancialStatus = await getOrderFinancialStatusService(
      shopifyOrderId
    );
    if (orderFinancialStatus instanceof NewAppError) {
      return orderFinancialStatus;
    }
    if (orderFinancialStatus.orderFinancialStatus !== "PAID") {
      //marking as paid as order was created with pending payment,to refund items we need paid items
      const markOrderAsPaid = await orderMarkAsPaidService(shopifyOrderId);
      if (markOrderAsPaid instanceof NewAppError) {
        return markOrderAsPaid;
      }
    }

    const getOrderFulfillments = await getOrderFulfillmentsService(
      shopifyOrderId
    );
    if (!getOrderFulfillments) {
      return new NewAppError(
        "FULFILLMENT_FETCH_ERROR",
        `Could not process shipment.Error processing order fulfillment data.`,
        503
      );
    }
    const processedFulfillment = await processMatchedERPFulfillmentsforRefund(
      getOrderFulfillments,
      shipment,
      "cancelUnFulfilled"
    );
    if (!processedFulfillment) {
      return new NewAppError(
        "PROCESSING_ERROR",
        `Error while processing shipment.`,
        500
      );
    }

    if (processedFulfillment.length <= 0) {
      return new NewAppError(
        "NO_LINE_ITEMS",
        `Could not map lineItems while processing.`,
        422
      );
    }
    let refundIds = [];
    const batchSize = 250; // Max items per request
    const totalbatches = Math.ceil(processedFulfillment.length / batchSize);

    for (let i = 0; i < totalbatches; i++) {
      const batch = processedFulfillment.slice(
        i * batchSize,
        (i + 1) * batchSize
      );

      for (const fulfillment of batch) {
        const { orderId, refundLineItems } = fulfillment;

        const refundResponse = await refundLineItemsService(
          orderId,
          refundLineItems
        );

        if (refundResponse.refunded) {
          refundIds.push(refundResponse.data.refundId);
        }
      }
    }
    if (refundIds.length <= 0) {
      return new NewAppError(
        "CANCEL_ERROR",
        `No items were cancelled for this shipment.`,
        422
      );
    }

    // Updated status after all refunds are processed
    const updatedShipment = await Shipment.findByIdAndUpdate(
      shipment._id.toString(),
      { status: nextStatus },
      { new: true }
    );

    return {
      shipment_id: shipment._id.toString(),
      refundIds,
    };
  } catch (error) {
    console.log("errror-------------", error.message);
    return new NewAppError(
      "SERVER_ERROR",
      `An unexpected error occurred while cancelling the shipment.`,
      500
    );
  }
}

export async function addErpIdentifierShipmentDbService(
  shipmentId,
  createShipmentOnERP
) {
  try {
    const addErpIdentifier = await Shipment.findByIdAndUpdate(
      shipmentId,
      {
        $set: {
          erpIdentifier: Number(createShipmentOnERP.data.order.OrderUniquifier),
        },
      },
      { new: true }
    );
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error while processing Shipment.${error.message}`,
      500
    );
  }
}
