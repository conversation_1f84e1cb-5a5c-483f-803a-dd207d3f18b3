import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import {
  CHECK_CUSTOMER_EXISTENCE_BY_ID,
  GET_COMPANY_BY_ID,
} from "./graphqlApis.js";
import {
  metafieldKeysWithSteps,
  stepRequirements,
} from "../helpers/companyMetaHelper.js";
/**
 * @param {express.Request} req - Express request object
 * @param {express.Response} res - Express response object
 */

export async function ProcessGetCompanyDetailsFromEmailMeta(req, res) {
  try {
    const email = req.query.email;

    // Validate that email is provided
    if (!email) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Email parameter is required.",
          },
        ],
      });
    }

    // Fetch customer by email
    const customer = await GetCustomerCompanyContactProfileByCustId(email);

    if (!customer) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "INTERNAL_SERVER_ERROR",
            message: `Something went wrong while finding Customer with email ${email}`,
          },
        ],
      });
    }
    if (!customer.customer || !customer.customerId) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "NOT_FOUND",
            message: `Customer with email ${email} does not exist.`,
          },
        ],
      });
    }
    if (!customer.companyId) {
      return res.status(200).json({
        responseCode: 0,
        status: "success",
        data: {
          message: "No company found, create a new one.",
        },
      });
    }

    // Fetch company details by company ID
    const companyDetails = await GetCompanyDetailsById(
      customer.companyId,
      email
    );

    if (!companyDetails) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "NOT_FOUND",
            message: `No company details found for the associated company ID ${customer.companyId}.`,
          },
        ],
      });
    }
    if (!companyDetails.companyFound) {
      return res.status(409).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "MIS_MATCH",
            message: `Email mismatch for the company found. \nRequest Email : ${email}\nCompany Associated Email : ${companyDetails.company.email}`,
            data: companyDetails,
          },
        ],
      });
    }

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: companyDetails.company,
    });
  } catch (error) {
    console.error(`Error in GetCompanyDetailsFromEmail: ${error.message}`);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: `An unexpected error occurred: ${error.message}`,
        },
      ],
    });
  }
}

export async function GetCompanyDetailsById(companyId, email) {
  try {
    const response = await fetchGraphqlDataShopify(GET_COMPANY_BY_ID, {
      id: companyId,
    });

    if (!response) return null;

    const company = response?.data?.company;
    if (company) {
      const currentStep = company?.metafields?.edges?.find(
        (edge) => edge?.node?.key === "currentStep"
      )?.node?.value;
      const metafields = CreateMetaFromCompany(company?.metafields?.edges);
      const firstName =
        company?.contacts?.edges[0]?.node?.customer?.firstName || "";
      const lastName =
        company?.contacts?.edges[0]?.node?.customer?.lastName || "";
      const individualName = combineName(firstName, lastName);
      const contactNumber =
        company?.contacts?.edges[0]?.node?.customer?.phone || "";
      const customCompany = {
        currentStep: currentStep ? currentStep : "1",
        id: company?.id,
        companyName: company?.name,
        individualName: individualName,
        email: company?.contacts?.edges[0]?.node?.customer?.email || "",
        contactNumber: contactNumber,
        details: metafields,
      };

      if (customCompany.email !== email) {
        return {
          companyFound: false,
          company: customCompany,
        };
      } else {
        return {
          companyFound: true,
          company: customCompany,
        };
      }
    }
    return null;
  } catch (error) {
    console.log("GetCompanyDetailsById Error : ", error);
    throw error;
  }
}

function CreateMetaFromCompany(metafields) {
  try {
    const customMetafields = metafields.reduce((acc, edge) => {
      acc[edge?.node?.key] = edge?.node?.value;
      return acc;
    }, {});
    const stepwiseMetafields = {};
    for (const [step, keys] of Object.entries(metafieldKeysWithSteps)) {
      stepwiseMetafields[step] = {};
      keys.forEach((key) => {
        stepwiseMetafields[step][key] = customMetafields[key] || null;
      });
    }
    return stepwiseMetafields;
  } catch (error) {
    console.log("CreateMetaFromCompany Error : ", error);
    throw error;
  }
}

// function splitName(completeName) {
//   const [firstName, ...rest] = completeName.trim().split(" ");
//   const lastName = rest.join(" ");

//   return {
//     firstName: firstName || "",
//     lastName: lastName || "",
//   };
// }

// function combineName(firstName, lastName) {
//   return `${firstName} ${lastName}`.trim();
// }

export async function ProcessUpdateCompanyDetailsMeta(req, res) {
  try {
    const { email, individualName, companyName, contactNumber, currentStep } =
      req.body;

    let details = req.body;

    const {
      tin,
      brn,
      nricNumber,
      obrn,
      sst,
      businessAddress,
      businessDescription,
      msicCodes,
      entityType,
      form9,
      form24,
      form49,
      memArticle,
      icCard,
      lan,
      bankStatement,
      formD,
      form1,
    } = details;

    if (
      !email ||
      !individualName ||
      !companyName ||
      !contactNumber ||
      !currentStep
    ) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message:
              "[Email, Individual Name, Company Name, Contact Number, Current Step] parameters are required.",
          },
        ],
      });
    }

    const validSteps = [1, 2, 3, 4];
    if (!validSteps.includes(parseInt(currentStep, 10))) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: "Invalid currentStep. It must be a value between 1 and 4.",
          },
        ],
      });
    }

    const requestedCurrentStep = parseInt(currentStep, 10);
    // Validate that email is provided

    const { firstName, lastName } = splitName(individualName);
    // Fetch customer by email
    const customer = await GetCustomerCompanyContactProfileByCustId(email);

    if (!customer) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "INTERNAL_SERVER_ERROR",
            message: `Something went wrong while finding Customer with email ${email}`,
          },
        ],
      });
    }
    if (!customer.customer || !customer.customerId) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "NOT_FOUND",
            message: `Customer with email ${email} does not exist.`,
          },
        ],
      });
    }
    if (!customer.companyId) {
      if (requestedCurrentStep === 1) {
        const { valid, missingFields } = ValidateStepFields(
          requestedCurrentStep,
          req.body
        );

        if (!valid) {
          return res.status(400).json({
            responseCode: 1,
            status: "error",
            errors: [
              {
                code: "VALIDATION_ERROR",
                message: `Missing mandatory fields for step ${requestedCurrentStep}: ${missingFields.join(
                  ", "
                )}`,
              },
            ],
          });
        }

        console.log(
          "Create Company by name, Update Customer details, Link Company to Customer, Assign main contact"
        );

        // const success = await ProcessCreateCompany();

        return res.status(200).json({
          responseCode: 0,
          status: "success",
          data: {
            message: "Step 1 Data saved successfully. Company created",
          },
        });
      } else {
        return res.status(400).json({
          responseCode: 1,
          status: "error",
          errors: [
            {
              code: "BAD_REQUEST",
              message: `No Company found and Current Step mismath for Processing`,
            },
          ],
        });
      }
    }
    // Fetch company details by company ID
    const companyDetails = await GetCompanyDetailsById(
      customer.companyId,
      email
    );
    if (!companyDetails) {
      return res.status(404).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "NOT_FOUND",
            message: `No company details found for the associated company ID ${customer.companyId}.`,
          },
        ],
      });
    }
    if (!companyDetails.companyFound) {
      return res.status(409).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "MIS_MATCH",
            message: `Email mismatch for the company found. \nRequest Email : ${email}\nCompany Associated Email : ${companyDetails.company.email}`,
            data: companyDetails,
          },
        ],
      });
    }

    //Now Check if the meta current step is greater than the request current step OR lets take an example.
    //Step validation shall be there, like if customer is on 2nd step, then API shall not accept 4th or 3rd step details.
    // If customer in on step x, then all steps lower than x shall be editable.
    // If meta currnt step is 1 and request current step is 3, then it should give an error and not allow the update.
    // Also if meta current step is 1 and requested current step is 2, then it should allow the update because it should have a gap of 1 only.
    // Next if meta current step is 3 and requested current step is 1 or 2, then also it should be able to update the company

    // If conditions satisfy, then check the requested current step and check for the mandatory input properties in the request body for the respective current step.

    console.log("companyDetails.company : ", companyDetails.company);
    // Validate step progression
    const companyCurrentStep = parseInt(
      companyDetails.company.currentStep || "0",
      10
    );

    // Allow only if updating lower steps or next immediate step
    if (requestedCurrentStep > companyCurrentStep + 1) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: `Invalid step update. You can only proceed to the next step or update previous steps. Current step: ${companyCurrentStep}, Requested step: ${requestedCurrentStep}`,
          },
        ],
      });
    }

    // Validate mandatory fields for the requested step
    const { valid, missingFields } = ValidateStepFields(
      requestedCurrentStep,
      req.body
    );

    if (!valid) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: `Missing mandatory fields for step ${requestedCurrentStep}: ${missingFields.join(
              ", "
            )}`,
          },
        ],
      });
    }

    // Proceed with update logic for the company based on currentStep
    // Here you can process the update or save the information accordingly.

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        message: `Company details for step ${requestedCurrentStep} updated successfully.`,
      },
    });
  } catch (error) {
    console.error(`Error in GetCompanyDetailsFromEmail: ${error.message}`);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: `An unexpected error occurred: ${error.message}`,
        },
      ],
    });
  }
}

function ValidateStepFields(step, body) {
  const stepConfig = stepRequirements[`step${step}`];

  // Ensure that step exists in the config
  if (!stepConfig) {
    return {
      valid: false,
      missingFields: [`Step ${step} configuration is missing`],
    };
  }

  // Validate mandatory fields for the step itself
  const { mandatory } = stepConfig;
  const missingFields = mandatory.filter((field) => !body[field]);

  if (missingFields.length > 0) {
    return {
      valid: false,
      missingFields,
    };
  }

  // Special handling for step 3 based on entity type
  if (step === 3) {
    const entityType = body.entityType;

    // Check if the entity type is valid
    if (
      !["Limited Liability Company", "Sole Proprietor"].includes(entityType)
    ) {
      return {
        valid: false,
        missingFields: [
          "Invalid entityType, it must be either 'Limited Liability Company' or 'Sole Proprietor'.",
        ],
      };
    }

    // Get the requirements based on the entity type
    const entityConfig = stepConfig.entityTypeValidation[entityType];
    const entityMandatory = entityConfig.mandatory;

    // Validate mandatory fields for the entity type
    const entityMissingFields = entityMandatory.filter((field) => !body[field]);
    if (entityMissingFields.length > 0) {
      return {
        valid: false,
        missingFields: entityMissingFields,
      };
    }
  }

  return {
    valid: true,
    missingFields: [],
  };
}
