import bcrypt from "bcryptjs";
import User from "../model/user.model.js";
import Distributor from "../model/distributor.model.js";
import Department from "../model/department.model.js";
import catchAsync from "../utils/catchAsync.js";
import jwt from "jsonwebtoken"

async function authenticate(username, password) {
  let user = await User.aggregate([
    {
      $match: {
        username: username,
      },
    },
    {
      $addFields: {
        usergroupids: {
          $map: {
            input: "$userGroup",
            as: "group",
            in: {
              $toObjectId: "$$group",
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: "usergroups",
        localField: "usergroupids",
        foreignField: "_id",
        as: "userGroup",
      },
    },
  ]);
  if (user.length) {
    user = user[0];
  } else {
    user = null;
  }

  if (!user) {
    return null;
  }
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    return null;
  }
  let allScopes = formatAccessScopes(user.userGroup);
  let userToBeReturned = {
    _id: user._id,
    name: user.name,
    username: user.username,
    access_scopes: allScopes,
    usergroupids: user.usergroupids,
    type: user.type
  };
  return userToBeReturned;
}

export { authenticate };

function formatAccessScopes(userGroup) {
  let access_scopes = {};
  userGroup.forEach((group) => {
    Object.keys(group.access_scopes).forEach((module) => {
      if (access_scopes[module]) {
        let scopes = [...access_scopes[module], ...group.access_scopes[module]];
        scopes = Array.from(new Set(scopes));
        access_scopes[module] = scopes;
      } else {
        access_scopes[module] = group.access_scopes[module];
      }
    });
  });

  return access_scopes;
}

export const loginFrontendUser = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  let user;
  let distributor = await Distributor.findOne({ email: email }).populate();
  let departmentPeople = await Department.findOne({ email: email }).populate();
  console.log(departmentPeople)
  if(departmentPeople) {
    const isCountryManager = departmentPeople.designation.isCountryManager
    if(isCountryManager) {
      user = departmentPeople
    }else {
      user = null
    }
  } 
  if(distributor) {
     user = distributor
  }
  console.log(user)
  if (!user) {
    return res.status(401).json({ error: "Invalid email or password" });
  }
  if(distributor || departmentPeople) {
    const isPasswordValid = await bcrypt.compare(password, user?.password);
    if (!isPasswordValid) {
      user = null;
    }
  }
  

  const token = jwt.sign(
    { userId: user._id, email: user.email, name: user.name, type: user.type, shopifyCustomerId: user.shopifyCustomerId },
    process.env.JWT_TOKEN_SECRET,
    { expiresIn: "8h" }
  );
  user.password = undefined
  return res.json({ token, user });
});
