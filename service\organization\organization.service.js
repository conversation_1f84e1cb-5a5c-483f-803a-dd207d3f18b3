import OrganizationSchema from "../../model/organization.model.js";
import { v4 as uuidv4 } from 'uuid';

export async function createOrganizationPeopleEntry(newEmployeeDetail){
  const uniqId = uuidv4(); 
  const createEmployee = OrganizationSchema.create({
    id: `${uniqId}`,
    ...newEmployeeDetail
  })
  return createEmployee;
}

// modify department details in db
export async function updateOrganizationDetails(modifiedOrganizationInfo){
  const updateDepartmentInfo = OrganizationSchema.findOneAndUpdate(
    {id: `${modifiedOrganizationInfo.id}`},
    {$set:{
      ...modifiedOrganizationInfo
    }},
  )
  return updateDepartmentInfo;
}

// delete distributor details
export async function deleteOrganizationPeopleDataFromDB(departmentData){
  const { id } = departmentData;
  const deleteDepartmentData = await OrganizationSchema.deleteOne({ id });

  return deleteDepartmentData;
}