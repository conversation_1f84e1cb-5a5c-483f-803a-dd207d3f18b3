extends baseEmail

block content
  p Dear #{emailPayload.customerName},

  p We’re pleased to inform you that your account passed our first-level approval process.

  p You’ve been assigned a dedicated sales representative:
  ul
    li Name: #{emailPayload.salesPersonName}
    li Email: 
      a(href=`mailto:${emailPayload.salesPersonEmail}`) #{emailPayload.salesPersonEmail}

  p They’ll assist you with inquiries and support.

  p Thank you for choosing us. Let’s build something great!

  p <PERSON> wishes,  
  p - The #{emailPayload.storeName} Team
