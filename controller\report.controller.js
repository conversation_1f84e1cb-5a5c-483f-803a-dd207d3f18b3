import Shipment from "../model/shipment.model.js";
import Order from "../model/order.model.js";

export const orderDistributorsManagersBreakdown = async (req, res) => {
  try {
    const orders = await Order.aggregate[
      {
        $search: {
          index: "order",
          range: {
            path: "createdAt",
            gte: new Date("Fri, 24 May 2024 00:00:00 GMT"),
            lte: new Date("Fri, 24 May 2024 23:59:59 GMT"),
          },
        },
      }
    ];
  } catch (error) {
    console.log("orderDistributorsManagersBreakdown", error);
    res.send({ error: "Something went wrong" });
  }
};
