import StatusActionLink from "../model/statusActionLink.model.js";
import {
  createOne,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";

export const getStatusActionLink = getAll(StatusActionLink);
export const getOneStatusActionLink = getOne(StatusActionLink);
export const createStatusActionLink = createOne(StatusActionLink);
export const updateStatusActionLink = updateOne(StatusActionLink);
export const deleteStatusActionLink = deleteOne(StatusActionLink);
