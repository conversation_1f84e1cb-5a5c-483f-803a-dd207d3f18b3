import bcrypt from "bcryptjs";
import Department from "../model/department.model.js";

async function authenticate(email, password) {
  let user = await Department.find({ email: email }).populate();

  console.log(user);

  if (user.length) {
    user = user[0];
  } else {
    user = null;
  }

  if (!user) {
    return null;
  }
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    return null;
  }

  return user;
}

export { authenticate };
