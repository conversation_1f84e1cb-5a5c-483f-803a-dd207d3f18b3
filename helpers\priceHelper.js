import Company from "../model/company.model.js";
import Inventory from "../model/inventory.model.js";

const findMatchingSkuAndPrice = (products, companyLocationCatalogIds) => {
  return products
    .map((product) => {
      const matchedPriceList = product.priceList.find((priceItem) => {    
        return companyLocationCatalogIds.includes(
          priceItem.companyLocationCatalogId.toString()
        );
      });

      if (matchedPriceList) {
        return {
          sku: product.sku,
          price: matchedPriceList.price,
        };
      }

      return null;
    })
    .filter((item) => item !== null);
};

export const getPricesForCustomerAndSkus = async (customerId, skuList) => {
  try {
    const companies = await Company.find({ customerIds: customerId });

    const catalogIds = companies.flatMap(
      (company) => company.companyLocationCatalogIds
    );

    if (catalogIds.length === 0) {
      console.log("not found");
      return [];
    }

    const inventories = await Inventory.find({
      sku: { $in: skuList },
    });

    if (inventories.length === 0) {
      console.log("No inventory found for the provided SKUs.");
      return []; // Return empty array if no inventories are found
    }

    const result = await findMatchingSkuAndPrice(inventories, catalogIds);

    return result;
  } catch (error) {
    console.error("Error fetching prices for customer and SKUs:", error);
    throw new Error("Failed to fetch prices");
  }
};
