import { dirname } from "path";
import { fileURLToPath } from "url";
import path from "path";
import fs from "fs";
import {
  stepRequirements,
  CustomCompanyDetails,
  splitName,
  ConvertToGID,
  ConvertFromGID,
} from "../helpers/companyHelper.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import { uploadToS3 } from "../utils/helperFunction.js";
import {
  ASSIGN_MAIN_CONTACT,
  COMPANY_ASSIGN_CUSTOMER,
  CREATE_COMPANY_BY_NAME,
  UPDATE_COMPANY_NAME,
  UPDATE_CUSTOMER,
} from "./graphqlApis.js";
import { GetCustomerCompanyContactProfileByCustId } from "../service/shopify/company.service.js";
import {
  GetDistributorRecord,
  CreateDistributorRecord,
  UpdateDistributorRecord,
} from "../service/distributor/companyDistributor.service.js";
/**
 * @param {express.Request} req - Express request object
 * @param {express.Response} res - Express response object
 */

const __dirname = dirname(fileURLToPath(import.meta.url));

export async function ProcessGetCompanyDetailsFromCustomerId(req, res) {
  try {
    const reqCustomerId = parseInt(req.params.id);

    // Validate that email is provided
    if (!reqCustomerId) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Query parameter is required: CustomerId",
            field: "CustomerId",
          },
        ],
      });
    }

    const reqCustomerGID = ConvertToGID(reqCustomerId, "Customer");

    // Fetch customer by email
    const customerCompany = await GetDistributorRecord(reqCustomerId);

    if (!customerCompany) {
      const customer = await GetCustomerCompanyContactProfileByCustId(
        reqCustomerGID
      );

      if (!customer) {
        return res.status(404).json({
          responseCode: 1,
          status: "error",
          errors: [
            {
              code: "NOT_FOUND",
              message: `Customer record for ${reqCustomerId} does not exist in DB.`,
            },
            {
              code: "INTERNAL_SERVER_ERROR",
              message: `Something went wrong while finding Customer with ID ${reqCustomerId} in shopify.`,
            },
          ],
        });
      }
      if (!customer.customerId) {
        return res.status(404).json({
          responseCode: 1,
          status: "error",
          errors: [
            {
              code: "NOT_FOUND",
              message: `Customer record for ${reqCustomerId} does not exist in DB.`,
            },
            {
              code: "NOT_FOUND",
              message: `Customer with ID ${reqCustomerId} does not exist in shopify.`,
            },
          ],
        });
      }
      if (!customer.companyId) {
        return res.status(200).json({
          responseCode: 0,
          status: "success",
          data: {
            currentStep: 0,
            companyId: null,
            details: {
              step1: {
                companyName: null,
                name: null,
                email: customer.email,
                phone: null,
                tin: null,
              },
              step2: {
                brn: null,
                nricNumber: null,
                obrn: null,
                sst: null,
                businessAddress: null,
                businessDescription: null,
                msicCodes: null,
              },
              step3: {
                entityType: null,
                form9: null,
                form24: null,
                form49: null,
                memArticle: null,
                icCard: null,
                lan: null,
                bankStatement: null,
                formD: null,
                form1: null,
              },
            },
          },
        });
      }
      return res.status(200).json({
        responseCode: 0,
        status: "success",
        data: {
          currentStep: 0,
          companyId: null,
          details: {
            step1: {
              companyName: null,
              name: null,
              email: customer.email,
              phone: null,
              tin: null,
            },
            step2: {
              brn: null,
              nricNumber: null,
              obrn: null,
              sst: null,
              businessAddress: null,
              businessDescription: null,
              msicCodes: null,
            },
            step3: {
              entityType: null,
              form9: null,
              form24: null,
              form49: null,
              memArticle: null,
              icCard: null,
              lan: null,
              bankStatement: null,
              formD: null,
              form1: null,
            },
          },
        },
      });
    }

    // Fetch company details by company ID
    const currentCompanyDetails = CustomCompanyDetails(customerCompany);

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: currentCompanyDetails,
    });
  } catch (error) {
    console.error(`Error in ProcessGetCompanyDetailsFromEmail: ${error}`);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: `An unexpected error occurred: ${error.message}`,
        },
      ],
    });
  }
}

export async function ProcessUpdateCompanyDetails(req, res) {
  try {
    const reqCustomerId = parseInt(req.params.id);
    const { currentStep } = req.body;
    const reqDetails = req.body.details;

    let validationErrors = [];

    if (!reqCustomerId) {
      validationErrors.push({
        code: "VALIDATION_ERROR",
        message: "Query parameter is required: CustomerId",
        field: "CustomerId",
      });
    }

    // Validate that currentStep exists
    if (!currentStep) {
      validationErrors.push({
        code: "VALIDATION_ERROR",
        message: "Missing mandatory body form field: currentStep",
        field: "currentStep",
      });
    }

    // If there are any validation errors, return them
    if (validationErrors.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: validationErrors,
      });
    }

    const requestedCurrentStep = parseInt(currentStep, 10);

    const validSteps = [1, 2, 3, 4];
    if (!validSteps.includes(requestedCurrentStep)) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: "Invalid currentStep. It must be a value between 1 and 4.",
          },
        ],
      });
    }

    if (requestedCurrentStep !== 4) {
      // Validate that details exist
      if (!reqDetails) {
        validationErrors.push({
          code: "VALIDATION_ERROR",
          message: "Missing mandatory body form field: details",
          field: "details",
        });
      } else if (typeof reqDetails !== "string") {
        validationErrors.push({
          code: "VALIDATION_ERROR",
          message:
            "details must be a valid JSON string in body form field: details",
          field: "details",
        });
      } else {
        try {
          JSON.parse(reqDetails); // Try to parse to check if it's valid JSON
        } catch (error) {
          validationErrors.push({
            code: "VALIDATION_ERROR",
            message:
              "details must be a valid JSON string in body form field: details",
            field: "details",
          });
        }
      }
    }

    // If there are any validation errors, return them
    if (validationErrors.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: validationErrors,
      });
    }

    const reqCustomerGID = ConvertToGID(reqCustomerId, "Customer");
    let details = null;
    if (requestedCurrentStep !== 4) {
      details = JSON.parse(reqDetails);
    }

    // Fetch customer by id
    const customerCompany = await GetDistributorRecord(reqCustomerId);

    if (!customerCompany) {
      const customer = await GetCustomerCompanyContactProfileByCustId(
        reqCustomerGID
      );

      if (!customer) {
        return res.status(404).json({
          responseCode: 1,
          status: "error",
          errors: [
            {
              code: "NOT_FOUND",
              message: `Customer record for ${reqCustomerId} does not exist in DB.`,
            },
            {
              code: "INTERNAL_SERVER_ERROR",
              message: `Something went wrong while finding Customer with ID ${reqCustomerId} in shopify.`,
            },
          ],
        });
      }
      if (!customer.customerId) {
        return res.status(404).json({
          responseCode: 1,
          status: "error",
          errors: [
            {
              code: "NOT_FOUND",
              message: `Customer record for ${reqCustomerId} does not exist in DB.`,
            },
            {
              code: "NOT_FOUND",
              message: `Customer with ID ${reqCustomerId} does not exist in shopify.`,
            },
          ],
        });
      }

      if (requestedCurrentStep === 1) {
        const { valid, missingFields } = await ValidateStepFieldsWithFiles(
          reqCustomerGID,
          requestedCurrentStep,
          details,
          req.files
        );

        if (!valid) {
          const validationErrors = missingFields.map((field) => ({
            code: "VALIDATION_ERROR",
            message: `Missing mandatory field for step ${requestedCurrentStep}: ${field}`,
            field: `${field}`,
          }));

          return res.status(400).json({
            responseCode: 1,
            status: "error",
            errors: [
              {
                code: "NOT_FOUND",
                message: `Customer record for ${reqCustomerId} does not exist in DB.`,
              },
              ...validationErrors, // Spread the validation errors here
            ],
          });
        }

        if (!customer.companyId) {
          console.log(
            "Create Company by name, Update Customer details, Link Company to Customer, Assign main contact"
          );

          const companyCreatedResult = await ProcessCreateCompany(
            details,
            reqCustomerGID
          );
          if (!companyCreatedResult.allPromisesResolved) {
            return res.status(400).json({
              responseCode: 1,
              status: "error",
              errors: [
                {
                  code: "NOT_FOUND",
                  message: `Customer record for ${reqCustomerId} does not exist in DB.`,
                },
                ...companyCreatedResult.errors,
              ],
            });
          }

          // console.log("companyCreatedResult:", JSON.stringify(companyCreatedResult, null, 2));

          const companyIdNum = ConvertFromGID(companyCreatedResult.companyId);

          const newCustomerCompany = await CreateDistributorRecord(
            details,
            reqCustomerId,
            companyIdNum
          );

          const currentCompanyDetails =
            CustomCompanyDetails(newCustomerCompany);

          return res.status(200).json({
            responseCode: 0,
            status: "success",
            data: currentCompanyDetails,
          });
        } else {
          let isMainContact = true;
          if (!customer.isMainContact) {
            const mainContactAssigned = await fetchGraphqlDataShopify(
              ASSIGN_MAIN_CONTACT,
              {
                companyId: customer.companyId,
                companyContactId: customer.companyContactId,
              }
            );
            const companyIdUpdated =
              mainContactAssigned?.data?.companyAssignMainContact?.company?.id;
            if (!companyIdUpdated) {
              isMainContact = false;
            }
          }
          if (!isMainContact) {
            return res.status(400).json({
              responseCode: 1,
              status: "error",
              errors: [
                {
                  code: "NOT_FOUND",
                  message: `Customer record for ${reqCustomerId} does not exist in DB.`,
                },
                {
                  code: "BAD_REQUEST",
                  message: `Customer Company MAIN contact Link Failed. Cannot create record in DB`,
                },
              ],
            });
          } else {
            // console.log("Updating Company by step:", requestedCurrentStep);
            const updatedStepResult = await UpdateCompanyByStep(
              reqCustomerGID,
              requestedCurrentStep,
              details,
              req.files,
              customer.companyId
            );
            // console.log({ updatedStepResult });

            if (!updatedStepResult.success) {
              return res.status(400).json({
                responseCode: 1,
                status: "error",
                errors: updatedStepResult.errors,
              });
            }

            const updatedCustomerCompany = await GetDistributorRecord(
              reqCustomerId
            );

            const currentCompanyDetails = CustomCompanyDetails(
              updatedCustomerCompany
            );

            return res.status(200).json({
              responseCode: 0,
              status: "success",
              data: currentCompanyDetails,
            });
          }
        }
      } else {
        return res.status(400).json({
          responseCode: 1,
          status: "error",
          errors: [
            {
              code: "NOT_FOUND",
              message: `Customer record for ${reqCustomerId} does not exist in DB.`,
            },
            {
              code: "BAD_REQUEST",
              message: `Should be on Step 1 for Processing.`,
            },
          ],
        });
      }
    }

    //Now Check if the meta current step is greater than the request current step OR lets take an example.
    //Step validation shall be there, like if customer is on 2nd step, then API shall not accept 4th or 3rd step details.
    // If customer in on step x, then all steps lower than x shall be editable.
    // If meta currnt step is 1 and request current step is 3, then it should give an error and not allow the update.
    // Also if meta current step is 1 and requested current step is 2, then it should allow the update because it should have a gap of 1 only.
    // Next if meta current step is 3 and requested current step is 1 or 2, then also it should be able to update the company

    // If conditions satisfy, then check the requested current step and check for the mandatory input properties in the request body for the respective current step.

    // Validate step progression
    const companyCurrentStep = customerCompany.currentStep;

    if (companyCurrentStep === 4) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: `Invalid step update. You cannot update after Step 4`,
          },
        ],
      });
    }

    // Allow only if updating lower steps or next immediate step
    if (requestedCurrentStep > companyCurrentStep + 1) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "BAD_REQUEST",
            message: `Invalid step update. You can only proceed to the next step or update previous steps. Current step: ${companyCurrentStep}, Requested step: ${requestedCurrentStep}`,
          },
        ],
      });
    }

    // Validate mandatory fields for the requested step
    const { valid, missingFields } = await ValidateStepFieldsWithFiles(
      reqCustomerGID,
      requestedCurrentStep,
      details,
      req.files
    );

    if (!valid) {
      const validationErrors = missingFields.map((field) => ({
        code: "VALIDATION_ERROR",
        message: `Missing mandatory field for step ${requestedCurrentStep}: ${field}`,
        field: `${field}`,
      }));

      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          ...validationErrors, // Spread the validation errors here
        ],
      });
    }

    // Proceed with update logic for the company based on currentStep
    // Here you can process the update or save the information accordingly.

    const updatedStepResult = await UpdateCompanyByStep(
      reqCustomerGID,
      requestedCurrentStep,
      details,
      req.files,
      ConvertToGID(customerCompany.shopifyCompanyId, "Company")
    );

    // console.log({ updatedStepResult });

    if (!updatedStepResult.success) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: updatedStepResult.errors,
      });
    }

    const updatedCustomerCompany = await GetDistributorRecord(reqCustomerId);

    const currentCompanyDetails = CustomCompanyDetails(updatedCustomerCompany);

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: currentCompanyDetails,
    });
  } catch (error) {
    console.error(`Error in ProcessUpdateCompanyDetails: ${error}`);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: `An unexpected error occurred: ${error.message}`,
        },
      ],
    });
  }
}

async function ValidateStepFieldsWithFiles(customerId, step, details, files) {
  const stepConfig = stepRequirements[`step${step}`];
  if (!stepConfig) {
    return {
      valid: false,
      missingFields: [`Step ${step} configuration is missing`],
    };
  }

  // console.log("details:", details);
  // console.log("files:", files);

  if (step === 4) {
    return {
      valid: true,
      missingFields: [],
    };
  }

  const { mandatory } = stepConfig;
  // console.log("mandatory:", mandatory);
  const missingFields = new Set(
    mandatory.filter((field) => !details[field] && !files[field])
  );

  // Step 3 specific validation: ensure files are provided if mandatory
  if (step === 3) {
    const entityType = details.entityType;

    // Check if the entity type is valid
    if (
      !["Limited Liability Company", "Sole Proprietor"].includes(entityType)
    ) {
      return {
        valid: false,
        missingFields: [
          "Invalid entityType, it must be either 'Limited Liability Company' or 'Sole Proprietor'.",
        ],
      };
    }

    const entityConfig = stepConfig.entityTypeValidation[entityType];
    const entityMandatory = entityConfig.mandatory;

    const existingRecord = await GetDistributorRecord(
      ConvertFromGID(customerId)
    );

    if (existingRecord && existingRecord.currentStep === 3) {
      console.log("Updating Step 3");

      entityMandatory.forEach((field) => {
        if (files[field]) return; // Valid input present
        if (!existingRecord.companyDetails.documents[field]) {
          missingFields.add(field); // Missing mandatory file not in input or DB
        }
      });
    } else {
      entityMandatory.forEach((field) => {
        if (!files[field]) {
          missingFields.add(field);
        }
      });
    }
  }

  if (missingFields.size > 0) {
    return {
      valid: false,
      missingFields: Array.from(missingFields),
    };
  }

  return {
    valid: true,
    missingFields: [],
  };
}

async function ProcessCreateCompany(details, customerId) {
  try {
    let errors = [];
    let allPromisesResolved = true;
    // console.log("details inside ProcessCreateCompany:", details);
    const { email, name, companyName, phone } = details;
    const { firstName, lastName } = splitName(name);
    const paymentTermsTemplateId = "gid://shopify/PaymentTermsTemplate/5";
    const companyCreated = await fetchGraphqlDataShopify(
      CREATE_COMPANY_BY_NAME,
      {
        name: companyName,
        paymentTermsTemplateId: paymentTermsTemplateId,
      }
    );
    // console.log("companyCreated:", JSON.stringify(companyCreated, null, 2));
    const companyId = companyCreated?.data?.companyCreate?.company?.id;
    const companyUserErrors = companyCreated?.data?.companyCreate?.userErrors;
    if (!companyId || companyUserErrors.length !== 0) {
      console.log("Company creation FAILED");
      errors.push({
        code: "SHOPIFY_API_FAILURE",
        message: `Company creation FAILED for Customer ID ${customerId} with Company Name ${companyName}`,
        shopifyErrors: companyUserErrors,
      });
      allPromisesResolved = false;
    }

    // console.log({ customerId, firstName, lastName, email, phone });

    const customerUpdated = await fetchGraphqlDataShopify(UPDATE_CUSTOMER, {
      id: customerId,
      firstName: firstName ? firstName : "",
      lastName: lastName ? lastName : "",
      email: email,
      phone: phone,
    });

    // console.log("customerUpdated", JSON.stringify(customerUpdated, null, 2));

    const customerIdUpdate =
      customerUpdated?.data?.customerUpdate?.customer?.id;
    const customerUserErrors =
      customerUpdated?.data?.customerUpdate?.userErrors;
    if (!customerIdUpdate) {
      console.log("Customer Update FAILED");
      errors.push({
        code: "SHOPIFY_API_FAILURE",
        message: `Customer Update FAILED for Customer ID ${customerId}`,
        shopifyErrors: customerUserErrors,
      });
      allPromisesResolved = false;
    } else if (customerIdUpdate && customerUserErrors.length !== 0) {
      errors.push({
        code: "SHOPIFY_API_FAILURE",
        message: `Customer Update FAILED for Customer ID ${customerId}`,
        shopifyErrors: customerUserErrors,
      });
      allPromisesResolved = false;
    }

    const companyContactAssigned = await fetchGraphqlDataShopify(
      COMPANY_ASSIGN_CUSTOMER,
      {
        companyId: companyId,
        customerId: customerId,
      }
    );
    const companyContactId =
      companyContactAssigned?.data?.companyAssignCustomerAsContact
        ?.companyContact?.id;
    if (!companyContactId) {
      console.log("Company Customer Link FAILED");
      errors.push({
        code: "SHOPIFY_API_FAILURE",
        message: `Company Customer Link FAILED for Customer ID ${customerId} with Company ID ${companyId}`,
      });
      allPromisesResolved = false;
    }

    const mainContactAssigned = await fetchGraphqlDataShopify(
      ASSIGN_MAIN_CONTACT,
      {
        companyId: companyId,
        companyContactId: companyContactId,
      }
    );
    const companyIdUpdated =
      mainContactAssigned?.data?.companyAssignMainContact?.company?.id;
    if (!companyIdUpdated) {
      console.log("Company Customer MAIN CONTACT LINK FAILED");
      errors.push({
        code: "SHOPIFY_API_FAILURE",
        message: `Company Customer MAIN CONTACT LINK FAILED for Customer ID ${customerId} with Company ID ${companyId}`,
      });
      allPromisesResolved = false;
    }

    return { allPromisesResolved, errors, companyId };
  } catch (error) {
    console.log("Error in ProcessCreateCompany : ", error);
    throw error;
  }
}

async function UpdateCompanyByStep(
  customerId,
  currentStep,
  details,
  files = [],
  companyId = ""
) {
  try {
    const existingRecord = await GetDistributorRecord(
      ConvertFromGID(customerId)
    );
    const existingCompanyDetails = existingRecord?.companyDetails || {};

    // Step 1: Update customer and company in Shopify, then update in the DB
    if (currentStep === 1) {
      const { name, email, phone, companyName } = details;
      const { firstName, lastName } = splitName(name);

      // console.log("details inside UpdateCompanyByStep:", details);
      // console.log("firstName:", firstName);
      // console.log("lastName:", lastName);
      // console.log("email:", email);
      // console.log("phone:", phone);
      // console.log("companyName:", companyName);

      // 1. Update the customer in Shopify with personal details
      const customerUpdateResult = await UpdateDistributorInShopify(
        customerId,
        firstName,
        lastName,
        email,
        phone,
        companyId,
        companyName
      );

      if (!customerUpdateResult.allPromisesResolved) {
        return {
          success: false,
          errors: customerUpdateResult.errors,
        };
      }
      const toUpdateObj = {
        currentStep: 1,
        shopifyCompanyId: ConvertFromGID(companyId),
        email,
        name,
        phone,
        companyName,
        companyDetails: {
          ...existingCompanyDetails,
          tin: details.tin,
        },
      };

      // 3. Update the database with company and customer details
      const dbUpdateResult = await UpdateDistributorRecord(
        ConvertFromGID(customerId),
        toUpdateObj
      );

      // console.log({ dbUpdateResult });

      if (!dbUpdateResult) {
        return {
          success: false,
          errors: [
            {
              code: "DB_UPDATE_FALURE",
              message: `Step 1 Update Failed in DB`,
            },
          ],
        };
      }

      return {
        success: true,
      };
    }

    // Step 2: Update the provided details in the DB
    if (currentStep === 2) {
      const toUpdateObj = {
        currentStep: 2,
        companyDetails: {
          ...existingCompanyDetails,
          brn: details.brn,
          nricNumber: details.nricNumber,
          obrn: details.obrn,
          sst: details.sst,
          businessAddress: details.businessAddress,
          businessDescription: details.businessDescription,
          msicCodes: details.msicCodes,
          pincode: details.pincode,
          country: details.country,
          city: details.city,
          state: details.state,
        },
      };

      // 3. Update the database with company and customer details
      const dbUpdateResult = await UpdateDistributorRecord(
        ConvertFromGID(customerId),
        toUpdateObj
      );

      // console.log({ dbUpdateResult });

      if (!dbUpdateResult) {
        return {
          success: false,
          errors: [
            {
              code: "DB_UPDATE_FALURE",
              message: `Step 2 Update Failed in DB`,
            },
          ],
        };
      }

      return {
        success: true,
      };
    }

    // Step 3: Upload files to S3 and update file URLs in the DB
    if (currentStep === 3) {
      if (!files || Object.keys(files).length === 0) {
        return {
          success: false,
          errors: [
            {
              code: "VALIDATION_ERROR",
              message: `Missing mandatory fields for step 3: No Files Provided`,
              field: `No Files Provided`,
            },
          ],
        };
      }

      const fileUploadPromises = Object.keys(files).map(async (key) => {
        const file = files[key][0]; // Get the first file for each key (since multer saves them as arrays)
        const filePath = path.join(
          __dirname,
          "..",
          "asset",
          "upload",
          "company",
          file.filename
        );

        try {
          // Upload to S3
          const uploadResult = await uploadToS3(
            filePath,
            process.env.AWS_S3_BUCKET,
            file.filename
          );
          const fileUrl = uploadResult.Location; // Get the S3 URL

          // Delete the local file after upload
          fs.unlinkSync(filePath);

          return {
            fileName: key, // Use the field name (form9, form24, etc.)
            fileUrl,
          };
        } catch (err) {
          console.error("S3 Upload Error:", err);
          throw new Error(`File upload failed for ${file.originalname}`);
        }
      });

      // Wait for all files to be uploaded
      const uploadedFiles = await Promise.all(fileUploadPromises);

      // Create an object to store file URLs
      const fileUrls = uploadedFiles.reduce((acc, file) => {
        acc[file.fileName] = file.fileUrl;
        return acc;
      }, {});

      const toUpdateObj = {
        currentStep: 3,
        companyDetails: {
          ...existingCompanyDetails,
          entityType: details.entityType,
          documents: {
            ...existingCompanyDetails.documents,
            ...fileUrls,
          },
        },
      };

      // Update the database
      const dbUpdateResult = await UpdateDistributorRecord(
        ConvertFromGID(customerId),
        toUpdateObj
      );

      // console.log({ dbUpdateResult });

      if (!dbUpdateResult) {
        return {
          success: false,
          errors: [
            {
              code: "DB_UPDATE_FALURE",
              message: `Step 3 Update Failed in DB`,
            },
          ],
        };
      }

      return {
        success: true,
      };
    }

    if (currentStep === 4) {
      const toUpdateObj = {
        currentStep: 4,
        companyDetails: {
          ...existingCompanyDetails,
        },
      };

      // 3. Update the database with company and customer details
      const dbUpdateResult = await UpdateDistributorRecord(
        ConvertFromGID(customerId),
        toUpdateObj
      );

      // console.log({ dbUpdateResult });

      if (!dbUpdateResult) {
        return {
          success: false,
          errors: [
            {
              code: "DB_UPDATE_FALURE",
              message: `Step 4 Update Failed in DB`,
            },
          ],
        };
      }

      return {
        success: true,
      };
    }
    // If the step is not recognized
    throw new Error(`Invalid step: ${currentStep}`);
  } catch (error) {
    console.error(`Error in UpdateCompanyByStep: ${error}`);
    return {
      success: false,
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: `Error processing step ${currentStep}: ${error.message}`,
        },
      ],
    };
  }
}

async function UpdateDistributorInShopify(
  customerId,
  firstName,
  lastName,
  email,
  phone,
  companyId,
  companyName
) {
  try {
    // console.log("UpdateDistributorInShopify")
    // console.log("customerId:", customerId);
    // console.log("firstName:", firstName);
    // console.log("lastName:", lastName);
    // console.log("email:", email);
    // console.log("phone:", phone);
    // console.log("companyId:", companyId);
    // console.log("companyName:", companyName);
    let allPromisesResolved = true;
    let errors = [];
    const customerUpdated = await fetchGraphqlDataShopify(UPDATE_CUSTOMER, {
      id: customerId,
      firstName: firstName ? firstName : "",
      lastName: lastName ? lastName : "",
      email: email,
      phone: phone,
    });

    const customerIdUpdate =
      customerUpdated?.data?.customerUpdate?.customer?.id;
    const customerUserErrors =
      customerUpdated?.data?.customerUpdate?.userErrors;
    if (!customerIdUpdate) {
      console.log("Customer Update FAILED");
      errors.push({
        code: "SHOPIFY_API_FAILURE",
        message: `Customer Update FAILED for Customer ID ${customerId}`,
        shopifyErrors: customerUserErrors,
      });
      allPromisesResolved = false;
    } else if (customerIdUpdate && customerUserErrors.length !== 0) {
      errors.push({
        code: "SHOPIFY_API_FAILURE",
        message: `Customer Update FAILED for Customer ID ${customerId}`,
        shopifyErrors: customerUserErrors,
      });
      allPromisesResolved = false;
    }

    const companyUpdated = await fetchGraphqlDataShopify(UPDATE_COMPANY_NAME, {
      companyId: companyId,
      name: companyName,
    });

    const companyIdUpdate = companyUpdated?.data?.companyUpdate?.company?.id;
    if (!companyIdUpdate) {
      console.log("Company Name Update FAILED");
      errors.push({
        code: "SHOPIFY_API_FAILURE",
        message: `Company Name Update FAILED for Customer ID ${customerId} and Company ID ${companyId}`,
      });
      allPromisesResolved = false;
    }
    return { allPromisesResolved, errors };
  } catch (error) {
    console.log("Error in UpdateCustomerInShopify : ", error);
    throw error;
  }
}
