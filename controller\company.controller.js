import { createOne, deleteOne, getAll, getOne, updateOne } from '../utils/controllerFactory.js';
import Company from '../model/company.model.js';
import AppError from '../utils/appError.js';
import catchAsync from '../utils/catchAsync.js';
import axios from "axios";

export const getCompanies = getAll(Company);
export const getOneCompany = getOne(Company);
export const createCompany = createOne(Company);
export const updateCompany = updateOne(Company);
export const deleteCompany = deleteOne(Company);


export const updateCompanyPriority = catchAsync(async (req, res, next) => {
  const companyData = req.body.companies;
  const bulkOps = companyData.map(update => ({
    updateOne: {
      filter: { _id: (update._id) },
      update: { $set: { priority: update.priority } }
    }
  }));

  const result = await Company.bulkWrite(bulkOps);
  res.status(200).json({
    status: 'success',
    // matchedCount: result.matchedCount,
    // modifiedCount: result.modifiedCount
    data: result
  });
});


export const createCompanyShopify = async (req, res, next) => {
  const name = req.body.name;
  const note = req.body.note || "";
  if(!name) return next(new AppError('Missing required field [name]'))
  var data = JSON.stringify({
    query: `mutation companyCreate($input: CompanyCreateInput!) {
      companyCreate(input: $input) {
        company {
          id
          name
        }
      }
    }`,
    variables: { "input": { "company": { "name": name, "note": note } } }
  });


  // TODO: if needed company creation with the payment term
  // export const createCompanyShopify = async (req, res, next) => {
  //   const name = req.body.name;
  //   const note = req.body.note || "";
  //   const paymentTermsTemplateId = "gid://shopify/PaymentTermsTemplate/5";
  //   if (!name) return next(new AppError("Missing required field [name]"));
  //   var data = JSON.stringify({
  //     query: `mutation companyCreate($input: CompanyCreateInput!) {
  //       companyCreate(input: $input) {
  //         company {
  //           id
  //           name
  //         }
  //       }
  //     }`,
  //     variables: {
  //       input: {
  //         company: { name: name, note: note },
  //         companyLocation: {
  //           buyerExperienceConfiguration: {
  //             checkoutToDraft: false,
  //             editableShippingAddress: false,
  //             paymentTermsTemplateId: paymentTermsTemplateId,
  //           },
  //         },
  //       },
  //     },
  //   });
  // mutation MyMutation {
  //   companyCreate(
  //     input: {companyLocation: {buyerExperienceConfiguration: {checkoutToDraft: false, editableShippingAddress: false, paymentTermsTemplateId: "gid://shopify/PaymentTermsTemplate/5"}}}
  //   )
  // }
  var config = {
    method: 'post',
    url: `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
    headers: {
      'X-Shopify-Access-Token': `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
      'Content-Type': 'application/json'
    },
    data: data
  };

  axios(config)
    .then(function (response) {
      console.log(JSON.stringify(response.data));
      req.body.shopifyCompanyId = response.data?.data?.companyCreate?.company?.id?.split("/")?.pop()
      next()
    })
    .catch(function (error) {
      console.log(error);
      return next(new AppError(error?.response?.data?.errors ? JSON.stringify(error?.response?.data?.errors) : error));

    });

};


export const updateCompanyShopify = async (req, res, next) => {

    const {shopifyCompanyId, name, note} = req.body;

    const myHeaders = new Headers();
    myHeaders.append("X-Shopify-Access-Token", `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`);
    myHeaders.append("Content-Type", "application/json");

    const graphql = JSON.stringify({
      query: `mutation MyMutation {\r\n  companyUpdate(companyId: \"gid://shopify/Company/${shopifyCompanyId}", input: {name: \"${name}", note: \"${note}"}) {\r\n    company {\r\n      id\r\n    }\r\n    userErrors {\r\n      message\r\n      field\r\n    }\r\n  }\r\n}\r\n`,
      variables: {}
    })
    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: graphql,
      redirect: "follow"
    };

    fetch(`${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`, requestOptions)
    .then((response) => next())
    .catch((error) => {
      return next(new AppError(error?.response?.data?.errors ? JSON.stringify(error?.response?.data?.errors) : error))
    });
};