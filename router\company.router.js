import express from 'express';
import {
  getCompanies,
  createCompany,
  getOneCompany,
  updateCompany,
  deleteCompany,
  updateCompanyPriority,
  updateCompanyShopify,
} from '../controller/company.controller.js';
import { createCompanyShopify } from '../controller/company.controller.js';

const router = express.Router();

router
  .route('/')
  .get(getCompanies)
  .post(createCompanyShopify, createCompany);
 
  router
  .route('/priority/update')
  .patch(updateCompanyPriority);

router
  .route('/:id')
  .get(getOneCompany)
  .patch(updateCompanyShopify, updateCompany)
  .delete(deleteCompany);

export default router;