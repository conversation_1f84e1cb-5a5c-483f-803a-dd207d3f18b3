import catchAsync from "./catchAsync.js";
import AppError from "./appError.js";
import APIFeatures from "./apiFeatures.js";
import Shipment from "../model/shipment.model.js";
import Distributor from "../model/distributor.model.js";
import Designation from "../model/designation.model.js";
import Department from "../model/department.model.js";

// export const getAll = (Model) =>
// catchAsync(async (req, res, next) => {
//   console.log("--------req", req.user);
//   const loggedInUserId = req.user;
//   const user = await Department.findById(loggedInUserId).populate();
//   console.log("----loggedInUSER", user);
//   let query = Model.find();
//   if (user) {
//     const salespersonDepartmentId = "66d843673104241b8c339634"; //!->salesperson department ID
//     const isSalesperson = user.departmentType.some(
//       (dept) => dept._id.toString() === salespersonDepartmentId
//     );
//     if (isSalesperson) {
//       query = Model.find({ salespersonId: loggedInUserId });
//     }
//   } else {
//     console.log("Logged-in user not found in Department collection");
//   }
//   const features = new APIFeatures(query, req.query)
//     .filter()
//     .sort()
//     .limitFields()
//     .paginate();

//   const doc = await features.query;

//   res.status(200).json({
//     status: "success",
//     result: doc.length,
//     data: {
//       data: doc,
//     },
//   });
// });

// Original function
export const getAll = (Model) =>
  catchAsync(async (req, res, next) => {
    console.log("--------req", req.user);
    let userId = req.user;
    const user = await Department.findById(userId).populate();
    console.log("----loggedInUSER", user);
    let query = Model.find();

    const features = new APIFeatures(Model.find(), req.query)
      .filter()
      .sort()
      .limitFields()
      .paginate();
    const doc = await features.query;
    res.status(200).json({
      status: "success",
      result: await Model.countDocuments(),
      data: {
        data: doc,
      },
    });
  });

// !---> Changed function to accept the search on the basis of email, shopifyCustomerId, name, companyName

export const searchWithCustomFilters = (Model) =>
  catchAsync(async (req, res, next) => {
    try {
      const {
        email,
        name,
        shopifyCustomerId,
        companyName,
        status,
        salespersonId,
      } = req.query;
      const salesPersonDept = req.salesPersonDept;

      const page = req.query.page ? parseInt(req.query.page, 10) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit, 10) : 10;
      const skip = (page - 1) * limit;
      let matchConditions = [];

      if (email && email.trim() !== "") {
        console.log("Searching by email:", email);
        matchConditions.push({
          email: { $regex: new RegExp(`^${email}`, "i") },
        });
      }

      if (name && name.trim() !== "") {
        console.log("Searching by name:", name);
        matchConditions.push({ name: { $regex: new RegExp(`^${name}`, "i") } });
      }

      if (companyName && companyName.trim() !== "") {
        console.log("Searching by companyName (partial search):", companyName);
        matchConditions.push({
          companyName: { $regex: new RegExp(`^${companyName}`, "i") },
        });
      }
      // Status filter
      if (status && status.trim() !== "") {
        console.log("Searching by status:", status);
        matchConditions.push({
          status: { $regex: new RegExp(`^${status}`, "i") },
        });
      }

      // Salesperson ID filter
      if (salespersonId && salespersonId.trim() !== "") {
        console.log("Searching by salespersonId:", salespersonId);
        matchConditions.push({
          salespersonId: { $regex: new RegExp(`^${salespersonId}`, "i") },
        });
      }

      let finalQuery =
        matchConditions.length > 0 ? { $or: matchConditions } : {};

      let salespersonDistributors;

      if (salesPersonDept) {
        salespersonDistributors = await Model.distinct("_id", {
          salespersonId: req.user._id.toString(),
        });
        if (salespersonDistributors.length > 0) {
          // Filter to only include distributors assigned to the salesperson
          finalQuery = {
            ...finalQuery,
            _id: { $in: salespersonDistributors },
          };
        } else {
          // No distributors for the salesperson
          return res.status(200).json({
            status: "success",
            result: 0,
            data: {
              data: [],
            },
          });
        }
      }

      // Shopify Customer ID filter
      if (shopifyCustomerId && shopifyCustomerId.trim() !== "") {
        const doc = await Model.aggregate([
          {
            $match: {
              $and: [
                ...(salesPersonDept
                  ? [{ _id: { $in: salespersonDistributors } }] // Add this condition only if the user is a salesperson
                  : []),
                {
                  $or: [
                    finalQuery,
                    {
                      $expr: {
                        $regexMatch: {
                          input: { $toString: "$shopifyCustomerId" },
                          regex: new RegExp(`^${shopifyCustomerId}`, "i"),
                        },
                      },
                    },
                  ],
                },
              ],
            },
          },
          {
            $facet: {
              results: [{ $skip: skip }, { $limit: limit }],
              totalCount: [{ $count: "total" }],
            },
          },
        ]);

        const totalResults = doc[0]?.totalCount?.[0]?.total || 0;

        return res.status(200).json({
          status: "success",
          result: totalResults,
          data: {
            data: doc[0].results,
          },
        });
      }

      // Regular query with filters
      const totalResults = await Model.countDocuments(finalQuery);

      const features = new APIFeatures(Model.find(finalQuery), req.query)
        .filter()
        .sort()
        .limitFields()
        .paginate();

      const doc = await features.query;

      return res.status(200).json({
        status: "success",
        result: totalResults,
        data: {
          data: doc,
        },
      });
    } catch (error) {
      console.error("Error during query execution:", error.message);
      return res.status(500).json({
        status: "error",
        message: "An error occurred while fetching data",
        error: error.message,
      });
    }
  });

// export const getAll = (Model) =>
//   catchAsync(async (req, res, next) => {
//     console.log("---->>>", req.user);
//     try {
//       console.log("----> Query Parameters:", req.query);

//       const { email, name, shopifyCustomerId, companyName } = req.query;

//       // Ensure default values for pagination
//       const page = req.query.page ? parseInt(req.query.page, 10) : 1;
//       const limit = req.query.limit ? parseInt(req.query.limit, 10) : 10;
//       const skip = (page - 1) * limit; // Calculate how many documents to skip

//       let matchConditions = [];

//       // Add email match condition
//       if (email && email.trim() !== "") {
//         console.log("Searching by email:", email);
//         matchConditions.push({ email: { $regex: new RegExp(email, "i") } });
//       }

//       // Add name match condition
//       if (name && name.trim() !== "") {
//         console.log("Searching by name:", name);
//         matchConditions.push({ name: { $regex: new RegExp(name, "i") } });
//       }

//       // Add companyName match condition
//       if (companyName && companyName.trim() !== "") {
//         console.log("Searching by companyName (partial search):", companyName);
//         matchConditions.push({ companyName: { $regex: new RegExp(companyName, "i") } });
//       }

//       let finalQuery = matchConditions.length > 0 ? { $or: matchConditions } : {};

//       // If shopifyCustomerId is provided, use aggregation for that specific search
//       if (shopifyCustomerId && shopifyCustomerId.trim() !== "") {
//         console.log("Searching by shopifyCustomerId (partial search):", shopifyCustomerId);

//         // Aggregation pipeline for shopifyCustomerId query
//         const doc = await Model.aggregate([
//           {
//             $match: {
//               $or: [
//                 ...matchConditions,
//                 {
//                   $expr: {
//                     $regexMatch: {
//                       input: { $toString: "$shopifyCustomerId" }, // Convert to string
//                       regex: new RegExp(shopifyCustomerId, "i"),  // Apply regex
//                     },
//                   },
//                 },
//               ],
//             },
//           },
//           {
//             $facet: {
//               results: [
//                 { $skip: skip }, // Skip for pagination
//                 { $limit: limit }, // Limit the number of documents
//               ],
//               totalCount: [
//                 { $count: "total" }, // Count total results
//               ],
//             },
//           },
//         ]);

//         const totalResults = doc[0]?.totalCount?.[0]?.total || 0;

//         return res.status(200).json({
//           status: "success",
//           result: totalResults,
//           data: {
//             data: doc[0].results,
//           },
//         });
//       } else {
//         // Simple .find() query for cases where shopifyCustomerId is not involved
//         const features = new APIFeatures(Model.find(finalQuery), req.query)
//           .filter()
//           .sort()
//           .limitFields()
//           .paginate();

//         const doc = await features.query;
//         const totalResults = await Model.countDocuments(finalQuery);

//         return res.status(200).json({
//           status: "success",
//           result: totalResults,
//           data: {
//             data: doc,
//           },
//         });
//       }
//     } catch (error) {
//       console.error("Error during query execution:", error.message);
//       return res.status(500).json({
//         status: "error",
//         message: "An error occurred while fetching data",
//         error: error.message,
//       });
//     }
//   });

export const getOne = (Model, populateOption) =>
  catchAsync(async (req, res, next) => {
    let query = Model.findById(req.params.id).lean();
    if (populateOption) query = query.populate(populateOption);
    const doc = await query;
    if (!doc) return next(new AppError("No document found with this ID"));
    res.status(200).json({
      status: "success",
      data: {
        data: doc,
      },
    });
  });

export const createOne = (Model) =>
  catchAsync(async (req, res, next) => {
    if (
      req.body.departmentNotified &&
      Array.isArray(req.body.departmentNotified)
    ) {
      req.body.departmentNotified = req.body.departmentNotified.map(
        (item) => item.value
      );
    }
    const doc = await Model.create(req.body);
    res.status(201).json({
      status: "success",
      result: 1,
      data: {
        data: doc,
      },
    });
  });
// original function
export const updateOne = (Model) =>
  catchAsync(async (req, res, next) => {
    const doc = await Model.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      status: "success",
      result: 1,
      data: {
        data: doc,
      },
    });
  });

// ! -> changed function to accept the nested object like companyDetails
// export const updateOne = (Model) =>
//   catchAsync(async (req, res, next) => {
//     const doc = await Model.findByIdAndUpdate(
//       req.params.id,
//       { $set: req.body },
//       {
//         new: true,
//         runValidators: true,
//       }
//     );

//     res.status(200).json({
//       status: "success",
//       result: 1,
//       data: {
//         data: doc,
//       },
//     });
//   });

export const deleteOne = (Model) =>
  catchAsync(async (req, res, next) => {
    const { distributor, designation } = req.body;

    if (distributor && designation) {
      const countryManager = req.body._id;

      const foundDesignation = await Designation.findById(designation);
      if (foundDesignation && foundDesignation.isCountryManager) {
        for (const distributorId of distributor) {
          const foundDistributor = await Distributor.findById(distributorId);

          if (foundDistributor) {
            foundDistributor.countryManager =
              foundDistributor.countryManager.filter(
                (id) => id.toString() !== countryManager.toString()
              );
            await foundDistributor.save();
          }
        }
      }
    }

    const doc = await Model.findByIdAndDelete(req.params.id);
    if (!doc) return next(new AppError("No document found with this ID", 400));
    res.status(204).json({
      status: "success",
      data: "Data deleted successfully",
    });
  });
