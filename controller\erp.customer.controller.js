import axios from "axios";
import {
  getCustomerDataDB<PERSON><PERSON><PERSON>,
  formattedCustomerDataHandler,
  createERPCustomerHandler,
  customerUpdateScheduledService,
  getERPSalesPersons,
} from "../service/ERPSync/customerService.js";
import Department from "../model/department.model.js";
import NewAppError from "../utils/newAppError.js";

const createCustomersController = async (customerId) => {
  try {
    if (!customerId) {
      throw new NewAppError(
        "BAD_REQUEST",
        "Invalid input for customerId.",
        400
      );
    }

    const customerData = await getCustomerDataDBHandler([customerId]);
    if (customerData?.status !== 200) {
      throw new NewAppError(
        customerData?.code,
        customerData?.message,
        customerData?.status
      );
    }

    if (customerData.data.customers.length <= 0) {
      throw new NewAppError("MISSING", "Customer not found.", 404);
    }

    const missingRequiredData = customerData.data.customers.filter(
      (customer) => !customer?.customerNumber || !customer?.salespersonId
    );

    if (missingRequiredData.length > 0) {
      throw new NewAppError(
        "MISSING_REQUIRED_FIELDS",
        `Customer Number or SalesPersonKey missing for provided Customer ID.`,
        404
      );
    }

    const salesPersonIds = customerData.data.customers.map(
      (customer) => customer.salespersonId
    );

    const salespersons = await Department.find(
      { _id: { $in: salesPersonIds } },
      { name: 1, salespersonkey: 1 }
    );

    const formattedData = await formattedCustomerDataHandler(
      customerData,
      salespersons
    );

    if (formattedData?.status !== 200) {
      throw new NewAppError(
        formattedData?.code,
        formattedData?.message,
        formattedData?.status
      );
    }

    // Create customer in ERP
    const customerToCreate = formattedData.data.formattedCustomers[0];
    const createdCustomer = await createERPCustomerHandler(customerToCreate);

    if (createdCustomer?.status !== 201 && createdCustomer?.status !== 200) {
      throw new NewAppError(
        createdCustomer?.code || "ERP_ERROR",
        createdCustomer?.message || "Failed to create customer in ERP",
        createdCustomer?.status || 400
      );
    }

    return { data: createdCustomer.data };
  } catch (error) {
    throw new NewAppError("SERVER_ERROR", `${error}`, 500);
  }
};

const customerManualSyncController = async (req, res) => {
  try {
    const response = await customerUpdateScheduledService(true);
    if (response?.status !== 200) {
      return res.status(response?.status).send({
        responseCode: 1,
        status: "error",
        errors: [{ code: "SERVER_ERROR", message: response.error }],
      });
    }

    res.status(response.status).send({
      responseCode: 0,
      status: "success",
      data: { message: response.message },
    });
  } catch {
    console.log(`Error while manual sync`);
    res.status(500).send({
      responseCode: 1,
      status: "error",
      errors: [{ code: "SERVER_ERROR", message: `Error while manual sync` }],
    });
  }
};

const getSalesPersonController = async (req, res) => {
  try {
    const salesPersons = await getERPSalesPersons();
    if (salesPersons.status !== 200) {
      return res.status(salesPersons.status).send({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: salesPersons.code,
            message: salesPersons.message,
          },
        ],
      });
    }

    res.status(salesPersons.status).send({
      responseCode: 0,
      status: "success",
      data: salesPersons.data,
    });
  } catch (error) {
    res.status(500).send({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: `Something went wrong.Error:${error?.message}`,
        },
      ],
    });
  }
};

const createSalesPersonController = async (req, res, next) => {
  try {
    const { salesPersonName, salesPersonKey } = req.body;

    if (!salesPersonName || !salesPersonKey) {
      return next(
        new NewAppError(
          "BAD_REQUEST",
          "salesPersonName and salesPersonKey are mandatory fields.",
          400
        )
      );
    }

    if (salesPersonKey !== salesPersonKey.toUpperCase()) {
      return next(
        new NewAppError(
          "INVALID_INPUT",
          "salesPersonKey must be in uppercase.",
          400
        )
      );
    }

    let data = JSON.stringify({
      SalespersonKey: salesPersonKey,
      Name: salesPersonName,
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SAGE_API_ENDPOINT}/AR/ARSalespersons`,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
      },
      data: data,
    };

    const response = await axios.request(config);

    res.status(response.status).send({
      responseCode: 0,
      status: "success",
      data: {
        message: `SalesPerson with key :${salesPersonKey}, created successfully.`,
      },
    });
  } catch (error) {
    const statusCode = error.response?.status || 500;
    const errorMessage =
      error.response?.data?.error?.message?.value ||
      error.message ||
      "Server Error";

    return next(
      new NewAppError(
        `${error.code || "SERVER_ERROR"}`,
        `ERP Error.Message:${errorMessage}`,
        statusCode
      )
    );
  }
};

const deleteSalesPersonController = async (req, res, next) => {
  try {
    const { salesPersonKey } = req.params;
    if (!salesPersonKey) {
      return next(
        new NewAppError("BAD_REQUEST", "salesPersonKey is required.", 400)
      );
    }
    let config = {
      method: "delete",
      maxBodyLength: Infinity,
      url: `${process.env.SAGE_API_ENDPOINT}/AR/ARSalespersons('${salesPersonKey}')`,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
      },
    };

    const response = await axios.request(config);
    res.status(response.status).send({
      responseCode: 0,
      status: "success",
      data: {
        message: `SalesPerson ${salesPersonKey} deleated sucessfully.`,
      },
    });
  } catch (error) {
    const statusCode = error.response?.status || 500;
    const errorMessage =
      error.response?.data?.error?.message?.value ||
      error.message ||
      "Server Error";

    return next(
      new NewAppError(
        `${error.code || "SERVER_ERROR"}`,
        `ERP Error.Message:${errorMessage}`,
        statusCode
      )
    );
  }
};

export {
  createCustomersController,
  customerManualSyncController,
  getSalesPersonController,
  createSalesPersonController,
  deleteSalesPersonController,
};
