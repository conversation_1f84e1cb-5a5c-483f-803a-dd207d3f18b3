import mongoose from "mongoose";

const cronLogSchema = new mongoose.Schema(
  {
    startTime: { type: Date, required: true },
    endTime: { type: Date, default: null },
    triggeredBy: { type: String, enum: ["cron", "manual"], required: true },
    totalProducts: {
      type: Number,
    },
    processedProducts: {
      type: Number,
    },
    completedPages: {
      type: Number,
    },
    status: {
      type: String,
      enum: ["running", "success", "failed"],
      default: "running",
    },
    duration: { type: Number, default: 0 }, // in milliseconds
  },
  { timestamps: true }
);

const CronLog = mongoose.model("CronLog", cronLogSchema);
export default CronLog;
