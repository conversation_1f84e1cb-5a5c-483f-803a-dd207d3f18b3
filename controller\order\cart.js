import Cart from "../../model/order/cart.model.js";
import AppError from "../../utils/appError.js";
import catchAsync from "../../utils/catchAsync.js";
import { deleteOne, getAll, getOne, updateOne } from "../../utils/controllerFactory.js";
import fs from 'fs';
import xlsx from "xlsx";
import { filterObj, validateProductSheet, validateProductSheetHeader } from "../../utils/helperFunction.js";
import Distributor from "../../model/distributor/distributor.model.js";
import { getCompanyLocations, getCustomerInfo } from "../shopify/index.js";

export const getCarts = getAll(Cart);
export const getOneCart = getOne(Cart);
export const updateCart = updateOne(Cart);
export const deleteCart = deleteOne(Cart);

export const updateCartLineItem = catchAsync(async (req, res, next) => {
  const cartId = req.params.cartId;
  const line_items = req.body.line_items;
  if (!line_items) return next(new AppError('Missing line_items array in payload'));
  const setOperations = {}; // To store $set operations
  const arrayFilters = []; // To store array filters
  line_items.forEach((update) => {
    const { _id, ...fieldsToUpdate } = update;
    if(fieldsToUpdate.quantity <=0) {
      return next(new AppError('Quatity must be greater than 0'));
    }
    const filteredFields = filterObj(fieldsToUpdate, 'quantity')
    const filterName = `cf${_id.toLowerCase().replace(/[^a-z0-9]+/g, '')}`;
    // Unique name for array filter
    arrayFilters.push({ [`${filterName}._id`]: _id }); // Define array filter
    // Create $set operations for each key-value pair in the update
    Object.entries(filteredFields).forEach(([key, value]) => {
      setOperations[`line_items.$[${filterName}].${key}`] = value;
    });
  });
  const updateResult = await Cart.updateOne(
    { _id: cartId }, // Find the document by ID
    { $set: setOperations }, // Apply the set operations
    { arrayFilters } // Apply the defined array filters
  );
  res.status(200).json({
    status: 'success',
    data: updateResult
  });
});

export const deleteCartLineItem = catchAsync(async (req, res) => {
  const cartId = req.params.cartId;
  const lineItemId = req.params.lineItemId;
  const updateResult = await Cart.updateOne(
    { _id: cartId },
    { $pull: { line_items: { _id: lineItemId } } }
  );
  res.status(200).json({
    status: 'success',
    data: updateResult
  });
});

export const createCartFromSheet = async (req, res, next) => {
  //! Add Validation for sheet header keys
  const shippingAddressId = req.body.shippingAddressId;
  const billingAddressId = req.body.billingAddressId;

  if (!req.file) return next(new AppError('No file attached'));
  const filePath = req.file.path;
  const customerId = req.body.customerId;
  if (!customerId) return next(new AppError('Missing customerId', 400));
  if (!billingAddressId || !shippingAddressId) return next(new AppError('Missing shippingAddressId or billingAddressId', 400));
  const workbook = xlsx.readFile(filePath);

  // Convert the first sheet to JSON
  const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
  const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);
  console.log(jsonData, " jsonDataaaaaaaaaaaaa")
  const incorrectHeaderKeys = validateProductSheetHeader(jsonData[0]);
  if (incorrectHeaderKeys.length > 0) return next(new AppError(`Invalid format or missing following header keys [${incorrectHeaderKeys}]`, 400));
  const validationResponse = await validateProductSheet(jsonData);
  if (!validationResponse.returnCode == 0) return next(new AppError(validationResponse.message, 400));
  const inventoryItems = validationResponse.inventoryData;

  const customerInfo = await getCustomerInfo(`gid://shopify/Customer/${customerId}`);
  console.log('customerInfo', customerInfo);
  if (!customerInfo.companyInfo?.id) return next(new AppError(`Provided customer ${customerId} is not associated with an company`));

  const companyLocations = await getCompanyLocations(customerInfo.companyInfo?.id);
  const addresses = companyLocations.length > 0 ? companyLocations[0] : {};
  const shippingAddress = addresses.shippingAddress;
  const billingAddress = addresses.billingAddress;
  console.log('companyLocations', companyLocations);
  const distributor = await Distributor.findOne({ shopifyCompanyId: customerInfo.companyInfo?.id?.split('/')?.pop() * 1 });

  const lineItemPayload = inventoryItems.map((inventoryItem) => {
    const requestedLineItem = jsonData.find((data) => {
      return inventoryItem.sku === data.ProductCode;
    });
    if (requestedLineItem) {
      return {
        shopifyVariantId: inventoryItem.shopifyVariantId,
        shopifyProductId: inventoryItem.shopifyProductId,
        productTitle: inventoryItem.productTitle,
        variantTitle: inventoryItem.variantTitle,
        price: inventoryItem.price,
        sku: inventoryItem.sku,
        quantity: requestedLineItem.Quantity || inventoryItem.quantity,
        image: inventoryItem.image,

      };
    }
  }).filter(it => it);
  const cartPayload = {
    shopifyCompanyId: customerInfo.companyInfo?.id?.split('/')?.pop() * 1,
    distributor: distributor._id,
    line_items: lineItemPayload,
    shipping_address: shippingAddressId,
    billing_address: billingAddressId,
    uploadedSheetName: filePath.split('/').pop(),
    // uploadedSheetPath: filePath,
    customer: {
      shopifyCustomerId: customerId,
      email: customerInfo.customer?.email,
      name: customerInfo.customer?.displayName,
      phone: customerInfo.customer?.phone,
    }
  };
  const existingActiveCarts = await Cart.find({"customer.shopifyCustomerId": customerId, status: {$in: ['CREATED', 'ABANDONED']} })
  const existingActiveCartIds = existingActiveCarts.map((existingActiveCart) => {
    return existingActiveCart._id;
  })
  const deletedCart = await Cart.deleteMany({_id: existingActiveCartIds}) 
  const createdCart = await Cart.insertMany(cartPayload);
  //*delete file
  fs.unlink(filePath, (err) => {
    if (err) {
      console.error('Error deleting file:', err);
    } else {
      console.log('File deleted successfully');
    }
  });
  res.status(200).json({
    status: 'success',
    createdCart,
  });
};


// single product add to cart
export const productAddToCart = async (req, res, next) => {
  const { shippingAddressId, billingAddressId, customerId, line_items } = req.body;

  if (!customerId) return next(new AppError('Missing customerId', 400));
  if (!billingAddressId || !shippingAddressId || !line_items) return next(new AppError('Missing shippingAddressId or billingAddressId or line_items', 400));

  const validationResponse = await validateProductSheet(line_items);
  
  if (!validationResponse.returnCode == 0) return next(new AppError(validationResponse.message, 400));
  if (validationResponse.inventoryData.length <= 0) return next(new AppError("Provided SKU not found in inventory data.", 400));

  const inventoryItems = validationResponse.inventoryData;
  const customerInfo = await getCustomerInfo(`gid://shopify/Customer/${customerId}`);
  if (!customerInfo.companyInfo?.id) return next(new AppError(`Provided customer ${customerId} is not associated with an company`));

  const companyLocations = await getCompanyLocations(customerInfo.companyInfo?.id);
  const addresses = companyLocations.length > 0 ? companyLocations[0] : {};
  const distributor = await Distributor.findOne({ shopifyCompanyId: customerInfo.companyInfo?.id?.split('/')?.pop() * 1 });

  const lineItemPayload = inventoryItems.map((inventoryItem) => {
    const requestedLineItem = line_items.find((data) => {
      return inventoryItem.sku === data.ProductCode;
    });
    if (requestedLineItem) {
      return {
        shopifyVariantId: inventoryItem.shopifyVariantId,
        shopifyProductId: inventoryItem.shopifyProductId,
        productTitle: inventoryItem.productTitle,
        variantTitle: inventoryItem.variantTitle,
        price: inventoryItem.price,
        sku: inventoryItem.sku,
        quantity: requestedLineItem.Quantity || inventoryItem.quantity,
        image: inventoryItem.image,

      };
    }
  }).filter(it => it);
  const cartPayload = {
    shopifyCompanyId: customerInfo.companyInfo?.id?.split('/')?.pop() * 1,
    distributor: distributor._id,
    line_items: lineItemPayload,
    shipping_address: shippingAddressId,
    billing_address: billingAddressId,
    // uploadedSheetPath: filePath,
    customer: {
      shopifyCustomerId: customerId,
      email: customerInfo.customer?.email,
      name: customerInfo.customer?.displayName,
      phone: customerInfo.customer?.phone,
    }
  };



  // Find existing active carts for the customer
  let existingActiveCarts = await Cart.find({
      "customer.shopifyCustomerId": customerId,
      status: { $in: ['CREATED', 'ABANDONED'] }
  });


  console.log(existingActiveCarts, " existingActiveCarts");

  if (existingActiveCarts && existingActiveCarts.length > 0) {
      // If there are existing active carts, find the first one
      let cart = existingActiveCarts[0];

      // Update or add line items in the existing cart
      line_items.forEach(newItem => {
          const existingItem = cart.line_items.find(item => item.sku === newItem.ProductCode);
          if (existingItem) {
              // Update quantity if the item already exists
              existingItem.quantity += newItem.Quantity;
          } else {
              const newItemWithSKU = {
                ...newItem,
                sku: newItem.ProductCode,
            };
            delete newItemWithSKU.ProductCode; // Optionally, remove the old key
    
            // Add new item if it does not exist in the cart
            cart.line_items.push(newItemWithSKU);
          }
      });


    console.log(cart.line_items, " line itemsssss");

      // Save the updated cart using findByIdAndUpdate
      cart = await Cart.findByIdAndUpdate(cart._id, { line_items: cart.line_items }, { new: true });

      res.status(200).json({
          status: 'success',
          cart
      });
  } else {
      // If no existing active cart, create a new cart
      // const newCart = new Cart({
      //     customer: {
      //         shopifyCustomerId: customerId
      //     },
      //     line_items: line_items,
      //     status: 'CREATED'
      // });

      // Save the new cart
      // const savedCart = await newCart.save();
      const createdCart = await Cart.insertMany(cartPayload);
    console.log(cartPayload, " cartPayloadcartPayloadcartPayloadcartPayloadcartPayload");

      res.status(200).json({
          status: 'success',
          cart: createdCart
      });
  }
}