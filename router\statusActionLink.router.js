import express from "express";
import {
  getStatusActionLink,
  createStatusActionLink,
  getOneStatusActionLink,
  updateStatusActionLink,
  deleteStatusActionLink,
} from "../controller/statusActionLink.controller.js";

const router = express.Router();

router.route("/").get(getStatusActionLink).post(createStatusActionLink);

router
  .route("/:id")
  .get(getOneStatusActionLink)
  .patch(updateStatusActionLink)
  .delete(deleteStatusActionLink);

export default router;
