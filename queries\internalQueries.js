const UPDATE_PRODUCT_VARIANTS = `
      mutation UpdateProductVariantsOptionValuesInBulk($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
        productVariantsBulkUpdate(productId: $productId, variants: $variants) {
          productVariants {
            id
            sku
            price
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

const GET_SHOPIFY_CUSTOMER = `query GetCustomer ($customerId: ID!) {
      customer(id: $customerId) {
        id
        createdAt
        email
        displayName
        firstName
        lastName
        metafields(first: 10) {
         edges {
            node {
              namespace
              key
              value
              id
            }
          }
        }
      }
    }`;

const GET_SHOPIFY_ORDER = `query getOrder($orderId: ID!) {
            order(id: $orderId) {
              customer {
                id
              }
              lineItems(first: 100) {
                edges {
                  node {
                    sku
                    quantity
                    title
                  
                  }
                }
              }
              metafields(first: 10) {
                    edges {
                        node  {
                          namespace
                          key
                          value
                        }
                    }
                }
    }
}`;

const UPDATE_SHOPIFY_CUSTOMER = `mutation  customerUpdate($input: CustomerInput!) {
      customerUpdate(
        input: $input
      ) {
        customer {
          id
         
        }
      }
    }`;

const GET_SHOPIFY_LOCATIONS = `{
  locations(first: 250) {
    edges {
      node {
        id
        name
       
      }
    }
  }
}`;

const CREATE_LOCATION_SHOPIFY = `mutation MyMutation($input: LocationAddInput!) {
        locationAdd(input: $input) {
          location {
            id
          }
          userErrors {
            code
            field
            message
          }
        }
      }`;

const INVENTORY_SET_QUANTITIES = `mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
      inventorySetQuantities(input: $input) {
        inventoryAdjustmentGroup {
    
          changes {
            name
            delta
            quantityAfterChange
          }
        }
        userErrors {
          code
          field
          message
        }
      }
    }`;

const REFUND_CREATE = `mutation RefundCreate($input: RefundInput!) {
      refundCreate(input: $input) {
        refund {
          id
          refundLineItems(first: 100) {
            edges {
              node {
                id
                quantity
                lineItem {
                  id
                  quantity
                  sku
                }
              }
            }
          }
        }
        userErrors {
          field
          message
        }
      }
    }`;

const CUSTOMER_COMPANY_DETAILS = `query getCustomerCompanyAndLocation($customerId: ID!) {
      customer(id: $customerId) {
        companyContactProfiles {
          company {
            id
            locations(first: 10) {
              edges {
                node {
                  id
                  roleAssignments(first: 10) {
                    edges {
                      node {
                      companyContact {
                          id
                          isMainContact
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }`;

const COMPANY_CONTACT_ROLES = `query GetCompanyContactRoles($companyContactId: ID!) {
  companyContact(id: $companyContactId) {
    roleAssignments(first: 10) {
      edges {
        node {
          id
          role {
            id
            name
          }
        
        }
      }
    }
  }
}`;

const COMPANY_REVOKE_ROLE = `mutation companyLocationRevokeRoles($companyLocationId: ID!, $rolesToRevoke: [ID!]!) {
  companyLocationRevokeRoles(companyLocationId: $companyLocationId, rolesToRevoke: $rolesToRevoke) {
    revokedRoleAssignmentIds
    userErrors {
      field
      message
    }
  }
}`;

//moved from distributor controller
const COMPANY_LOCATION_QUERY = `query getCustomerCompanyAndLocation($customerId: ID!) {
  customer(id: $customerId) {
    companyContactProfiles {
      company {
        id
        locations(first: 10) {
          edges {
            node {
              id
            }
          }
        }
      }
    }
  }
}
`;

const CONTACT_ROLES_QUERY = `query getCompanyContactRoles($companyId: ID!) {
  company(id: $companyId) {
    contactRoles(first: 10) {
      edges {
        node {
          id
          name
        }
      }
    }
  }
}
`;

const CONTACTS_QUERY = `query getCompanyContacts($companyId: ID!) {
  company(id: $companyId) {
    contacts(first: 10) {
      edges {
        node {
          isMainContact
          customer {
            id
          }
          id
        }
      }
    }
  }
}
`;

const ASSIGN_ROLE_MUTATION = `mutation AssignRoleToCompanyLocation(
  $locationId: ID!,
  $orderingOnlyRoleId: ID!,
  $companyContactId: ID!
) {
  companyLocationAssignRoles(
    companyLocationId: $locationId,
    rolesToAssign: [
      {
        companyContactRoleId: $orderingOnlyRoleId,
        companyContactId: $companyContactId
      }
    ]
  ) {
    roleAssignments {
      id
      role {
        id
        name
      }
    }
    userErrors {
      field
      message
    }
  }
}
`;

const VERIFY_META_TRUE_MUTATION = `mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
  metafieldsSet(metafields: $metafields) {
    metafields {
      key
      namespace
      value
      createdAt
      updatedAt
    }
    userErrors {
      field
      message
      code
    }
  }
}
`;

const VERIFY_META_FALSE_MUTATION = `
        mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
          metafieldsSet(metafields: $metafields) {
            metafields {
              key
              namespace
              value
              createdAt
              updatedAt
            }
            userErrors {
              field
              message
              code
            }
          }
        }
      `;

const CHECK_COMPANY_QUERY = `
      query getCustomerCompanyAndLocation($customerId: ID!) {
        customer(id: $customerId) {
          companyContactProfiles {
            company {
              id
              locations(first: 10) {
                edges {
                  node {
                    id
                  }
                }
              }
            }
          }
        }
      }
    `;
const LOCATION_ADDRESS_MUTATION = `
    mutation companyLocationAssignAddress(
      $address: CompanyAddressInput!, 
      $addressTypes: [CompanyAddressType!]!, 
      $locationId: ID!
    ) {
      companyLocationAssignAddress(
        address: $address
        addressTypes: $addressTypes
        locationId: $locationId
      ) {
        addresses {
    address1
  }
        userErrors {
          field
          message
        }
      }
    }
  `;

const MARK_ORDER_PAID = `mutation markOrderPaid ($orderId:ID!){
  orderMarkAsPaid(input: {id: $orderId}) {
    order {
      id
    }
    userErrors {
      field
      message
    }
  }
}
`;

const ORDER_FINANCIAL_STATUS = `query getOrderFinantialStatus($orderId: ID!) {
  order(id: $orderId) {
    displayFinancialStatus
    id
  }
}`;

const GET_PRODUCT_METAFIELD = `query GetProductMetafield($ids: [ID!]!) {
                nodes(ids: $ids) {
                  ... on Product {
                    id
                    metafield(key: "budget_category", namespace: "custom") {
                      value
                    }
                  }
                }
              }`;

const CREATE_DRAFT_ORDER = `
  mutation CreateDraftOrder($input: DraftOrderInput!) {
    draftOrderCreate(input: $input) {
      draftOrder {
        id
        invoiceUrl
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const DELETE_DRAFT_ORDER = `
  mutation draftOrderDelete($input: DraftOrderDeleteInput!) {
    draftOrderDelete(input: $input) {
      deletedId
      userErrors {
        field
        message
      }
    }
  }
`;

const CONFIRM_DRAFT_ORDER = `
  mutation ConfirmDraftOrder($id: ID!) {
    draftOrderComplete(id: $id) {
      draftOrder {
        id
        status
      }
      userErrors {
        field
        message
      }
    }
  }
`;

const VARIANT_BY_SKU = `
    query GetVariantBySKU($sku: String!) {
      productVariants(first: 1, query: $sku) {
        edges {
          node {
            id
            sku
            title
          }
        }
      }
    }
  `;

const GET_CATALOGS_FROM_COMPANY = `
  query GetCatalogsFromCompany($companyId: ID!) {
    company(id: $companyId) {
      locations(first: 1) {
        edges {
          node {
            catalogs(first: 1) {
              edges {
                node {
                  id
                }
              }
            }
          }
        }
      }
    }
  }
`;

const GET_VARIANTS_FROM_CATALOG = `
  query GetCatalogPrices($catalogId: ID!, $cursor: String) {
  catalog(id: $catalogId) {
    priceList {
      prices(first: 200, after: $cursor) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            price {
              amount
              currencyCode
            }
            variant {
              id
              title
              price
              sku
              availableForSale 
              product {
                id
                title
              }
            }
          }
        }
      }
    }
  }
}
`;
const GET_ALL_PRODUCTS_AND_VARIANTS = `
  query GetAllProductsAndVariantsWithPagination($cursor: String) {
    products(first: 200, after: $cursor, query: "status:active") {
      edges {
        cursor
        node {
          id
          title
          variants(first: 250) {
            edges {
              node {
                id
                title
                sku
                price
              }
            }
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

const GET_VARIANT_PRICE_FROM_CATALOG = `
  query GetVariantPriceFromCatalog($catalogId: ID!, $variantQuery: String!) {
    catalog(id: $catalogId) {
      id
      priceList {
        prices(first: 1, query: $variantQuery) {
          edges {
            node {
              price {
                amount
                currencyCode
              }
              compareAtPrice {
                amount
                currencyCode
              }
            }
          }
        }
      }
    }
  }
`;

const GET_VARIANT_PRICE = `
query GetVariantPrice($variantId: ID!) { 
  productVariant(id: $variantId) { 
    id 
    price 
    compareAtPrice 
  } 
}
`;

const INVENTORY_ACTIVATE = `
      mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!) {
        inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId) {
          inventoryLevel { id }
          userErrors { field message }
        }
 }`;

const GET_INVENTORY_ITEM_ID = `
query getInventoryItemId($variantId: ID!) {
  productVariant(id: $variantId) {
    inventoryItem {
      id
    }
  }
}
`;

const CREATE_PRODUCT_WITH_VARIANTS = `
mutation MyMutation($input: ProductSetInput!) {
  productSet(input: $input) {
    product {
      id
      title
      variants(first: 250) {
        nodes {
          id
          price
          sku
          inventoryItem {
             id
          }
        }
      }
    }
    userErrors {
      code
      field
      message
    }
  }
}
`;

const CREATE_VARIANT = `
 mutation CreateProductVariants($productId: ID!, $variantsInput: [ProductVariantsBulkInput!]!) {
  productVariantsBulkCreate(productId: $productId, variants: $variantsInput) {
    productVariants {
      id
      title
      sku
      inventoryItem {
        id
      }
      selectedOptions {
        name
        value
      }
    }
    userErrors {
      field
      message
    }
  }
}
`;

const UPDATE_VARIANT = `
mutation UpdateVariant($productId: ID!, $variantsInput: [ProductVariantsBulkInput!]!) {
      productVariantsBulkUpdate(productId: $productId, variants: $variantsInput) {
        productVariants {
          id
          title
          sku
          price
          inventoryItem {
             id
          }
          selectedOptions {
            name
            value
          }
        }
        userErrors {
          field
          message
        }
      }
    }
`;

const GET_LOCATION_BY_NAME = `
query GetLocationByName($name: String!) {
  locations(first: 1, query: $name) {
    edges {
      node {
        id
        name
        address {
          address1
          address2
          city
          province
          country
          zip
        }
      }
    }
  }
} `;

const GET_PRODUCT_DETAILS = `
    query GetProduct($productId: ID!) {
      product(id: $productId) {
        id
        tags
        metafields(first: 10) {
          edges {
            node {
              namespace
              key
              value
              type
            }
          }
        }
      }
    }
  `;

const GET_PUBLICATIONS = `
    query GetPublications {
      publications(first: 10) {
        edges {
          node {
            id
            name
          }
        }
      }
    }
`;

const PUBLISH_PRODUCT = `
    mutation publishablePublish($id: ID!, $input: [PublicationInput!]!) {
      publishablePublish(id: $id, input: $input) {
        publishable {
          availablePublicationsCount {
            count
          }
          resourcePublicationsCount {
            count
          }
        }
        shop {
          publicationCount
        }
        userErrors {
          field
          message
        }
      }
    }
`;

const GET_ORDERS_FULFILLMENT = `
query GetFulfillmentOrders($orderId: ID!, $cursor: String) {
  order(id: $orderId) {
    id
    name
    fulfillmentOrders(first: 250) {
      pageInfo {
        hasNextPage
        endCursor
      }
      edges {
        node {
          id
          status
          lineItems(first: 250,after: $cursor) {
            pageInfo {
              hasNextPage
              endCursor
            }
            edges {
              node {
                id
                remainingQuantity
                lineItem {
                  title
                  id
                  quantity
                }
              }
            }
          }
        }
      }
    }
  }
}
`;

const FULFILLMENT_CREATE = `
mutation FulfillmentCreate($fulfillment: FulfillmentInput!) {
  fulfillmentCreate(fulfillment: $fulfillment) {
    fulfillment {
      id
      status
      totalQuantity
    }
    userErrors {
      field
      message
    }
  }
}
`;

// Query to update product tags
const UPDATE_PRODUCT_TAGS = `
  mutation updateProduct($input: ProductInput!) {
    productUpdate(input: $input) {
      product {
        id
        tags
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// For smart collection automation (from createCollectionsFromProductMetafields.controller.js)
const SMART_GET_PRODUCT_METAFIELD_DEFINITIONS = `
  query SmartGetProductMetafieldDefinitions($cursor: String) {
    metafieldDefinitions(first: 100, after: $cursor, ownerType: PRODUCT) {
      pageInfo { hasNextPage endCursor }
      edges {
        node {
          id
          name
          key
          namespace
          type { name }
        }
      }
    }
  }
`;

const SMART_GET_PRODUCTS_WITH_METAFIELDS = `
  query SmartGetProducts($cursor: String) {
    products(first: 100, after: $cursor) {
      pageInfo { hasNextPage endCursor }
      edges {
        cursor
        node {
          id
          title
          metafields(first: 20) {
            edges {
              node {
                namespace
                key
                value
                id
              }
            }
          }
        }
      }
    }
  }
`;

const SMART_GET_ALL_COLLECTIONS = `
  query SmartGetAllCollections($cursor: String) {
    collections(first: 100, after: $cursor) {
      pageInfo { hasNextPage endCursor }
      edges {
        node {
          id
          title
        }
      }
    }
  }
`;

const SMART_CREATE_COLLECTION = `
  mutation SmartCollectionCreate($input: CollectionInput!) {
    collectionCreate(input: $input) {
      userErrors { field message }
      collection { id title handle updatedAt productsCount { count } }
    }
  }
`;

const UPDATE_PRODUCT_METAFIELD = `
  mutation SetProductMetafields($metafields: [MetafieldsSetInput!]!) {
    metafieldsSet(metafields: $metafields) {
      metafields {
        id
        namespace
        key
        value
      }
      userErrors {
        field
        message
      }
    }
  }
`;

export const GET_PRODUCTS = `
  query getProducts($cursor: String) {
    products(first: 250, after: $cursor) {
      pageInfo { hasNextPage }
      edges {
        cursor
        node {
          id
          tags
          metafields(first: 20, namespace: "custom") {
            edges {
              node {
                id
                key
                namespace
                value
                type
              }
            }
          }
        }
      }
    }
  }
`;


export {
  UPDATE_PRODUCT_VARIANTS,
  UPDATE_SHOPIFY_CUSTOMER,
  GET_SHOPIFY_CUSTOMER,
  GET_SHOPIFY_ORDER,
  GET_SHOPIFY_LOCATIONS,
  CREATE_LOCATION_SHOPIFY,
  INVENTORY_SET_QUANTITIES,
  REFUND_CREATE,
  CUSTOMER_COMPANY_DETAILS,
  COMPANY_CONTACT_ROLES,
  COMPANY_REVOKE_ROLE,
  COMPANY_LOCATION_QUERY,
  CONTACT_ROLES_QUERY,
  CONTACTS_QUERY,
  ASSIGN_ROLE_MUTATION,
  VERIFY_META_TRUE_MUTATION,
  VERIFY_META_FALSE_MUTATION,
  CHECK_COMPANY_QUERY,
  LOCATION_ADDRESS_MUTATION,
  MARK_ORDER_PAID,
  ORDER_FINANCIAL_STATUS,
  GET_PRODUCT_METAFIELD,
  CREATE_DRAFT_ORDER,
  DELETE_DRAFT_ORDER,
  CONFIRM_DRAFT_ORDER,
  VARIANT_BY_SKU,
  GET_CATALOGS_FROM_COMPANY,
  GET_VARIANTS_FROM_CATALOG,
  GET_ALL_PRODUCTS_AND_VARIANTS,
  GET_VARIANT_PRICE_FROM_CATALOG,
  GET_VARIANT_PRICE,
  CREATE_VARIANT,
  GET_INVENTORY_ITEM_ID,
  CREATE_PRODUCT_WITH_VARIANTS,
  INVENTORY_ACTIVATE,
  UPDATE_VARIANT,
  GET_LOCATION_BY_NAME,
  GET_PRODUCT_DETAILS,
  GET_PUBLICATIONS,
  PUBLISH_PRODUCT,
  GET_ORDERS_FULFILLMENT,
  FULFILLMENT_CREATE,
  UPDATE_PRODUCT_TAGS,
  SMART_GET_PRODUCT_METAFIELD_DEFINITIONS,
  SMART_GET_PRODUCTS_WITH_METAFIELDS,
  SMART_GET_ALL_COLLECTIONS,
  SMART_CREATE_COLLECTION,
  UPDATE_PRODUCT_METAFIELD
};
