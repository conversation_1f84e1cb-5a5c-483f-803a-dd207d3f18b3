import Distributor from "../model/distributor.model.js";
import Order from "../model/order.model.js";
import Status from "../model/status.model.js";

export async function searchApi(req, res) {
  try {
    const {
      input,
      fromDate,
      toDate,
      page = 1,
      pageSize = 10,
      budgetCategoryValue,
      preOrderValue,
      status,
    } = req.body;
    const user = req.user;
    const salesPersonDept = req.salesPersonDept;

    const pipeline = [];

    const isValidDate = (dateString) => !isNaN(Date.parse(dateString));
    let distributorIds = [];
    if (salesPersonDept) {
      // If the user is a salesperson, get the distributors associated with them.
      distributorIds = await Distributor.distinct("_id", {
        salespersonId: user._id.toString(),
      });

      // If no distributors are found, return an empty response.
      if (!distributorIds.length) {
        return res.send({ total: 0, page, pageSize, orders: [] });
      }

      // Add salesperson-specific distributor filter to the pipeline.
      pipeline.push({
        $match: {
          distributor: { $in: distributorIds },
        },
      });
    }

    if (input) {
      const statusResult = await Status.findOne({
        status: input.toUpperCase(),
      });
      const distributorResult = await Distributor.findOne({ name: input });

      const statusCondition = statusResult
        ? { status: statusResult._id }
        : null;
      const distributorCondition = distributorResult
        ? { distributor: distributorResult._id }
        : null;
      const nameCondition = {
        $or: [
          { name: { $regex: input, $options: "i" } },
          { subOrderId: { $regex: input, $options: "i" } },
        ],
      };

      const conditions = [];

      if (statusCondition) conditions.push(statusCondition);
      if (distributorCondition) conditions.push(distributorCondition);

      conditions.push(nameCondition);

      pipeline.push({
        $match: {
          $or: conditions,
        },
      });
    }

    if (fromDate || toDate) {
      const dateMatch = {};
      if (fromDate && isValidDate(fromDate)) {
        dateMatch.$gte = new Date(fromDate);
      }
      if (toDate && isValidDate(toDate)) {
        const end = new Date(toDate);
        end.setHours(23, 59, 59, 999);
        dateMatch.$lte = end;
      }

      if (Object.keys(dateMatch).length > 0) {
        pipeline.push({
          $match: {
            createdAt: dateMatch,
          },
        });
      }
    }

    if (budgetCategoryValue !== undefined && budgetCategoryValue !== "") {
      pipeline.push({
        $match: {
          budgetCategoryValue: budgetCategoryValue,
        },
      });
    }

    if (preOrderValue !== undefined && preOrderValue !== "") {
      pipeline.push({
        $match: {
          preOrderValue: preOrderValue,
        },
      });
    }
    if (status !== undefined && status !== "") {
      pipeline.push({
        $match: {
          $or: [{ status: status }],
        },
      });
    }

    // Lookup for status
    pipeline.push({
      $lookup: {
        from: "status",
        localField: "status",
        foreignField: "_id",
        as: "statusDetails",
      },
    });

    // Lookup for distributor
    pipeline.push({
      $lookup: {
        from: "distributors",
        localField: "distributor",
        foreignField: "_id",
        as: "distributorDetails",
      },
    });

    // Unwind arrays to objects
    pipeline.push(
      { $unwind: { path: "$statusDetails", preserveNullAndEmptyArrays: true } },
      {
        $unwind: {
          path: "$distributorDetails",
          preserveNullAndEmptyArrays: true,
        },
      }
    );

    // Sort by createdAt in descending order
    pipeline.push({
      $sort: { createdAt: -1 },
    });

    // Pagination stages
    const skip = (page - 1) * pageSize;
    pipeline.push({ $skip: skip }, { $limit: pageSize });

    // Add final projection to include full objects
    pipeline.push({
      $replaceRoot: {
        newRoot: {
          $mergeObjects: [
            "$$ROOT",
            { status: "$statusDetails" },
            { distributor: "$distributorDetails" },
          ],
        },
      },
    });

    const orders = await Order.aggregate(pipeline);

    const countPipeline = pipeline.slice(0, -4);
    countPipeline.push({ $count: "total" });

    // Add distributor filter for count if salesperson.
    if (salesPersonDept) {
      countPipeline.unshift({
        $match: {
          distributor: { $in: distributorIds },
        },
      });
    }

    const totalResult = await Order.aggregate(countPipeline);
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    res.send({ total, page, pageSize, orders });
  } catch (error) {
    res.status(500).send("Something went wrong.");
  }
}
