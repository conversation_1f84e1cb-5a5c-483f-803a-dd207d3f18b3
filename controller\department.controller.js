import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import Department from "../model/department.model.js";
import { authenticate } from "./departmentAuthenticate.controller.js";
import {
  createOne,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import catchAsync from "../utils/catchAsync.js";
import Distributor from "../model/distributor.model.js";
import Designation from "../model/designation.model.js";
import NewAppError from "../utils/newAppError.js";
import XLSX from "xlsx";
import fs from "fs";

export const getDepartments = getAll(Department);
export const getOneDepartment = getOne(Department);
export const createDepartment = catchAsync(async (req, res) => {
  const { password } = req.body;
  let hashedPassword = password;
  if (password) {
    hashedPassword = await bcrypt.hash(password, 10);
  }
  const user = new Department({
    ...req.body,
    password: hashedPassword,
  });

  const savedUser = await user.save();
  delete savedUser.password;
  res.status(201).json(savedUser);
});

export const hashPassword = async (req, res, next) => {
  const { password } = req.body;
  let hashedPassword = password;
  if (password) {
    hashedPassword = await bcrypt.hash(password, 10);
  }
  req.body.password = hashedPassword;
  next();
};

export const updateDepartment = catchAsync(async (req, res) => {
  const doc = await Department.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  const { distributor, designation } = doc;

  if (distributor && designation) {
    const countryManager = req.body._id;
    const foundDesignation = await Designation.findById(designation);

    if (foundDesignation && foundDesignation.isCountryManager) {
      const existingDistributors = await Distributor.find({
        countryManager: { $in: [countryManager] },
      });

      for (const distributorId of distributor) {
        const foundDistributor = await Distributor.findById(distributorId);

        if (foundDistributor) {
          if (!foundDistributor.countryManager.includes(countryManager)) {
            foundDistributor.countryManager.push(countryManager);
            await foundDistributor.save();
          }
        }
      }

      // Step 3: Remove countryManager from distributors that are NOT in the new req.body.distributor list
      for (const existingDist of existingDistributors) {
        if (!distributor.includes(existingDist._id.toString())) {
          // Remove countryManager from distributors no longer in the req.body.distributor list
          existingDist.countryManager = existingDist.countryManager.filter(
            (id) => id.toString() !== countryManager
          );
          await existingDist.save();
        }
      }
    }
  }

  res.status(200).json({
    status: "success",
    result: 1,
    data: {
      data: doc,
    },
  });
});

export const deleteDepartment = deleteOne(Department);

// Controller function for people login
export const loginDepartmentPeople = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const user = await authenticate(email, password);
  if (!user) {
    return res.status(401).json({ error: "Invalid email or password" });
  }

  const token = jwt.sign(
    { userId: user._id, email: user.email, name: user.name, type: user.type },
    process.env.JWT_TOKEN_SECRET,
    { expiresIn: "8h" }
  );

  return res.json({ token, user });
});

export const findPeopleWithKey = catchAsync(async (req, res) => {
  const { key } = req.params;

  if (!key) {
    return NewAppError("Invalid key", "Key is required", "400");
  }

  const query = { salespersonkey: key };

  const data = await Department.find(query);

  return res.status(200).json({
    status: "success",
    message: "Data fetched successfully",
    data: data,
  });
});

export const createDepartmentFromExcel = catchAsync(async (req, res, next) => {
  if (!req.file) {
    return next(
      new NewAppError("VALIDATION_ERROR", "Please upload an Excel file.", 400)
    );
  }

  // Read the Excel file
  const workbook = XLSX.readFile(req.file.path);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];

  // Convert sheet to JSON
  const data = XLSX.utils.sheet_to_json(worksheet);

  const successfulRecords = [];
  const errorRecords = [];

  // Process each row
  for (const row of data) {
    const errors = [];

    // Validate mandatory fields
    if (!row["Salesperson ACCPAC Name"])
      errors.push("Salesperson ACCPAC Name is required");
    if (!row["Salesperson Email"]) errors.push("Salesperson Email is required");
    if (!row["Salesperson ACCPAC Key"])
      errors.push("Salesperson ACCPAC Key is required");
    if (!row["Dummy Password"]) errors.push("Dummy Password is required");

    // Check if email already exists
    if (row["Salesperson Email"]) {
      const existingUser = await Department.findOne({
        email: row["Salesperson Email"],
      });
      if (existingUser) {
        errors.push("Email already exists");
      }
    }

    // Check if Salesperson ACCPAC Key already exists
    if (row["Salesperson ACCPAC Key"]) {
      const existingKey = await Department.findOne({
        salespersonkey: row["Salesperson ACCPAC Key"],
      });
      if (existingKey) {
        errors.push("Salesperson ACCPAC Key already exists");
      }
    }

    if (errors.length > 0) {
      errorRecords.push({
        ...row,
        errors: errors.join(", "),
      });
      continue;
    }

    const hashedPassword = await bcrypt.hash(row["Dummy Password"], 10);
    const departmentData = {
      name: row["Salesperson ACCPAC Name"],
      email: row["Salesperson Email"],
      password: hashedPassword,
      departmentType: "66d843673104241b8c339634",
      salespersonkey: row["Salesperson ACCPAC Key"],
    };

    successfulRecords.push(departmentData);
  }

  // Save valid records to database
  if (successfulRecords.length > 0) {
    await Department.insertMany(successfulRecords);
  }

  // Clean up the uploaded file - do it once before any return
  fs.unlinkSync(req.file.path);

  // Handle error records if any
  if (errorRecords.length > 0) {
    const errorWorkbook = XLSX.utils.book_new();
    // Update error sheet headers
    const errorSheet = XLSX.utils.json_to_sheet([
      ...errorRecords.map((record, index) => ({
        "Salesperson Email": record["Salesperson Email"] || "",
        "Salesperson ACCPAC Name": record["Salesperson ACCPAC Name"] || "",
        "Salesperson ACCPAC Key": record["Salesperson ACCPAC Key"] || "",
        "Dummy Password": record["Dummy Password"] || "",
        "Error Message": record.errors,
      })),
    ]);

    XLSX.utils.book_append_sheet(errorWorkbook, errorSheet, "Errors");

    // Write to buffer instead of file
    const buffer = XLSX.write(errorWorkbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    // Convert buffer to base64
    const base64File = buffer.toString("base64");

    return res.status(207).send({
      responseCode: 0,
      status: "success",
      data: {
        errorFile: base64File,
        successCount: successfulRecords.length,
        errorCount: errorRecords.length,
        message: "Process completed with some errors",
      },
    });
  }

  return res.status(200).send({
    responseCode: 0,
    status: "success",
    data: {
      successCount: successfulRecords.length,
      message: "All records processed successfully",
    },
  });
});

export const getSalesPerson = catchAsync(async (req, res) => {
  const salesPersons = await Department.aggregate([
    {
      $match: {
        type: "deptt_people",
      },
    },
    {
      $lookup: {
        from: "departmenttypes",
        localField: "departmentType",
        foreignField: "_id",
        as: "departmentType",
      },
    },
    {
      $unwind: "$departmentType",
    },
    {
      $match: {
        "departmentType.pseudoId": "SALES_PERSON",
      },
    },
    {
      $project: {
        name: 1,
        email: 1,
        salespersonkey: 1,
        departmentDetails: 1,
      },
    },
  ]);
  return res.status(200).json({
    responseCode: 0,
    data: {
      message: "Record fetched successfully",
      salesPersons,
    },
  });
});
