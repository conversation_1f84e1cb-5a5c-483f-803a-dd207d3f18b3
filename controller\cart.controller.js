import Cart from "../model/cart.model.js";
import AppError from "../utils/appError.js";
import catchAsync from "../utils/catchAsync.js";
import {
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import fs from "fs";
import xlsx from "xlsx";
import {
  filterObj,
  validateAndCreateInventory,
  validateProductSheet,
  validateProductSheetHeader,
} from "../utils/helperFunction.js";
import Distributor from "../model/distributor.model.js";
import { getCompanyLocations, getCustomerInfo } from "./shopify.controller.js";
import Inventory from "../model/inventory.model.js";
import { checkForDuplicates } from "./order.controller.js";
import { getPricesForCustomerAndSkus } from "../helpers/priceHelper.js";

export const getCarts = getAll(Cart);
export const getOneCart = getOne(Cart);
export const updateCart = updateOne(Cart);
export const deleteCart = deleteOne(Cart);

export const updateCartLineItem = catchAsync(async (req, res, next) => {
  const cartId = req.params.cartId;
  const line_items = req.body.line_items;
  if (!line_items)
    return next(new AppError("Missing line_items array in payload"));
  const setOperations = {}; // To store $set operations
  const arrayFilters = []; // To store array filters
  line_items.forEach((update) => {
    const { _id, ...fieldsToUpdate } = update;
    if (fieldsToUpdate.quantity <= 0) {
      return next(new AppError("Quatity must be greater than 0"));
    }
    const filteredFields = filterObj(fieldsToUpdate, "quantity");
    const filterName = `cf${_id.toLowerCase().replace(/[^a-z0-9]+/g, "")}`;
    // Unique name for array filter
    arrayFilters.push({ [`${filterName}._id`]: _id }); // Define array filter
    // Create $set operations for each key-value pair in the update
    Object.entries(filteredFields).forEach(([key, value]) => {
      setOperations[`line_items.$[${filterName}].${key}`] = value;
    });
  });
  const updateResult = await Cart.updateOne(
    { _id: cartId }, // Find the document by ID
    { $set: setOperations }, // Apply the set operations
    { arrayFilters } // Apply the defined array filters
  );
  res.status(200).json({
    status: "success",
    data: updateResult,
  });
});

export const deleteCartLineItem = catchAsync(async (req, res) => {
  const cartId = req.params.cartId;
  const lineItemId = req.params.lineItemId;
  const updateResult = await Cart.updateOne(
    { _id: cartId },
    { $pull: { line_items: { _id: lineItemId } } }
  );
  res.status(200).json({
    status: "success",
    data: updateResult,
  });
});

export const createCustomProductCart = async (req, res, next) => {
  const {
    name,
    description,
    price,
    customerId,
    shippingAddressId,
    billingAddressId,
    quantity,
  } = req.body;
  if (!name || !description || !quantity || quantity < 0)
    return next(
      new AppError(
        "Missing any of the following required field [name or description or quantity]"
      )
    );

  if (!customerId) return next(new AppError("Missing customerId", 400));
  // if (!billingAddressId || !shippingAddressId)
  //   return next(
  //     new AppError("Missing shippingAddressId or billingAddressId", 400)
  //   );
  const customerInfo = await getCustomerInfo(
    `gid://shopify/Customer/${customerId}`
  );
  if (!customerInfo.companyInfo?.id)
    return next(
      new AppError(
        `Provided customer ${customerId} is not associated with an company`
      )
    );

  // const companyLocations = await getCompanyLocations(
  //   customerInfo.companyInfo?.id
  // );

  const distributor = await Distributor.findOne({
    shopifyCustomerId: parseInt(customerId),
  });
  //Create iventory
  //Get Esisting Cart if yes update orcreate
  let existingActiveCarts = await Cart.find({
    "customer.shopifyCustomerId": customerId,
    status: { $in: ["CREATED", "ABANDONED"] },
  });
  const lineItemPayload = {
    shopifyVariantId: "n/a",
    shopifyProductId: "n/a",
    productTitle: name,
    variantTitle: name,
    inventoryType: "CUSTOM",
    description: description,
    price: price || 0,
    sku: `${name.split(" ").join("_")}_${Math.floor(Math.random() * 100000)}`,
    quantity: 0,
    image:
      "https://cdn.shopify.com/s/files/1/0656/0969/5370/files/Titan-Watches-Logo.jpg?v=1725868130",
  };

  const customInventory = await Inventory.create(lineItemPayload);
  lineItemPayload.quantity = quantity;

  if (!distributor) return next(new AppError("Invalid Distributor Id.", 400));

  const cartPayload = {
    shopifyCompanyId: customerInfo.companyInfo?.id?.split("/")?.pop() * 1,
    distributor: distributor._id,
    line_items: lineItemPayload,
    // shipping_address: shippingAddressId,
    // billing_address: billingAddressId,
    description: description,
    // uploadedSheetPath: filePath,
    customer: {
      shopifyCustomerId: customerId,
      email: customerInfo.customer?.email,
      name: customerInfo.customer?.displayName,
      phone: customerInfo.customer?.phone,
    },
  };
  if (existingActiveCarts && existingActiveCarts.length > 0) {
    let cart = existingActiveCarts[0];
    [lineItemPayload].forEach((newItem) => {
      const existingItem = cart.line_items.find(
        (item) => item.sku === newItem.sku
      );
      if (existingItem) {
        existingItem.quantity += newItem.quantity;
      } else {
        cart.line_items.push(newItem);
      }
    });
    cart = await Cart.findByIdAndUpdate(
      cart._id,
      { line_items: cart.line_items },
      { new: true }
    );
    res.status(200).json({
      status: "success",
      cart,
      customInventory: customInventory,
    });
  } else {
    const createdCart = await Cart.insertMany(cartPayload);
    res.status(200).json({
      status: "success",
      cart: createdCart,
      customInventory: customInventory,
    });
  }
};

export const createCartFromSheet = async (req, res, next) => {
  //! Add Validation for sheet header keys
  // const shippingAddressId = req.body.shippingAddressId;
  // const billingAddressId = req.body.billingAddressId;

  if (!req.file) return next(new AppError("No file attached"));
  const filePath = req.file.path;
  const customerId = req.body.customerId;
  if (!customerId) return next(new AppError("Missing customerId", 400));
  // if (!billingAddressId || !shippingAddressId)
  //   return next(
  //     new AppError("Missing shippingAddressId or billingAddressId", 400)
  //   );
  const workbook = xlsx.readFile(filePath);

  // Convert the first sheet to JSON
  const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
  let jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

  const hasDuplicates = checkForDuplicates(jsonData);
  if (hasDuplicates)
    return next(new AppError("Duplicate SKU entries found.", 400));

  const orderWithInvalidQuantity = jsonData.filter((order) => {
    const quantity = order.quantity ?? order.Quantity;
    return isNaN(quantity) || quantity < 0;
  });

  jsonData = jsonData.filter((x) => x.quantity || x.Quantity);

  if (jsonData.length === 0) {
    return next(
      new AppError("At least 1 item should have more than 0 quantity", 400)
    );
  }

  if (orderWithInvalidQuantity.length > 0) {
    return next(new AppError("Some Items have Invalid quantity", 400));
  }

  const incorrectHeaderKeys = validateProductSheetHeader(jsonData[0]);
  if (incorrectHeaderKeys.length > 0)
    return next(
      new AppError(
        `Invalid format or missing following header keys [${incorrectHeaderKeys}]`,
        400
      )
    );
  const validationResponse = await validateProductSheet(jsonData);
  if (!validationResponse.returnCode == 0)
    return next(new AppError(validationResponse.message, 400));
  let inventoryItems = validationResponse.inventoryData;

  const customerInfo = await getCustomerInfo(
    `gid://shopify/Customer/${customerId}`
  );

  if (!customerInfo.companyInfo?.id)
    return next(
      new AppError(
        `Provided customer ${customerId} is not associated with an company`
      )
    );

  const companyLocations = await getCompanyLocations(
    customerInfo.companyInfo?.id
  );
  const addresses = companyLocations.length > 0 ? companyLocations[0] : {};
  const shippingAddress = addresses.shippingAddress;
  const billingAddress = addresses.billingAddress;
  const distributor = await Distributor.findOne({
    shopifyCustomerId: customerId,
  });

  if (!distributor)
    return next(new AppError(`Provided customer detail didn't exits.`));

  const createdInventories = await validateAndCreateInventory(jsonData);
  inventoryItems = [...inventoryItems, ...createdInventories];
  const lineItemPayload = inventoryItems
    .map((inventoryItem) => {
      // const requestedLineItem = jsonData.find((data) => {
      //   return inventoryItem.sku === data.ProductCode;
      // });
      // if (requestedLineItem) {
        return {
          shopifyVariantId: inventoryItem.shopifyVariantId,
          shopifyProductId: inventoryItem.shopifyProductId,
          productTitle: inventoryItem.productTitle,
          variantTitle: inventoryItem.variantTitle,
          price: inventoryItem.price,
          sku: inventoryItem.sku,
          quantity: inventoryItem.quantity,
          image: inventoryItem.image,
          productCategory: inventoryItem.productCategory,
        };
      // }
    })
    .filter((it) => it);
  const cartPayload = {
    shopifyCompanyId: customerInfo.companyInfo?.id?.split("/")?.pop() * 1,
    distributor: distributor._id,
    line_items: lineItemPayload,
    // shipping_address: shippingAddressId,
    // billing_address: billingAddressId,
    uploadedSheetName: filePath.split("/").pop(),
    // uploadedSheetPath: filePath,
    customer: {
      shopifyCustomerId: customerId,
      email: customerInfo.customer?.email,
      name: customerInfo.customer?.displayName,
      phone: customerInfo.customer?.phone,
    },
  };
  const existingActiveCarts = await Cart.find({
    "customer.shopifyCustomerId": customerId,
    status: { $in: ["CREATED", "ABANDONED"] },
  });
  const existingActiveCartIds = existingActiveCarts.map(
    (existingActiveCart) => {
      return existingActiveCart._id;
    }
  );
  const deletedCart = await Cart.deleteMany({ _id: existingActiveCartIds });
  const createdCart = await Cart.insertMany(cartPayload);
  //*delete file
  fs.unlink(filePath, (err) => {
    if (err) {
      console.error("Error deleting file:", err);
    } else {
      console.log("File deleted successfully");
    }
  });
  res.status(200).json({
    status: "success",
    createdCart,
  });
};

// single product add to cart
// export const productAddToCart = async (req, res, next) => {
//   const { shippingAddressId, billingAddressId, customerId, line_items } = req.body;

//   if (!customerId) return next(new AppError("Missing customerId", 400));
//   if (!billingAddressId || !shippingAddressId || !line_items)
//     return next(
//       new AppError(
//         "Missing shippingAddressId or billingAddressId or line_items",
//         400
//       )
//     );

//   const validationResponse = await validateProductSheet(line_items);

//   if (!validationResponse.returnCode == 0)
//     return next(new AppError(validationResponse.message, 400));
//   if (validationResponse.inventoryData.length <= 0)
//     return next(new AppError("Provided SKU not found in inventory data.", 400));

//   const inventoryItems = validationResponse.inventoryData;
//   const customerInfo = await getCustomerInfo(
//     `gid://shopify/Customer/${customerId}`
//   );
//   if (!customerInfo.companyInfo?.id)
//     return next(
//       new AppError(
//         `Provided customer ${customerId} is not associated with an company`
//       )
//     );

//   const companyLocations = await getCompanyLocations(
//     customerInfo.companyInfo?.id
//   );
//   const addresses = companyLocations.length > 0 ? companyLocations[0] : {};
//   const distributor = await Distributor.findOne({
//     shopifyCompanyId: customerInfo.companyInfo?.id?.split("/")?.pop() * 1,
//   });

//   const lineItemPayload = inventoryItems
//     .map((inventoryItem) => {
//       const requestedLineItem = line_items.find((data) => {
//         return inventoryItem.sku === data.ProductCode;
//       });
//       if (requestedLineItem) {
//         return {
//           shopifyVariantId: inventoryItem.shopifyVariantId,
//           shopifyProductId: inventoryItem.shopifyProductId,
//           productTitle: inventoryItem.productTitle,
//           variantTitle: inventoryItem.variantTitle,
//           price: inventoryItem.price,
//           sku: inventoryItem.sku,
//           quantity: requestedLineItem.Quantity || inventoryItem.quantity,
//           image: inventoryItem.image,
//         };
//       }
//     })
//     .filter((it) => it);

//   const cartPayload = {
//     shopifyCompanyId: customerInfo.companyInfo?.id?.split("/")?.pop() * 1,
//     distributor: distributor._id,
//     line_items: lineItemPayload,
//     shipping_address: shippingAddressId,
//     billing_address: billingAddressId,
//     // uploadedSheetPath: filePath,
//     customer: {
//       shopifyCustomerId: customerId,
//       email: customerInfo.customer?.email,
//       name: customerInfo.customer?.displayName,
//       phone: customerInfo.customer?.phone,
//     },
//   };

//   let existingActiveCarts = await Cart.find({
//     "customer.shopifyCustomerId": customerId,
//     status: { $in: ["CREATED", "ABANDONED"] },
//   });

//   if (existingActiveCarts && existingActiveCarts.length > 0) {
//     let cart = existingActiveCarts[0];

//     line_items.forEach((newItem) => {
//       const existingItem = cart.line_items.find(
//         (item) => item.sku === newItem.ProductCode
//       );

//       if (existingItem) {
//         existingItem.quantity += newItem.Quantity;
//       } else {

//         const newData = lineItemPayload.find(
//           (item) => item.sku === newItem.ProductCode
//         );
//         cart.line_items.push(newData);
//       }
//     });

//     cart = await Cart.findByIdAndUpdate(
//       cart._id,
//       { line_items: cart.line_items },
//       { new: true }
//     );

//     res.status(200).json({
//       status: "success",
//       cart,
//     });
//   } else {

//     const createdCart = await Cart.insertMany(cartPayload);

//     res.status(200).json({
//       status: "success",
//       cart: createdCart,
//     });
//   }
// };

// single product add to cart
export const productAddToCart = async (req, res, next) => {
  const { shippingAddressId, billingAddressId, customerId, line_items } =
    req.body;

  if (!customerId) return next(new AppError("Missing customerId", 400));
  // if (!billingAddressId || !shippingAddressId || !line_items)
  //   return next(
  //     new AppError(
  //       "Missing shippingAddressId or billingAddressId or line_items",
  //       400
  //     )
  //   );

  const validationResponse = await validateProductSheet(line_items);

  if (!validationResponse.returnCode == 0)
    return next(new AppError(validationResponse.message, 400));
  if (validationResponse.inventoryData.length <= 0)
    return next(new AppError("Provided SKU not found in inventory data.", 400));

  const inventoryItems = validationResponse.inventoryData;
  const customerInfo = await getCustomerInfo(
    `gid://shopify/Customer/${customerId}`
  );
  if (!customerInfo.companyInfo?.id)
    return next(
      new AppError(
        `Provided customer ${customerId} is not associated with an company`
      )
    );

  const companyLocations = await getCompanyLocations(
    customerInfo.companyInfo?.id
  );
  const addresses = companyLocations.length > 0 ? companyLocations[0] : {};
  const distributor = await Distributor.findOne({
    shopifyCustomerId: parseInt(customerId),
  });

  if (!distributor) {
    return next(new AppError(`Provided customer ${customerId} doesn't exist!`));
  }

  const lineItemPayload = await Promise.all(
    inventoryItems.map(async (inventoryItem, index) => {
      const requestedLineItem = line_items.find((data) => {
        return inventoryItem.sku === data.ProductCode;
      });

      let newPriceData;

      if (requestedLineItem) {
        newPriceData = await getPricesForCustomerAndSkus(customerId, [
          inventoryItem.sku,
        ]);

        let newPrice = inventoryItem.price;
        const priceItem = newPriceData.find(
          (item) => item.sku === inventoryItem.sku
        );

        if (priceItem) {
          newPrice = priceItem.price;
          console.log(priceItem, "new price");
        }

        return {
          shopifyVariantId: inventoryItem.shopifyVariantId,
          shopifyProductId: inventoryItem.shopifyProductId,
          productTitle: inventoryItem.productTitle,
          variantTitle: inventoryItem.variantTitle,
          price: newPrice,
          sku: inventoryItem.sku,
          quantity: requestedLineItem.Quantity || inventoryItem.quantity,
          image: inventoryItem.image
            ? inventoryItem.image
            : "https://cdn.shopify.com/s/files/1/0656/0969/5370/files/Titan-Watches-Logo.jpg?v=1725868130",
          productCategory: line_items[index].productCategory,
        };
      }
    })
  );

  // Filter out any undefined values
  const filteredLineItemPayload = lineItemPayload.filter((it) => it);

  console.log(
    filteredLineItemPayload,
    "lineItemPayload,lineItemPayload,lineItemPayload"
  );

  console.log(
    lineItemPayload,
    "lineItemPayload,lineItemPayload,lineItemPayload"
  );

  const cartPayload = {
    shopifyCompanyId: customerInfo.companyInfo?.id?.split("/")?.pop() * 1,
    distributor: distributor._id,
    line_items: lineItemPayload,
    // shipping_address: shippingAddressId,
    // billing_address: billingAddressId,
    // uploadedSheetPath: filePath,
    customer: {
      shopifyCustomerId: customerId,
      email: customerInfo.customer?.email,
      name: customerInfo.customer?.displayName,
      phone: customerInfo.customer?.phone,
    },
  };

  let existingActiveCarts = await Cart.find({
    "customer.shopifyCustomerId": customerId,
    status: { $in: ["CREATED", "ABANDONED"] },
  });

  if (existingActiveCarts && existingActiveCarts.length > 0) {
    let cart = existingActiveCarts[0];

    line_items.forEach((newItem) => {
      const existingItem = cart.line_items.find(
        (item) => item.sku === newItem.ProductCode
      );

      if (existingItem) {
        existingItem.quantity += newItem.Quantity;
      } else {
        const newData = lineItemPayload.find(
          (item) => item.sku === newItem.ProductCode
        );
        console.log(newData, " new Data new Data");
        cart.line_items.push(newData);
      }
    });

    cart = await Cart.findByIdAndUpdate(
      cart._id,
      { line_items: cart.line_items },
      { new: true }
    );

    res.status(200).json({
      status: "success",
      cart: cart,
    });
  } else {
    const createdCart = await Cart.insertMany(cartPayload);

    res.status(200).json({
      status: "success",
      cart: createdCart[0],
    });
  }
};
