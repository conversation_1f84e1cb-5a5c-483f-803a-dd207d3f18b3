import Department from "../model/department.model.js";
import DepartmentType from "../model/departmentType.model.js";
import Status from "../model/status.model.js";

export const getDepartmentNotifiedEmails = async (nextStatus) => {
  let departmentNotified = [];

  if (nextStatus && Array.isArray(nextStatus.departmentNotified)) {
    let departmentDetails = await Promise.all(
      nextStatus.departmentNotified.map(async (notified) => {
        const departmentData = await DepartmentType.findById(notified._id);
        const departmentPeople = await Department.find({
          departmentType: { $in: [departmentData._id] },
        });

        if (departmentPeople.length > 0) {
          return departmentPeople.map((person) => person.email);
        }
      })
    );
    departmentNotified = departmentDetails.flat();
  }

  return departmentNotified;
};

export const getDepartmentForCreated = async (statusId) => {
  if (!statusId) {
    return;
  }

  const status = await Status.findById(statusId).populate();
  let departmentDetails = [];

  if (status && Array.isArray(status.departmentNotified)) {
    departmentDetails = await Promise.all(
      status.departmentNotified.map(async (notified) => {
        const departmentData = await DepartmentType.findById(notified._id);
        const departmentPeople = await Department.find({
          departmentType: departmentData._id,
        });
        if (departmentPeople.length > 0) {
          return departmentPeople.map((person) => person.email);
        }
      })
    );
    departmentDetails = departmentDetails.flat();
  }
  return departmentDetails;
};
