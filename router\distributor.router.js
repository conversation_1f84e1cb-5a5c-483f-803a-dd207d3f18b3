import express from "express";
import {
  getDistributors,
  createDistributor,
  getOneDistributor,
  updateDistributor,
  updateDistributorInOmsErpController,
  deleteDistributor,
  updateDistributorPriority,
  createCustomerInShopify,
  checkIfDistributorHasOrderOrNot,
  removeEmailFromPayload,
  updateDistributorInShopify,
  changeCustomerStatus,
  banCustomer,
  getDistributorStatusCount,
  filterDistributors,
  checkCompany,
  getDeptWiseDistributors,
  updateCustomerFiles,
  downloadDistributorSheet,
  uploadDistributorSheet,
} from "../controller/distributor.controller.js";
import { generateToken } from "../utils/helperFunction.js";
import { hashPassword } from "../controller/department.controller.js";
import { authenticateDepttPeopleAccessModuleWise } from "../middlewares/authenticateDepttPeople.js";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import { generateRandomNumber } from "../utils/helperFunction.js";
const router = express.Router();

const generateAuthToken = (req, res, next) => {
  const token = generateToken({
    shopifyCustomerId: req.body.shopifyCustomerId,
    customerType: "distributor",
  });
  req.body.token = token;
  next();
};

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "company"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".").pop();
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Renames the file
  },
});
const upload = multer({
  storage,
}).fields([
  { name: "form9" },
  { name: "form24" },
  { name: "form49" },
  { name: "memArticle" },
  { name: "icCard" },
  { name: "lan" },
  { name: "bankStatement" },
  { name: "formD" },
  { name: "form1" },
]);

const uploadSheet = multer({
  storage: multer.memoryStorage(),
}).fields([{ name: "sheet", maxCount: 1 }]);

router
  .route("/")
  .get(
    authenticateDepttPeopleAccessModuleWise("distributor", "list", "read"),
    getDeptWiseDistributors
  )
  .post(
    createCustomerInShopify,
    hashPassword,
    generateAuthToken,
    createDistributor
  );
router
  .route("/filterDistributors")
  .get(
    authenticateDepttPeopleAccessModuleWise("distributor", "list", "read"),
    filterDistributors
  );
router
  .route("/changeCustomerStatus")
  .post(
    authenticateDepttPeopleAccessModuleWise("distributor", "approve"),
    changeCustomerStatus
  );
router
  .route("/banCustomer")
  .post(
    authenticateDepttPeopleAccessModuleWise("distributor", "rejectCustomer"),
    banCustomer
  );
router
  .route("/getDistributorStatusCount")
  .get(
    authenticateDepttPeopleAccessModuleWise("distributor", "list", "read"),
    getDistributorStatusCount
  );
// router.route('/updateCustomerNumber').post(updateCustomerNumber)

router.route("/checkCompany").post(checkCompany);
router.route("/priority/update").patch(updateDistributorPriority);

router
  .route("/downloadDistributorSheet")
  .get(
    authenticateDepttPeopleAccessModuleWise("distributor", "editDetails"),
    downloadDistributorSheet
  );
router
  .route("/uploadDistributorSheet")
  .post(
    authenticateDepttPeopleAccessModuleWise("distributor", "editDetails"),
    uploadSheet,
    uploadDistributorSheet
  );

router
  .route("/:id")
  .get(getOneDistributor)
  .patch(
    authenticateDepttPeopleAccessModuleWise("distributor", "editDetails"),
    removeEmailFromPayload,
    updateDistributorInShopify,
    updateDistributorInOmsErpController
  )
  .delete(checkIfDistributorHasOrderOrNot, deleteDistributor)
  .post(upload, updateCustomerFiles);

export default router;
