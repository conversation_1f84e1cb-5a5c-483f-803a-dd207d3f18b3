import axios from "axios";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CUSTOMERPRICELIST,
  TAXCLASSCODE1,
  PRINTSTATEMENTS,
  CUSTOMEROPTIONALFIELD,
  CUSTOMEROPTIONALFIELDVALUE,
  CUSTOMER_DUMMY_DATA,
  NAMESPACE,
  TER<PERSON><PERSON>RYCODE,
} from "../../constants/erpConstants.js";
import Distributor from "../../model/distributor.model.js";
import Department from "../../model/department.model.js";
import { getSyncRecordFromDB, saveSyncRecord } from "../ERPSync/syncRecord.js";
import CustomerErrorLog from "../../model/erp.customer.error.model.js";
import {
  GET_SHOPIFY_CUSTOMER,
  UPDATE_SHOPIFY_CUSTOMER,
  CUSTOMER_COMPANY_DETAILS,
  COMPANY_CONTACT_ROLES,
  COMPANY_RE<PERSON><PERSON>_ROLE,
  ASSIGN_ROLE_MUTATION,
  CONTACT_ROLES_QUERY,
  CONTACTS_QUERY,
} from "../../queries/internalQueries.js";
import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";
import NewAppError from "../../utils/newAppError.js";

const getCustomerDataDBHandler = async (customerIds) => {
  try {
    const customers = await Distributor.find({
      shopifyCustomerId: { $in: customerIds },
    });

    if (!customers || customers.length <= 0) {
      return {
        status: 404,
        code: "NOT_FOUND",
        message: `No customers found for the given customerIds: ${customerIds}`,
      };
    }

    return {
      status: 200,
      data: { customers },
    };
  } catch (error) {
    console.log("error getCustomerDataDBHandler", error);
    return {
      status: 500,
      code: "SERVER_ERROR",
      message: `Server Error`,
    };
  }
};

const formattedCustomerDataHandler = async (customerData, salespersons) => {
  try {
    const customers = customerData.data.customers || [];

    const formattedCustomersArray = customers
      .filter((customer) => customer?.customerNumber)
      .map((customer) => {
        const salesperson = salespersons.find(
          (person) => person._id.toString() === customer.salespersonId
        );
        return {
          CustomerName: customer?.name || "",
          CustomerNumber: customer.customerNumber || "",
          BusinessRegistrationNumber: customer?.companyDetails?.brn || "",
          GroupCode: GROUPCODE || "",
          TerritoryCode: "",
          AddressLine1: customer?.companyDetails?.businessAddress || "",
          AddressLine2: customer?.companyDetails?.businessAddress || "",
          PhoneNumber: customer?.phone || "",
          Email: customer?.email || "",
          ContactName: customer?.name || "",
          ContactsPhone: customer?.phone || "",
          City: customer?.companyDetails?.city || "",
          StateProvince: customer?.companyDetails?.state || "",
          ZipPostalCode: customer?.companyDetails?.pincode || "",
          Country: customer?.companyDetails?.country || "",
          Salesperson1: salesperson.salespersonkey || "",
          CustomerPriceList: CUSTOMERPRICELIST || "",
          CreditBureauRating: "C",
          Terms: TERMSCODE || "",
          TaxGroup: TAXGROUP || "",
          TaxClassCode1: TAXCLASSCODE1 || "",
          PrintStatements: PRINTSTATEMENTS,
          TerritoryCode: TERRITORYCODE,
          CustomerOptionalFieldValues: [
            {
              OptionalField: CUSTOMEROPTIONALFIELD,
              Value: CUSTOMEROPTIONALFIELDVALUE,
            },
          ],
        };
      });
    if (!formattedCustomersArray?.length) {
      return {
        status: 400,
        code: "INVALID_INPUT",
        message: `No valid customers to create.`,
      };
    }

    return {
      status: 200,
      data: { formattedCustomers: formattedCustomersArray },
    };
  } catch (error) {
    console.log("error formattedCustomerDataHandler", error);
    return {
      status: 500,
      code: "SERVER_ERROR",
      message: `Server Error while Formating customer.Error:${error?.message}`,
    };
  }
};

const createERPCustomerHandler = async (customerData) => {
  try {
    const {
      CustomerNumber,
      CustomerName,
      GroupCode,
      AddressLine1,
      AddressLine2,
      PhoneNumber,
      Email,
      ContactName,
      ContactsPhone,
      City,
      StateProvince,
      ZipPostalCode,
      Country,
      Salesperson1,
      CreditBureauRating,
      Terms,
      TaxGroup,
      TaxClassCode1,
      TerritoryCode,
      PrintStatements,
    } = customerData;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      Company: process.env.SAGE_COMPANY,
      APIKey: process.env.SAGE_CUSTOM_API_KEY,
      CustomerNumber,
      CustomerName,
      GroupCode,
      AddressLine1,
      AddressLine2,
      PhoneNumber,
      Email,
      ContactName,
      ContactsPhone,
      City,
      StateProvince,
      ZipPostalCode,
      Country,
      Salesperson1,
      CreditBureauRating,
      Terms,
      TaxGroup,
      TaxClassCode1,
      TerritoryCode,
      PrintStatements,
    });

    const config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${
        process.env.SAGE_CUSTOM_API_ENDPOINT
      }/api/ARCustomer/PostARCustomer?${queryParams.toString()}`,
      headers: {
        "Content-Type": "text/plain",
        Accept: "text/plain",
      },
    };

    const response = await axios.request(config);
    if (response?.data?.includes("CustomerID already exists")) {
      return {
        status: 400,
        code: "CUSTOMER_EXISTS",
        message: "CustomerId already exists.",
      };
    }

    return {
      status: response?.status || 200,
      data: {
        customerNumber: response?.data?.CustomerNumber || CustomerNumber,
        message: response.data,
      },
    };
  } catch (error) {
    const errorData = error?.response?.data;
    const errorMessage =
      errorData?.error?.message?.value ||
      errorData?.message ||
      error.message ||
      "Unknown error during ERP customer creation";

    // Check for customer already exists patterns in the error message
    const isCustomerExists =
      errorMessage.toLowerCase().includes("customer already exists") ||
      errorMessage.toLowerCase().includes("duplicate customer") ||
      errorMessage.toLowerCase().includes("customer number already exists") ||
      errorMessage.toLowerCase().includes("already in use") ||
      errorData?.error?.code === "DUPLICATE_KEY" ||
      error?.response?.status === 409; // Conflict status code

    return {
      status: error?.response?.status || 500,
      code: isCustomerExists
        ? "CUSTOMER_EXISTS"
        : errorData?.error?.code || "ERP_ERROR",
      message: isCustomerExists
        ? "Customer already exists in ERP system. Please use a different customer number."
        : errorMessage,
    };
  }
};

const customerUpdateScheduledService = async (manual = false) => {
  let nextLink;
  try {
    const syncRecord = await getSyncRecordFromDB();
    const lastSyncDate = syncRecord?.customerlastSyncDate || new Date(0);
    const lastSyncDateFormatted = lastSyncDate
      .toISOString()
      .replace(".000Z", "+00:00");
    let retryCount = 0;
    let skipValue = 0;

    const select =
      "$select=CustomerName,Status,CustomerNumber,BusinessRegistrationNumber,GroupCode,TerritoryCode,AddressLine1,AddressLine2,PhoneNumber,Email,ContactName,ContactsPhone,City,StateProvince,ZipPostalCode,Country,Salesperson1,CustomerPriceList,CreditBureauRating";
    const filter = `$filter=DateLastMaintained gt ${lastSyncDateFormatted}`;

    const baseURL = `${process.env.SAGE_API_ENDPOINT}/AR/ARCustomers?${select}&${filter}`;

    while (true) {
      nextLink = `${baseURL}&$skip=${skipValue}`;
      let config = {
        method: "get",
        maxBodyLength: Infinity,
        url: nextLink,
        headers: {
          "Content-Type": `${process.env.API_CONTENT_TYPE}`,
          Accept: `${process.env.API_CONTENT_TYPE}`,
          Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
        },
      };

      let response;
      try {
        response = await axios.request(config);

        // response = CUSTOMER_DUMMY_DATA;
      } catch (error) {
        console.error("Error fetching customers:", error.message);
        retryCount++;
        await logCustomerErrorInDB(
          null,
          null,
          manual,
          `Batch Error. Unable to fetch customers.Message:${error.message}.BatchLink:${nextLink}.`
        );

        if (retryCount > 1) {
          throw Error(
            `Batch Error. Unable to fetch customers.Message:${error.message}.BatchLink:${nextLink}.`
          );
          break;
        }

        continue;
      }

      const customers = response?.data?.value || [];
      if (!customers.length) {
        break;
      }

      console.log(
        `Retrieved ${customers.length} customers in the current batch.`
      );

      await processCustomerData(customers, manual);

      const nextLinkURL = response.data["@odata.nextLink"];
      if (!nextLinkURL) {
        console.log("No more data to fetch. Stopping the loop.");
        break;
      }

      // Extract skip value from nextLinkURL
      const urlParams = new URLSearchParams(nextLinkURL.split("?")[1]);
      skipValue = urlParams.get("$skip") || skipValue;

      retryCount = 0;
    }

    const currentSyncDate = new Date().toISOString();
    await saveSyncRecord(currentSyncDate, "customer");
    if (manual) {
      return { status: 200, message: `Sync completed.` };
    }
  } catch (error) {
    console.error(`Error in customerUpdateScheduledService: ${error.message}.`);
    await logCustomerErrorInDB(
      null,
      null,
      manual,
      `Critical error in scheduled service function customerUpdateScheduledService.Message: ${error.message}.Link:${nextLink}`
    );
    if (manual) {
      console.log(error.message, "error.message");
      return { status: 500, error: error.message };
    }
  }
};

const processCustomerData = async (customerDataArray, manual) => {
  for (const customerData of customerDataArray) {
    const { CustomerNumber, CustomerName } = customerData;

    try {
      await updateShopifyCustomer(customerData);
      await updateCustomerInOmsDb(customerData);
    } catch (error) {
      await logCustomerErrorInDB(
        CustomerNumber,
        CustomerName,
        manual,
        `Error processing  CustomerNumber: ${CustomerNumber}, CustomerName: ${CustomerName}. Error: ${error.message}`
      );

      console.error(
        `Error processing  CustomerNumber: ${CustomerNumber}, CustomerName: ${CustomerName}. Error: ${error.message}`
      );
    }
  }
};

const updateCustomerInOmsDb = async (customerData) => {
  try {
    let status =
      customerData.Status === "Active"
        ? "Customer Created in Sage"
        : customerData.Status;
    const updatedDistributor = await Distributor.findOneAndUpdate(
      { customerNumber: customerData.CustomerNumber },
      {
        status: status,
      },
      { new: true }
    );
    if (!updatedDistributor) {
      throw Error("Distributor not found for the given customer number.");
    }
    console.log(`Updated customer ${customerData.CustomerNumber} in OMS.`);
    return true;
  } catch (error) {
    throw Error(
      `Failed to update OMS for customer :${customerData.CustomerNumber}.Message: ${error.message}`
    );
  }
};
const updateShopifyCustomer = async (customerData) => {
  try {
    const customer = await Distributor.findOne({
      customerNumber: customerData.CustomerNumber,
    });

    if (!customer) {
      throw Error(`No customer match in DB found for specified customer.`);
    }
    const shopifyCustomer = await getCustomerDataShopifyHandler(
      customer.shopifyCustomerId
    );
    const customerMetaData = shopifyCustomer.metafields.edges;
    let MetafieldVerified;
    if (customerMetaData.length) {
      MetafieldVerified = customerMetaData.find(
        (metafield) => metafield.node.key === "verified"
      );
    }

    const status = customerData.Status === "Active" ? "True" : "False";

    if (status === "False" && customer.status === "Customer Created in Sage") {
      const revokeCompanyLocation =
        await revokeCustomerCompanyLocationRoleService(
          customer.shopifyCustomerId
        );
      if (revokeCompanyLocation instanceof NewAppError) {
        throw Error(revokeCompanyLocation);
      }
    } else if (status === "True" && customer.status === "Inactive") {
      const assignCompanyLocation =
        await assignCustomerCompanyLocationRoleService(
          customer.shopifyCustomerId
        );

      if (assignCompanyLocation instanceof NewAppError) {
        throw Error(assignCompanyLocation);
      }
    }

    const variables = {
      input: {
        id: `gid://shopify/Customer/${customer.shopifyCustomerId}`,
        firstName: customerData.CustomerName,
        metafields: MetafieldVerified
          ? [{ id: MetafieldVerified.node.id, value: status }]
          : [],
      },
    };

    const response = await fetchGraphqlDataShopify(
      UPDATE_SHOPIFY_CUSTOMER,
      variables
    );

    if (response?.errors?.length > 0 || !response?.data?.customerUpdate) {
      throw Error("Error while updating Shopify Customer.");
    }
    const updatedCustomerId = response.data.customerUpdate.customer.id;

    console.log(`Updated customer on Shopify: ${updatedCustomerId}`);

    return true;
  } catch (error) {
    throw new Error(
      `Failed to update Shopify for customer: ${customerData.CustomerNumber}.Message: ${error.message}`
    );
  }
};

const getCustomerDataShopifyHandler = async (customerId) => {
  try {
    console.log(customerId, "customerId");
    let shopifyCustomerId = `gid://shopify/Customer/${customerId}`;

    const responseData = await fetchGraphqlDataShopify(GET_SHOPIFY_CUSTOMER, {
      customerId: shopifyCustomerId,
    });

    if (!responseData?.data?.customer || responseData?.errors?.length > 0) {
      throw Error(
        `Customer not found.customerId:${customerId}.${
          responseData?.errors && responseData?.errors[0].message
        }`
      );
    }
    const customerData = responseData.data.customer;

    return customerData;
  } catch (error) {
    throw Error(
      `Error in function getCustomerDataShopifyHandler:Message:${error.message}`
    );
  }
};

const logCustomerErrorInDB = async (
  customerNumber,
  customerName,
  manual,
  errorMessage
) => {
  try {
    const res = await CustomerErrorLog.create({
      customerNumber,
      customerName,
      manual,
      errorMessage,
      timestamp: new Date(),
    });
    console.log(`Logged error for Customer ${customerNumber}: ${errorMessage}`);
  } catch (error) {
    console.error(
      `Failed to log error for Customer ${customerNumber}: ${error.message}`
    );
  }
};

const updateCustomerService = async (customer) => {
  try {
    const updateCustomer = await updateCustomerERP(customer);
    if (updateCustomer.status !== 204) {
      return updateCustomer;
    }
    return updateCustomer;
  } catch (error) {
    console.log(error.message);
    return {
      status: "500",
      code: "SERVER_ERROR",
      message: "Could not update customer.Error while updating customer.",
    };
  }
};

const updateCustomerERP = async (customer) => {
  try {
    const salesperson = await Department.findById(customer?.salespersonId);

    const data = {
      CustomerName: customer?.name,
      Salesperson1: salesperson?.salespersonkey,
      PhoneNumber: customer?.phone,
      ContactsPhone: customer?.phone,
      BusinessRegistrationNumber: customer?.companyDetails?.brn,
      AddressLine1: customer?.companyDetails?.businessAddress,
      ZipPostalCode: customer?.companyDetails?.pincode,
      Country: customer?.companyDetails?.country,
      City: customer?.companyDetails?.city,
      StateProvince: customer?.companyDetails?.state,
    };

    const filteredData = {};
    for (const key in data) {
      if (data[key] !== undefined) {
        filteredData[key] = data[key];
      }
    }

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.SAGE_API_ENDPOINT}/AR/ARCustomers('${customer.customerNumber}')`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
      },
      data: filteredData,
    };

    const updated = await axios.request(config);

    if (updated.status !== 204) {
      return {
        status: updated.status,
        code: updated.error.code,
        message: updated.error.message.value,
      };
    }

    return {
      status: updated.status,
      data: { message: "updated successfully." },
    };
  } catch (error) {
    console.log(error.message, "Error While customer Update.");
    return {
      status: error.status,
      code: error.response?.data?.error?.code || "SERVER_ERROR",
      message:
        error.response?.data?.error?.message?.value ||
        "Error While customer Update.",
    };
  }
};

const getERPSalesPersons = async () => {
  try {
    const select = "$select=SalespersonKey,Status,Name";
    const baseUrl = `${process.env.SAGE_API_ENDPOINT}/AR/ARSalespersons?${select}`;
    let skipValue = 0;
    let allSalespersons = [];
    let nextLink;

    while (true) {
      nextLink = `${baseUrl}&$skip=${skipValue}`;

      const config = {
        method: "get",
        maxBodyLength: Infinity,
        url: nextLink,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
        },
      };

      let response;
      try {
        response = await axios.request(config);
      } catch (error) {
        console.error("Error fetching Batch salespersons:", error.message);
        throw Error(
          `Batch Error. Unable to fetch salespersons Batch .BatchLink:${nextLink}.`
        );
      }

      const salesPersons = response?.data?.value || [];
      if (!salesPersons.length) {
        break;
      }
      allSalespersons.push(...salesPersons);

      const nextLinkURL = response.data["@odata.nextLink"];
      if (!nextLinkURL) {
        break;
      }

      const urlParams = new URLSearchParams(nextLinkURL.split("?")[1]);
      skipValue = urlParams.get("$skip") || skipValue;
    }

    if (allSalespersons.length <= 0) {
      return {
        status: 404,
        message: `No salespersons found in ERP.`,
        code: `NOT_FOUND`,
      };
    }

    return {
      status: 200,
      data: { salespersons: allSalespersons },
    };
  } catch (error) {
    console.error("Error:", error.message);
    return {
      status: 500,
      message: `Error fetching salespersons from ERP.Message:${error.message}`,
      code: `SERVER_ERROR`,
    };
  }
};

//service to revoke  role from company location.This will detach company and customer

const revokeCustomerCompanyLocationRoleService = async (customerId) => {
  try {
    const customerCompanyRelatedDetails =
      await getCustomerCompanyRelatedDetailsService(customerId);
    if (customerCompanyRelatedDetails instanceof NewAppError) {
      return customerCompanyRelatedDetails;
    }

    const companyContactRoleAssignment =
      await companyContactRoleAssignmentService(
        customerId,
        customerCompanyRelatedDetails
      );

    if (companyContactRoleAssignment instanceof NewAppError) {
      return companyContactRoleAssignment;
    }

    const companyRevokedRoles = await revokeRoleCompanyService(
      customerId,
      companyContactRoleAssignment
    );
    if (companyRevokedRoles instanceof NewAppError) {
      return companyRevokedRoles;
    }

    return companyRevokedRoles;
  } catch (error) {
    console.log(error.message);
    return new NewAppError("SERVER_ERROR", error.message, 500);
  }
};

const getCustomerCompanyRelatedDetailsService = async (customerId) => {
  try {
    const shopifyCustomerId = `gid://shopify/Customer/${customerId}`;

    const responseData = await fetchGraphqlDataShopify(
      CUSTOMER_COMPANY_DETAILS,
      {
        customerId: shopifyCustomerId,
      }
    );

    if (!responseData.data.customer || responseData?.errors?.length > 0) {
      return new NewAppError(
        "SERVER_ERROR",
        `Customer not found.customerId:${customerId}.${
          responseData?.errors ? responseData?.errors[0].message : ""
        }`,
        500
      );
    }

    return responseData;
  } catch (error) {
    return new NewAppError("SERVER_ERROR", error.message, 500);
  }
};
const companyContactRoleAssignmentService = async (
  customerId,
  customerCompanyRelatedDetails
) => {
  try {
    const { companyId, companyLocationId, companyContactId } =
      getCustomerRelatedCompanyIds(customerCompanyRelatedDetails);
    if (!companyId || !companyLocationId || !companyContactId) {
      return new NewAppError(
        "SERVER_ERROR",
        `Missing customer data for Customer Id:${customerId}.`,
        500
      );
    }

    let responseData = await fetchGraphqlDataShopify(COMPANY_CONTACT_ROLES, {
      companyContactId,
    });

    if (
      responseData.data.companyContact.roleAssignments.edges.length <= 0 ||
      responseData?.errors?.length > 0
    ) {
      return new NewAppError(
        "SERVER_ERROR",
        `No company contact role for customerId:${customerId}.${
          responseData?.errors ? responseData?.errors[0].message : ""
        }`,
        500
      );
    }
    responseData.data.companyLocationId = companyLocationId;

    return responseData;
  } catch (error) {
    return new NewAppError("SERVER_ERROR", error.message, 500);
  }
};

const revokeRoleCompanyService = async (
  customerId,
  customerCompanyRelatedDetails
) => {
  try {
    const companyContactRoleAssignmentId =
      customerCompanyRelatedDetails.data.companyContact.roleAssignments.edges[0]
        .node.id;
    const companyLocationId =
      customerCompanyRelatedDetails.data.companyLocationId;

    const responseData = await fetchGraphqlDataShopify(COMPANY_REVOKE_ROLE, {
      companyLocationId,
      rolesToRevoke: [companyContactRoleAssignmentId],
    });

    if (responseData?.errors?.length > 0) {
      return new NewAppError(
        "SERVER_ERROR",
        `Error while revoking role for customerId:${customerId}.${responseData.errors[0].message}`,
        500
      );
    }
    if (responseData.data.companyLocationRevokeRoles.userErrors.length > 0) {
      return new NewAppError(
        "SERVER_ERROR",
        `Error while revoking role for customerId:${customerId}.Message:${responseData.data.companyLocationRevokeRoles.userErrors[0].message}`,
        500
      );
    }

    const revokedRoleAssignmentIds =
      responseData.data.companyLocationRevokeRoles.revokedRoleAssignmentIds;
    return {
      message: "Revoke company location role successful.",
      customerId,
      revokedRoleAssignmentIds,
    };
  } catch (error) {
    return new NewAppError("SERVER_ERROR", error.message, 500);
  }
};

//Assign role to company location

const assignCustomerCompanyLocationRoleService = async (customerId) => {
  try {
    const customerCompanyRelatedDetails =
      await getCustomerCompanyRelatedDetailsService(customerId);
    if (customerCompanyRelatedDetails instanceof NewAppError) {
      return customerCompanyRelatedDetails;
    }

    const companyContactRoleId = await companyContactRolesService(
      customerId,
      customerCompanyRelatedDetails
    );

    if (companyContactRoleId instanceof NewAppError) {
      return companyContactRoleId;
    }

    const companyContactId = await getCompanyContactService(
      customerId,
      customerCompanyRelatedDetails
    );
    if (companyContactId instanceof NewAppError) {
      return companyContactId;
    }
    const companyAssignedRoles = await assignRoleToCompanyService(
      customerId,
      companyContactRoleId,
      companyContactId,
      customerCompanyRelatedDetails
    );
    if (companyAssignedRoles instanceof NewAppError) {
      return companyAssignedRoles;
    }

    return companyAssignedRoles;
  } catch (error) {
    console.log(error.message);
    return new NewAppError("SERVER_ERROR", error.message, 500);
  }
};

const companyContactRolesService = async (
  customerId,
  customerCompanyRelatedDetails
) => {
  try {
    const { companyId } = getCustomerRelatedCompanyIds(
      customerCompanyRelatedDetails
    );
    const contactRolesResponse = await fetchGraphqlDataShopify(
      CONTACT_ROLES_QUERY,
      { companyId }
    );
    const contactRoles = contactRolesResponse.data.company.contactRoles.edges;
    const orderingOnlyRole = contactRoles.find(
      (role) => role.node.name === "Ordering only"
    );
    const orderingOnlyRoleId = orderingOnlyRole.node.id;
    return orderingOnlyRoleId;
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error getting contact role.${error.message}`,
      500
    );
  }
};

const getCompanyContactService = async (
  customerId,
  customerCompanyRelatedDetails
) => {
  try {
    const { companyId } = getCustomerRelatedCompanyIds(
      customerCompanyRelatedDetails
    );
    const contactsResponse = await fetchGraphqlDataShopify(CONTACTS_QUERY, {
      companyId,
    });

    const mainContact = contactsResponse.data.company.contacts.edges.find(
      (contact) => contact.node.isMainContact
    );
    const companyContactId = mainContact.node.id;
    return companyContactId;
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error getting company contact.${error.message}`,
      500
    );
  }
};

const assignRoleToCompanyService = async (
  customerId,
  companyContactRoleId,
  companyContactId,
  customerCompanyRelatedDetails
) => {
  try {
    const { companyLocationId } = getCustomerRelatedCompanyIds(
      customerCompanyRelatedDetails
    );

    const responseData = await fetchGraphqlDataShopify(ASSIGN_ROLE_MUTATION, {
      locationId: companyLocationId,
      orderingOnlyRoleId: companyContactRoleId,
      companyContactId,
    });

    if (responseData?.errors?.length > 0) {
      return new NewAppError(
        "SERVER_ERROR",
        `Error while assigning role for customerId:${customerId}.${responseData.errors[0].message}`,
        500
      );
    }
    if (responseData.data.companyLocationAssignRoles.userErrors.length > 0) {
      return new NewAppError(
        "SERVER_ERROR",
        `Error while assigning role for customerId:${customerId}.Message:${responseData.data.companyLocationAssignRoles.userErrors[0].message}`,
        500
      );
    }

    const assignedCompanyContactRoleIds =
      responseData.data.companyLocationAssignRoles.roleAssignments.map(
        (assignment) => assignment.role.id
      );

    return {
      message: "Assigning company location role successful.",
      customerId,
      assignedCompanyContactRoleIds,
    };
  } catch (error) {
    return new NewAppError("SERVER_ERROR", error.message, 500);
  }
};

const getCustomerRelatedCompanyIds = (customerCompanyRelatedDetails) => {
  try {
    const companyProfiles =
      customerCompanyRelatedDetails.data.customer?.companyContactProfiles;
    const companyId =
      companyProfiles?.length > 0 ? companyProfiles[0]?.company?.id : null;

    const companyLocations =
      companyProfiles?.length > 0
        ? companyProfiles[0]?.company?.locations?.edges
        : null;

    const companyLocationId =
      companyLocations?.length > 0 ? companyLocations[0]?.node?.id : null;

    const roleAssignments =
      companyLocations?.length > 0
        ? companyLocations[0]?.node?.roleAssignments?.edges
        : null;

    const companyContactId =
      roleAssignments?.length > 0
        ? roleAssignments[0]?.node?.companyContact?.id
        : null;

    return { companyId, companyLocationId, companyContactId };
  } catch {
    return new NewAppError(
      "SERVER_ERROR",
      `Error while fetching Customer Related Company Ids.Message: ${error.message} `,
      500
    );
  }
};
export {
  formattedCustomerDataHandler,
  createERPCustomerHandler,
  getCustomerDataDBHandler,
  customerUpdateScheduledService,
  updateCustomerInOmsDb,
  updateShopifyCustomer,
  updateCustomerService,
  getERPSalesPersons,
  revokeCustomerCompanyLocationRoleService,
};
