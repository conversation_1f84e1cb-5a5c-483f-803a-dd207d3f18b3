import Distributor from "../../model/distributor.model.js";
import AppError from "../../utils/appError.js";
import Department from "../../model/department.model.js";
import XLSX from "xlsx";
import { updateCustomerService } from "../ERPSync/customerService.js";

export async function GetDistributorRecord(customerId) {
  try {
    const record = await Distributor.findOne({
      shopifyCustomerId: customerId,
    }).exec();

    if (!record) {
      return null;
    }

    return record;
  } catch (error) {
    console.error(`Error in GetDbRecord: ${error.message}`);
    return null;
  }
}

export async function CreateDistributorRecord(details, customerId, companyId) {
  try {
    const newCustomerCompany = new Distributor({
      shopifyCustomerId: customerId,
      currentStep: 1,
      shopifyCompanyId: companyId,
      email: details.email,
      companyName: details.companyName,
      name: details.name,
      phone: details.phone,
      companyDetails: {
        tin: details.tin,
      },
    });

    await newCustomerCompany.save();

    console.log("New customer company record created successfully.");
    return newCustomerCompany; // Return the saved record
  } catch (error) {
    console.error(`Error in CreateDbRecord: ${error.message}`);
    throw new Error(`Could not create the record: ${error.message}`);
  }
}

export async function UpdateDistributorRecord(customerId, updateData) {
  try {
    // Fetch the existing record by customerId
    const existingRecord = await Distributor.findOne({
      shopifyCustomerId: customerId,
    });

    let toUpdateObj = { ...updateData };

    if (existingRecord) {
      const existingCurrentStep = existingRecord.currentStep || 0;
      const incomingCurrentStep = updateData.currentStep;

      // Only update `currentStep` if the incoming value is greater
      if (existingCurrentStep >= incomingCurrentStep) {
        delete toUpdateObj.currentStep;
      }

      // Update the existing record
      await Distributor.findOneAndUpdate(
        { shopifyCustomerId: customerId },
        { $set: toUpdateObj },
        { new: true }
      );
    } else {
      // Create a new record if it doesn't exist
      await Distributor.findOneAndUpdate(
        { shopifyCustomerId: customerId },
        { $set: toUpdateObj },
        { new: true, upsert: true }
      );
    }

    return true;
  } catch (error) {
    console.error(
      `Failed to update or create record for customerId ${customerId}: ${error.message}`
    );
    return false;
  }
}

export async function downloadDistributorSheetService(distributorSheet) {
  try {
    // 1. Fetch distributors from database
    const distributors = await Distributor.find({
      status: {
        $in: ["Customer Created in Sage"],
      },
    })
      .select("name email customerNumber salespersonId")
      .lean();

    // 2. Get unique salesperson IDs
    const salespersonIds = [
      ...new Set(distributors.map((d) => d.salespersonId)),
    ].filter(Boolean);

    // 3. Fetch salesperson details from Department collection
    const salespersonDetails = await Department.find({
      _id: { $in: salespersonIds },
    })
      .select("name salespersonkey")
      .lean();

    // 4. Create a lookup map for quick access to salesperson details
    const salespersonMap = salespersonDetails.reduce((acc, sp) => {
      acc[sp._id.toString()] = {
        name: sp.name,
        salespersonkey: sp.salespersonkey,
      };
      return acc;
    }, {});

    // 5. Transform data for Excel format with salesperson details
    const worksheetData = distributors
      .map((distributor) => {
        const salesperson = salespersonMap[distributor.salespersonId] || {};

        // Only create record if all required fields are present and sanitized
        if (
          distributor.name?.trim() &&
          distributor.email?.trim() &&
          distributor.customerNumber?.trim() &&
          salesperson.name?.trim() &&
          salesperson.salespersonkey?.trim()
        ) {
          return {
            "Customer Name": distributor.name.trim(),
            Email: distributor.email.trim().toLowerCase(),
            "Customer Number": distributor.customerNumber.trim(),
            "Salesperson Name": salesperson.name.trim(),
            "Salesperson Key": salesperson.salespersonkey.trim(),
            "Updated Salesperson Key": salesperson.salespersonkey.trim(),
          };
        }
        return null;
      })
      .filter(Boolean);

    // 6. Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(worksheetData);

    // 7. Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, "Distributors");

    // 8. Generate buffer
    const excelBuffer = XLSX.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    return excelBuffer;
  } catch (error) {
    console.error("Error downloading distributor sheet:", error);
    throw new AppError("Error downloading distributor sheet", 500);
  }
}

export async function uploadDistributorSheetService(fileBuffer) {
  try {
    // 1. Read Excel file
    const workbook = XLSX.read(fileBuffer, { type: "buffer" });

    // 2. Get first worksheet
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    // 3. Convert to JSON
    const distributors = XLSX.utils.sheet_to_json(worksheet);

    // 2. Validate required columns
    const requiredColumns = [
      "Customer Name",
      "Email",
      "Customer Number",
      "Salesperson Name",
      "Salesperson Key",
      "Updated Salesperson Key",
    ];

    const sheetColumns = Object.keys(distributors[0] || {});
    const missingColumns = requiredColumns.filter(
      (col) => !sheetColumns.includes(col)
    );

    if (missingColumns.length > 0) {
      throw new AppError(
        `Missing required columns: ${missingColumns.join(", ")}`,
        400
      );
    }

    // Process all rows in parallel
    const processPromises = distributors.map(async (row) => {
      try {
        // Sanitize input data
        const sanitizedRow = {
          "Customer Name": String(row["Customer Name"] || "")?.trim(),
          Email: String(row["Email"] || "")
            ?.trim()
            .toLowerCase(),
          "Customer Number": String(row["Customer Number"] || "")?.trim(),
          "Salesperson Key": String(row["Salesperson Key"] || "")?.trim(),
          "Updated Salesperson Key": String(
            row["Updated Salesperson Key"] || ""
          )?.trim(),
        };

        // Validate all required fields in the row
        const missingFields = Object.entries(sanitizedRow)
          .filter(([_, value]) => !value)
          .map(([field]) => field);

        if (missingFields.length > 0) {
          return {
            success: false,
            customerName: sanitizedRow["Customer Name"] || "N/A",
            customerNumber: sanitizedRow["Customer Number"] || "N/A",
            error: `Missing required fields: ${missingFields.join(", ")}`,
          };
        }

        // Skip if no change in salesperson key (after sanitization)
        if (
          sanitizedRow["Salesperson Key"] ===
          sanitizedRow["Updated Salesperson Key"]
        ) {
          return null;
        }

        // Find the new salesperson by key (using sanitized key)
        const newSalesperson = await Department.findOne({
          salespersonkey: sanitizedRow["Updated Salesperson Key"],
        }).select("_id name salespersonkey");

        if (!newSalesperson) {
          return {
            success: false,
            customerName: sanitizedRow["Customer Name"],
            customerNumber: sanitizedRow["Customer Number"],
            error: `Invalid Updated Salesperson Key: ${sanitizedRow["Updated Salesperson Key"]}`,
          };
        }

        // Find distributor using sanitized values
        const distributor = await Distributor.findOne({
          name: sanitizedRow["Customer Name"],
          customerNumber: sanitizedRow["Customer Number"],
          email: sanitizedRow["Email"],
        });

        if (!distributor) {
          return {
            success: false,
            customerName: sanitizedRow["Customer Name"],
            customerNumber: sanitizedRow["Customer Number"],
            error: "Customer not found",
          };
        }

        // Update in database
        const updatedDistributor = await Distributor.findByIdAndUpdate(
          distributor._id,
          {
            salespersonId: newSalesperson._id,
            $push: {
              timeline: {
                comment: `Salesperson updated from ${sanitizedRow["Salesperson Key"]} to ${sanitizedRow["Updated Salesperson Key"]}`,
                date: new Date(),
              },
            },
          },
          { new: true }
        );

        // Update in ERP
        try {
          const erpResponse = await updateCustomerService(updatedDistributor);

          if (erpResponse.status !== 204) {
            return {
              success: false,
              customerName: sanitizedRow["Customer Name"],
              customerNumber: sanitizedRow["Customer Number"],
              error: `ERP Update failed: ${erpResponse.message}`,
            };
          }
        } catch (erpError) {
          return {
            success: false,
            customerName: sanitizedRow["Customer Name"],
            customerNumber: sanitizedRow["Customer Number"],
            error: `ERP Error: ${erpError.message}`,
          };
        }

        return {
          success: true,
          customerName: sanitizedRow["Customer Name"],
          customerNumber: sanitizedRow["Customer Number"],
          oldSalespersonKey: sanitizedRow["Salesperson Key"],
          newSalespersonKey: sanitizedRow["Updated Salesperson Key"],
        };
      } catch (rowError) {
        return {
          success: false,
          customerName: row["Customer Name"]?.trim() || "N/A",
          customerNumber: row["Customer Number"]?.trim() || "N/A",
          error: rowError.message,
        };
      }
    });

    // Wait for all promises to resolve
    const results = await Promise.all(processPromises);

    // Count skipped rows (null results)
    const skipped = results.filter((r) => r === null).length;

    // Filter out null values and separate successful and failed results
    const processedResults = results.filter(Boolean);
    const successful = processedResults.filter((r) => r.success);
    const failed = processedResults.filter((r) => !r.success);

    // Prepare response
    const response = {
      totalProcessed: distributors.length,
      skipped: {
        count: skipped,
        message: "Rows skipped due to no change in salesperson key",
      },
      successful: {
        count: successful.length,
        items: successful,
      },
      failed: {
        count: failed.length,
        items: failed,
      },
    };

    // If any failures occurred, create error sheet and throw formatted error
    if (failed.length > 0) {
      // Create error worksheet
      const errorWorksheet = failed.map((item) => ({
        "Customer Name": item.customerName,
        "Customer Number": item.customerNumber,
        "Error Message": item.error,
      }));

      // Create workbook and add worksheet
      const errorWorkbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(errorWorksheet);

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(errorWorkbook, worksheet, "Failed Updates");

      // Generate buffer
      const errorBuffer = XLSX.write(errorWorkbook, {
        type: "buffer",
        bookType: "xlsx",
      });

      throw new AppError(
        {
          data: {
            summary: response,
            errorSheet: {
              buffer: errorBuffer,
              filename: `salesperson_update_errors_${
                new Date().toISOString().split("T")[0]
              }.xlsx`,
            },
          },
          message: "Some records failed to process",
        },
        400
      );
    }

    return response;
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    console.error("Error processing distributor sheet:", error);
    throw new AppError(
      {
        message: "Error processing distributor sheet",
        data: error.message,
      },
      500
    );
  }
}
