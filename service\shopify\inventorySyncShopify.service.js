import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";
import Inventory from "../../model/inventory.model.js";

export default async function inventorySyncShopify() {
  let allVariantProducts = [];
  let hasNextPage = true;
  let afterCursor = null;
  let apiHits = 0;

  try {
    while (hasNextPage) {
      apiHits++;
      console.log("Api hit : ", apiHits);
      const response = await fetchGraphqlDataShopify(
        SHOPIFY_VARIANTS_INVENTORY,
        {
          first: 250,
          after: afterCursor,
        }
      );

      const edges = response?.data?.productVariants?.edges;

      if (edges) {
        edges.forEach((element) => {
          const variant = element.node;
          allVariantProducts.push({
            sku: variant.sku,
            quantity: variant.inventoryQuantity,
          });
        });
      }

      hasNextPage =
        response?.data?.productVariants?.pageInfo?.hasNextPage || false;
      afterCursor =
        response?.data?.productVariants?.pageInfo?.endCursor || null;
    }

    await updateQuantityInDB(allVariantProducts);
    // console.log(JSON.stringify(allVariantProducts));
  } catch (error) {
    console.log("Error in ProcessShopifyInventory: ", error);
    return null;
  }
}

async function updateQuantityInDB(variants) {
  if (!variants?.length) {
    console.log("variants inventory data missing");
    return;
  }

  const variantMapShopify = {};
  const allVariantSKUs = variants.map((x) => {
    variantMapShopify[x.sku] = x.quantity;
    return x.sku;
  });

  const existingInventories = await Inventory.find({
    sku: { $in: allVariantSKUs },
  });

  const toBeUpdated = [];

  const itemsWhichAreNotPresentInShopify = [];

  for (let i = 0; i < existingInventories.length; i++) {
    const existingInventory = existingInventories[i];
    const onHoldQuantity = existingInventory.on_hold || 0;
    const shopifyQuantity = variantMapShopify[existingInventory.sku];
    if (shopifyQuantity === undefined || shopifyQuantity === null) {
      itemsWhichAreNotPresentInShopify.push(existingInventory.sku);
      continue;
    }
    if (shopifyQuantity < onHoldQuantity) {
      console.log(
        "Qty shall not be less than on-hold, sku:",
        existingInventory.sku,
        ",qty:",
        shopifyQuantity
      );
      continue;
    }
    const qtyToBeUpdated = shopifyQuantity - onHoldQuantity;
    if (qtyToBeUpdated !== existingInventory.quantity) {
      toBeUpdated.push({
        sku: existingInventory.sku,
        qty: qtyToBeUpdated < 0 ? 0 : qtyToBeUpdated,
      });
    }
  }

  console.log(
    "itemsWhichAreNotPresentInShopify",
    itemsWhichAreNotPresentInShopify
  );

  const bulkUpdateOps = toBeUpdated.map((x) => ({
    updateOne: {
      filter: { sku: x.sku },
      update: {
        $set: { quantity: x.qty },
      },
    },
  }));
  const bulkwrite = await Inventory.bulkWrite(bulkUpdateOps);
  console.log("bulkwrite inventory sync", bulkwrite);
}

const SHOPIFY_VARIANTS_INVENTORY = `query GetInventory($first: Int!, $after: String) {
  productVariants(
    first: $first
    after: $after
    query: "product_status:active"
  ) {
    edges {
      node {
        inventoryQuantity
        sku
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}`;
