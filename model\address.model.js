import mongoose from "mongoose";

const addressSchema = new mongoose.Schema({
  shopifyCustomerId: {
    type: String
  },
  shopifyAddressId: {
    type: String
  },
  distributor: {
    type: mongoose.Schema.ObjectId,
    ref: "Distributor",
    required: true,
  },
  first_name: {
    type: String
  },
  last_name: {
    type: String
  },
  company: {
    type: String
  },
  address1: {
    type: String
  },
  address2: {
    type: String
  },
  city: {
    type: String
  },
  province: {
    type: String
  },
  country: {
    type: String
  },
  zip: {
    type: String
  },
  province_code: {
    type: String
  },
  country_code: {
    type: String
  },
  isDefault: {
    type: String
  },
  country_name: {
    type: String
  },
}, { timestamps: true });

const Address = mongoose.model("Address", addressSchema);
export default Address;
