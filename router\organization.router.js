import express from 'express';
import {
  getOrganizations,
  createOrganization,
  getOneOrganization,
  updateOrganization,
  deleteOrganization
} from '../controller/organization.controller.js';

const router = express.Router();

router
  .route('/')
  .get(getOrganizations)
  .post(createOrganization);

router
  .route('/:id')
  .get(getOneOrganization)
  .patch(updateOrganization)
  .delete(deleteOrganization);

export default router;