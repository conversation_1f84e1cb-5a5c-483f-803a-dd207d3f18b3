import DepartmentSchema from "../../model/department/createDepartment.model.js";
import { v4 as uuidv4 } from 'uuid';

// create new department in db
export async function createDepartmentEntry(newDepartmentDetails){
  const uniqId = uuidv4(); 
  const departmentSchema = DepartmentSchema.create({
    id: `${uniqId}`,
    ...newDepartmentDetails
  })
  return departmentSchema;
}


// modify department details in db
export async function updateDepartmentDetail(modifiedDepartmentInfo){
  const updateDepartmentInfo = DepartmentSchema.findOneAndUpdate(
    {id: `${modifiedDepartmentInfo.id}`},
    {$set:{
      ...modifiedDepartmentInfo
    }},
  )
  return updateDepartmentInfo;
}

// delete distributor details
export async function deleteDepartmentEntry(departmentData){
  const { id } = departmentData;
  const deleteDepartmentData = await DepartmentSchema.deleteOne({ id });

  return deleteDepartmentData;
}