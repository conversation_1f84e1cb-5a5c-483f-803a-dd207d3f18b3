import axios from "axios";
import ExcelJS from "exceljs";
import fs from "fs";
import Inventory from "../model/inventory.model.js";
import path from "path";
import { uploadToS3 } from "../utils/helperFunction.js";
import LinesheetRecord from "../model/linesheet.model.js";
import { products } from "../constants/inventories.js";
import archiver from "archiver";
import stream from "stream";
import { generateXlsxSheet } from "../utils/lineSheetFileDownload.js";
import Status from "../model/status.model.js";

const __filename = decodeURI(new URL(import.meta.url).pathname).slice(1);
const __dirname = path.dirname(__filename);

export const downloadVariantXlsx = async (req, res) => {
  try {
    console.log("triggered");
    async function addImageToExcel(imageUrl, worksheet, workbook, range) {
      const response = await axios.get(imageUrl, {
        responseType: "arraybuffer",
      });
      const imageBuffer = Buffer.from(response.data, "binary");

      // Add the image to the worksheet
      const imageId = workbook.addImage({
        buffer: imageBuffer,
        extension: "png",
      });

      // Set the position and size of the image
      worksheet.addImage(imageId, range);

      return imageId; // Return the imageId for file name and extension
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Images");

    const fetchedJsonData = await Inventory.find({}).limit(50);
    // console.log("Inventory", fetchedJsonData);

    let formattedJsonData = fetchedJsonData.map((item) => {
      return {
        title: item.productTitle,
        image: item.image
          ? item.image //.split(".jpg").join("_500x.jpg")
          : "no image",
        productCode: item.sku,
        quantity: 0,
        cluster: item.cluster,
        brand: item.brand,
      };
    });
    // console.log("formattedJsonData", formattedJsonData);

    // formattedJsonData = formattedJsonData.slice(0, 10);

    // console.log(formattedJsonData);

    if (!fetchedJsonData.length) {
      return res.send({ error: "Data not found" });
    }

    // let formattedJsonData = [
    //   {
    //     title: "Default Title",
    //     name: "abc",
    //     qty: "1",
    //     image:
    //       "https://cdn.shopify.com/s/files/1/0640/5636/1060/files/Untitled_19.png?v=1712578831",
    //   },
    //   {
    //     title: "Default Title",
    //     name: "abc",
    //     qty: "1",
    //     image:
    //       "https://cdn.shopify.com/s/files/1/0640/5636/1060/files/Untitled_19.png?v=1712578831",
    //   },
    // ];

    const columns = Object.keys(formattedJsonData[0]);
    for (let i = 0; i < columns.length; i++) {
      const key = columns[i];
      const width = key === "image" ? 20 : 20; // Set width to 40 for 'image' column and 20 for others
      worksheet.getColumn(i + 1).width = width; // Set column width
      worksheet.getCell(1, i + 1).value =
        key.charAt(0).toUpperCase() + key.slice(1); // Set column header
    }

    for (let i = 0; i < formattedJsonData.length; i++) {
      const item = formattedJsonData[i];
      for (const [index, key] of Object.keys(item).entries()) {
        if (key === "image") {
          // Show image if the key is 'image'
          const range = `${String.fromCharCode(65 + index)}${
            i + 2
          }:${String.fromCharCode(65 + index)}${i + 2}`;
          await addImageToExcel(item[key], worksheet, workbook, range);
        } else {
          // Show text for other keys
          worksheet.getCell(
            `${String.fromCharCode(65 + index)}${i + 2}`
          ).value = item[key];
        }
      }
    }

    worksheet.getRow(1).height = 20; // Set height for heading tab
    for (let i = 2; i <= formattedJsonData.length + 1; i++) {
      worksheet.getRow(i).height = 120; // Set height for other rows
    }
    // Save the workbook to a file
    const filePath = "images.xlsx";
    const linesheetPath = path.join(
      __dirname,
      "..",
      "asset",
      "linesheet",
      `linesheet_${Date.now()}.xlsx`
    );
    await workbook.xlsx.writeFile(linesheetPath);
    console.log("formattedJsonData", formattedJsonData);

    // fs.mkdirSync(path.dirname(linesheetPath), { recursive: true });
    const uploadResponse = await uploadToS3(
      linesheetPath,
      process.env.AWS_S3_BUCKET
    );
    // console.log('uploadResponse', uploadResponse)
    console.log("uploadResponse =======>", uploadResponse);
    if (uploadResponse.status != "fail") {
      await LinesheetRecord.deleteMany({});
      await LinesheetRecord.create({
        downloadUrl: uploadResponse.Location,
        key: uploadResponse.key,
        etag: uploadResponse.ETag,
      });
    }
    fs.unlinkSync(linesheetPath); // Delete the temporary file after streaming
    res.status(200).send({
      status: "success",
      dowloadUrl: uploadResponse.Location,
    });
    // await workbook.xlsx.writeFile(linesheetPath);
    // fileStream.on("end", () => {
    //   fs.unlinkSync(filePath); // Delete the temporary file after streaming
    // });
  } catch (error) {
    console.error("Error:", error);
    res.status(500).send("Internal Server Error");
    return;
  }
};

export const dowloadLinesheet = async (req, res) => {
  const linesheet = await LinesheetRecord.findOne();
  res.status(200).send({
    status: "success",
    data: {
      fileName: linesheet?.key,
      downloadUrl: linesheet?.downloadUrl,
    },
  });
};

//!NOT_IN_USE
// async function getJsonData() {
//   try {
//     let data = JSON.stringify({
//       query: `{
//           productVariants(first: 10) {
//             edges {
//               node {
//                 title
//                 sku
//                 product {
//                   featuredImage {
//                     src
//                   }
//                 }
//               }
//             }
//           }
//         }`,
//       variables: {},
//     });

//     let config = {
//       method: "post",
//       maxBodyLength: Infinity,
//       url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/graphql.json`,
//       headers: {
//         "x-shopify-access-token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
//         "Content-Type": "application/json",
//       },
//       data: data,
//     };

//     let response = await axios.request(config);
//     console.log(response?.data?.data?.productVariants?.edges);
//     let jsonToReturn = response?.data?.data?.productVariants?.edges || [];

//     return jsonToReturn;
//   } catch (error) {
//     console.log(error);
//   }
// }

export const downloadXlsxSheet = async (req, res) => {
  console.log("start111111 downloadXlsxSheet");
  const products = await Inventory.find({ inventoryType: { $ne: "CUSTOM" } });
  const data = await generateXlsxSheet(products, process.env.AWS_S3_BUCKET);
  console.log("end111111 downloadXlsxSheet");
  res.status(200).json({ data });
};

export const alignmentPendingLineSheet = async (req, res) => {
  try {
    const shipments = await Status.aggregate([
      {
        $match: {
          pseudoId: "ALIGNMENT_PENDING",
        },
      },
      {
        $lookup: {
          from: "shipments",
          localField: "_id",
          foreignField: "status",
          as: "shipment",
        },
      },
      {
        $unwind: {
          path: "$shipment",
        },
      },
      {
        $project: {
          shipment: 1,
          _id: 0,
        },
      },
      {
        $lookup: {
          from: "orders",
          localField: "shipment.order",
          foreignField: "_id",
          as: "order",
        },
      },
      {
        $unwind: {
          path: "$order",
        },
      },
      {
        $lookup: {
          from: "distributors",
          localField: "order.distributor",
          foreignField: "_id",
          as: "distributor",
        },
      },
      {
        $unwind: {
          path: "$distributor",
        },
      },
    ]);

    if (!shipments.length) {
      console.log("No shipments found with ALIGNMENT_PENDING status.");
      return res
        .status(404)
        .json({ error: "No shipments found with ALIGNMENT_PENDING status." });
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet("Shipments");

    // Define the columns for the Excel sheet
    worksheet.columns = [
      { header: "SKU", key: "sku", width: 20 },
      { header: "Price", key: "price", width: 15 },
      { header: "Quantity", key: "quantity", width: 15 },
      { header: "Image", key: "image", width: 40 },
    ];

    shipments.forEach((shipment) => {
      shipment.shipment.lineItems?.forEach((item) => {
        worksheet.addRow({
          sku: item.sku,
          price: item.price,
          quantity: item.requested,
          image: item.image,
        });
      });
    });

    // Apply some formatting
    worksheet.getRow(1).font = { bold: true };
    worksheet.eachRow({ includeEmpty: false }, function (row, rowNumber) {
      row.height = 20;
    });

    res.setHeader(
      "Content-Disposition",
      'attachment; filename="Alignment_Pending_Shipments.xlsx"'
    );
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );

    await workbook.xlsx.write(res);

    res.end();

    console.log("Excel file generated: ALIGNMENT_PENDING.xlsx");
  } catch (error) {
    console.error("Error generating shipment excel:", error);
    res.status(500).send("Internal Server Error");
  }
};
