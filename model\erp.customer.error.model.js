import mongoose from "mongoose";

const CustomerErrorLogSchema = new mongoose.Schema(
  {
    customerNumber: {
      type: String,
      required: false,
      default: null,
    },
    customerName: {
      type: String,
      required: false,
      default: null,
    },
    errorMessage: {
      type: String,
      required: true,
    },
    manual: {
      type: Boolean,
      default:false
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
  },
  { timestamps: true }
);

const CustomerErrorLog = mongoose.model("CustomerErrorLog", CustomerErrorLogSchema);
export default CustomerErrorLog;
