import mongoose from "mongoose";
import jwt from "jsonwebtoken";
import Department from "../model/department.model.js";
import newAppError from "../utils/newAppError.js";
import User from "../model/user.model.js";

const departmentBasedScopes = {
  distributor: {
    approve: ["SUNRISE_ADMIN", "FINANCE_EXECUTIVE", "FINANCE_MANAGER"],
    editDetails: ["SUNRISE_ADMIN", "FINANCE_EXECUTIVE", "FINANCE_MANAGER"],
    rejectCustomer: ["SUNRISE_ADMIN", "FINANCE_EXECUTIVE", "FINANCE_MANAGER"],
    showProfile: {
      read: ["SUNRISE_ADMIN", "FINANCE_EXECUTIVE", "FINANCE_MANAGER"],
      write: ["SUNRISE_ADMIN", "FINANCE_MANAGER"],
      delete: ["SUNRISE_ADMIN", "FINANCE_EXECUTIVE", "FINANCE_MANAGER"],
    },
    list: {
      read: [
        "SUNRISE_ADMIN",
        "FINANCE_EXECUTIVE",
        "FINANCE_MANAGER",
        "SALES_PERSON",
        "SUNRISE_SALES_COORDINATOR",
      ],
      write: ["SUNRISE_ADMIN", "FINANCE_MANAGER"],
      delete: ["SUNRISE_ADMIN", "FINANCE_EXECUTIVE", "FINANCE_MANAGER"],
    },
  },
  order: {
    autoAllocate: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
    cancelOrder: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
    manualAllocate: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
    list: {
      read: [
        "SALES_PERSON",
        "SUNRISE_TEAM",
        "SUNRISE_ADMIN",
        "SUNRISE_SALES_COORDINATOR",
      ],
      write: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
      delete: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
    },
  },
  shipment: {
    bulkProcess: [
      "SALES_PERSON",
      "SUNRISE_TEAM",
      "SUNRISE_ADMIN",
      "SUNRISE_SALES_COORDINATOR",
    ],
    autoAllocate: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
    cancelShipment: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
    manualAllocate: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
    list: {
      read: [
        "SALES_PERSON",
        "SUNRISE_TEAM",
        "SUNRISE_ADMIN",
        "SUNRISE_SALES_COORDINATOR",
      ],
      write: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
      delete: ["SALES_PERSON", "SUNRISE_TEAM", "SUNRISE_ADMIN"],
    },
  },
};

const authenticateDepttPeopleAccess = (module, scope) => {
  return async (req, res, next) => {
    const auth = req.headers.authorization;

    if (!auth) {
      return res.status(401).json({ error: "No token provided" });
    }

    const token = auth.split(" ")[1];

    try {
      const decodedUser = jwt.verify(token, process.env.JWT_TOKEN_SECRET);
      let userId = decodedUser.userId;
      const user = await Department.findById(userId).populate();
      if (!user) {
        return res.status(401).json({ error: "You're Not Authorized." });
      }

      req.body.current_user = user;
      req.user = user;

      next();
    } catch (err) {
      return res.status(401).json({ error: "You're Not Authorized" });
    }
  };
};
const authenticateDepttPeopleAccessModuleWise = (
  module,
  scope,
  permission = null
) => {
  return async (req, res, next) => {
    const auth = req.headers.authorization;

    if (!auth) {
      return next(new newAppError("BAD_REQUEST", "No token provided.", 401));
    }

    const token = auth.split(" ")[1];
    try {
      
      const decodedUser = jwt.verify(token, process.env.JWT_TOKEN_SECRET);
     
 
      const userId = decodedUser.userId;
      

      // Try to find user in Department
      let user = await Department.findById(userId).populate("departmentType");
      let userType = "department";
      if (!user) {
        // Try to find user in User
        try {
          user = await User.findById(userId);
          userType = "user";
        } catch (e) {
          console.log(e);
          return next(new newAppError("SERVER_ERROR","User not found",400))
        }
      }
     
      
      
      // If userType is 'user' and user.type is 'admin_user', always allow access
      if (userType === "user" && user.type === "admin_user") {
        req.user = user;
        return next();
      }
     
      
      if (decodedUser.type === "admin_user" && permission === "read") {
        req.user = user;
        return next();
      }
      if (!user) {
        return next(
          new newAppError("ACCESS_DENIED", "You're Not Authorized.", 401)
        );
      }

      const moduleScopes = departmentBasedScopes[module];
      if (!moduleScopes || !moduleScopes[scope]) {
        return next(new newAppError("ACCESS_DENIED", "Access Denied.", 400));
      }

      let allowedRoles;
      const scopePermissions = moduleScopes[scope];

      if (typeof scopePermissions === "object" && permission) {
        allowedRoles = scopePermissions[permission];
        if (!allowedRoles) {
          return next(
            new newAppError(
              "ACCESS_DENIED",
              `Invalid permission: ${permission}`,
              400
            )
          );
        }
      } else if (Array.isArray(scopePermissions)) {
        allowedRoles = scopePermissions;
      } else {
        return next(new newAppError("ACCESS_DENIED", "Access Denied.", 400));
      }
       
      // Always allow Sunrise Admin and Sunrise Super Admin
      const isAdmin = user.departmentType.some((dept) =>
        ["SUNRISE_ADMIN", "SUNRISE_SUPER_ADMIN"].includes(dept.pseudoId)
      );

      let hasAccess = false;
      if (isAdmin) {
        hasAccess = true;
      } else {
        hasAccess = user.departmentType.some((dept) => {
          return allowedRoles.includes(dept.pseudoId);
        });
      }

      if (!hasAccess) {
        return next(new newAppError("ACCESS_DENIED", "Access Denied.", 400));
      }

      const isSunriseCoordinator = user.departmentType.some(
        (dept) => dept.pseudoId === "SUNRISE_SALES_COORDINATOR"
      );

      if (isSunriseCoordinator && user.salesPersons?.length > 0) {
        // Populate salesPersons (referenced departments)
        const populatedSalesPersons = await Department.find({
          _id: { $in: user.salesPersons },
        });

        req.user = user;
        req.salesPersonsUnderCoordinator = populatedSalesPersons;
        return next();
      }

      const salesPersonDept =
        user.departmentType.find((dept) => {
          return dept.pseudoId === "SALES_PERSON";
        }) || null;

      req.body.current_user = user;
      req.user = user;
      req.salesPersonDept = salesPersonDept;

      next();
    } catch (err) {
      console.error("Authentication error:", err.message);

      return next(
        new newAppError("UNAUTHORIZED", "You're Not Authorized", 401)
      );
    }
  };
};

export {
  authenticateDepttPeopleAccess,
  authenticateDepttPeopleAccessModuleWise,
};
