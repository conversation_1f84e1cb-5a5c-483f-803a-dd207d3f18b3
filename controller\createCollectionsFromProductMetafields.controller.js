import { smartGetProductMetafieldDefinitions, smartGetProductsWithMetafields, smartGetCollections, smartCreateCollection } from "./shopify.controller.js";

export const createCollectionsFromProductMetafields = async () => {
  console.log("🔵 Smart Collection Sync Start");

  // 1️⃣ Fetch all product metafield definitions (with pagination)
  let allDefs = [];
  let defsCursor = null;
  let defsHasNextPage = true;
  while (defsHasNextPage) {
    const defsData = await smartGetProductMetafieldDefinitions(defsCursor);
    if (!defsData) break;
    allDefs = allDefs.concat(defsData.edges.map((e) => e.node));
    defsHasNextPage = defsData.pageInfo.hasNextPage;
    defsCursor = defsHasNextPage ? defsData.pageInfo.endCursor : null;
  }

  // Find definition IDs for shopify_1, shopify_2, shopify_3
  const shopify1Def = allDefs.find((d) => d.namespace === "custom" && d.key === "shopify_1");
  const shopify2Def = allDefs.find((d) => d.namespace === "custom" && d.key === "shopify_2");
  const shopify3Def = allDefs.find((d) => d.namespace === "custom" && d.key === "shopify_3");

  // 2️⃣ Fetch all products with metafields (to get values)
  let allProducts = [];
  let prodCursor = null;
  let prodHasNextPage = true;
  while (prodHasNextPage) {
    const productsData = await smartGetProductsWithMetafields(prodCursor);
    if (!productsData) break;
    allProducts = allProducts.concat(productsData.edges.map((e) => e.node));
    prodHasNextPage = productsData.pageInfo.hasNextPage;
    prodCursor = prodHasNextPage ? productsData.pageInfo.endCursor : null;
  }

  // 3️⃣ Build desired collections from product metafields
  const collectionsToCreate = [];
  allProducts.forEach((product) => {
    let shopify1 = null, shopify2 = null, shopify3 = null;
    (product.metafields.edges || []).forEach(({ node }) => {
      if (node.namespace === "custom" && node.key === "shopify_1") shopify1 = node.value;
      if (node.namespace === "custom" && node.key === "shopify_2") shopify2 = node.value;
      if (node.namespace === "custom" && node.key === "shopify_3") shopify3 = node.value;
    });
    // If all three metafields exist, create 3 collections: [1,2,3], [1,2], [1]
    if (shopify1 && shopify2 && shopify3) {
      collectionsToCreate.push({
        title: shopify3,
        rules: [
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify1, conditionObjectId: shopify1Def?.id },
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify2, conditionObjectId: shopify2Def?.id },
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify3, conditionObjectId: shopify3Def?.id },
        ],
      });
      collectionsToCreate.push({
        title: shopify2,
        rules: [
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify1, conditionObjectId: shopify1Def?.id },
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify2, conditionObjectId: shopify2Def?.id },
        ],
      });
      collectionsToCreate.push({
        title: shopify1,
        rules: [
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify1, conditionObjectId: shopify1Def?.id },
        ],
      });
    }
    // If only shopify_1 and shopify_2
    else if (shopify1 && shopify2) {
      collectionsToCreate.push({
        title: shopify2,
        rules: [
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify1, conditionObjectId: shopify1Def?.id },
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify2, conditionObjectId: shopify2Def?.id },
        ],
      });
      collectionsToCreate.push({
        title: shopify1,
        rules: [
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify1, conditionObjectId: shopify1Def?.id },
        ],
      });
    }
    // If only shopify_1
    else if (shopify1) {
      collectionsToCreate.push({
        title: shopify1,
        rules: [
          { column: "PRODUCT_METAFIELD_DEFINITION", relation: "EQUALS", condition: shopify1, conditionObjectId: shopify1Def?.id },
        ],
      });
    }
  });

  // 2️⃣ Fetch existing collections
  let allCollections = [];
  let colCursor = null;
  let colHasNextPage = true;
  while (colHasNextPage) {
    const collectionsData = await smartGetCollections(colCursor);
    if (!collectionsData) break;
    allCollections = allCollections.concat(collectionsData.edges.map((e) => e.node));
    colHasNextPage = collectionsData.pageInfo.hasNextPage;
    colCursor = colHasNextPage ? collectionsData.pageInfo.endCursor : null;
  }

  // 4️⃣ Create missing collections (check ruleset)
  const created = [];
  const createdRulesets = new Set();
  for (const col of collectionsToCreate) {
    // Check for duplicate ruleset
    const rulesKey = JSON.stringify(col.rules);
    if (createdRulesets.has(rulesKey)) {
      continue;
    }
    // Check if a collection with the same title exists
    let exists = false;
    for (const existingCol of allCollections) {
      if (existingCol.title === col.title) {
        exists = true;
        break;
      }
    }
    if (exists) {
      continue;
    }
    const input = {
      title: col.title,
      ruleSet: {
        appliedDisjunctively: false,
        rules: col.rules,
      },
    };
    const createdData = await smartCreateCollection(input);
    if (!createdData) {
      console.error(`❌ No response for collection: ${col.title}`);
      continue;
    }
    if (createdData.userErrors?.length) {
      console.error(`❌ Error creating ${col.title}:`, createdData.userErrors);
      continue;
    } else {
      created.push(createdData.collection.title);
      createdRulesets.add(rulesKey);
    }
  }

  // res.json({
  //   message: `Smart sync done. Created: ${created.length}`,
  //   createdCollections: created,
  // });

  console.log(`Smart sync done. Created: ${created.length}`, created)

  return;
};