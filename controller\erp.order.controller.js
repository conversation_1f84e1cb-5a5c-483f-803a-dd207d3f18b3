import {
  getShopifyOrder,
  createERPOrderService,
  getcreatedOrderDataHandler,
  formatOrderForERP,
} from "../service/ERPSync/orderService.js";
import NewAppError from "../utils/newAppError.js";
import Distributer from "../model/distributor.model.js";
const createERPOrderController = async (orderId) => {
  try {
    if (!orderId) {
      return new NewAppError(
        "INVALID_INPUT",
        "Need Order to creating order in ERP.",
        400
      );
    }

    const order = await getcreatedOrderDataHandler(orderId);

    if (!order) {
      return new NewAppError(
        "NOT_FOUND",
        `No matching order in DataBase.`,
        404
      );
    }

    const formattedOrder = await formatOrderForERP(order);

    const response = await createERPOrderService(formattedOrder);
    return {
      responseCode: 0,
      status: "success",
      data: { order: response.data },
    };
  } catch (error) {
    console.log(error, "error here");
    return new NewAppError("SERVER_ERROR", error?.message, 500);
  }
};

export { createERPOrderController };
