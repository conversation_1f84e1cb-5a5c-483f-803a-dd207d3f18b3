import fs from "fs";
import mongoose from "mongoose";
import xlsx from "xlsx";
import path from "path";
import Department from "../model/department.model.js";
import DepartmentType from "../model/departmentType.model.js";
import Shipment from "../model/shipment.model.js";
import Status from "../model/status.model.js";
import Inventory from "../model/inventory.model.js";
import StatusActionLink from "../model/statusActionLink.model.js";
import Order from "../model/order.model.js";
import {
  alignmentPendingOrderProcessTrigger,
  endEscalationStatus,
} from "./action.controller.js";
import {
  createOne,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import AppError from "../utils/appError.js";
import {
  uploadToS3,
  validateSkusSubsetUtility,
} from "../utils/helperFunction.js";
import EmailNotification from "../model/emailNotification.model.js";
import { generateInvoicePdf } from "../utils/pdfGenerator.js";
import Distributor from "../model/distributor.model.js";
import { getDepartmentNotifiedEmails } from "../utils/findNotifiedDepartment.js";
import NewAppError from "../utils/newAppError.js";
import {
  shipmentUploadValidateService,
  compareShipmentItems,
  shipmentEmailNotification,
  processShipmentService,
  cancelPendingShipmentService,
  processBulkShipmentsService,
  addErpIdentifierShipmentDbService,
} from "../service/shipmentService.js";
import { validateAllocationQuantityWithERPService } from "../service/orderService.js";
import { createERPOrderController } from "../controller/erp.order.controller.js";
import catchAsync from "../utils/catchAsync.js";
import APIFeatures from "../utils/apiFeatures.js";
import { allocatedMoreThanOrderedCheck } from "../helpers/sheetValidation.js";

const __filename = new URL(import.meta.url).pathname;
const __dirname = path.dirname(__filename);

const ObjectId = mongoose.Types.ObjectId;

export const getShipment = getAll(Shipment);
export const getOneShipment = getOne(Shipment);
export const createShipment = createOne(Shipment);
export const updateShipment = updateOne(Shipment);
export const updateShipmentStatus = updateOne(Shipment);
export const deleteShipment = deleteOne(Shipment);

export const getDeptWiseShipments = catchAsync(async (req, res, next) => {
  const user = req.user;
  const salesPersonDept = req.salesPersonDept;
  const salesPersonsUnderCoordinator = req.salesPersonsUnderCoordinator;

  let query = Shipment.find();
  let totalCount = await Shipment.countDocuments();

  // Case 1: Salesperson access
  if (user && salesPersonDept) {
    const distributorIds = await Distributor.distinct("_id", {
      salespersonId: user._id.toString(),
    });

    totalCount = await Shipment.countDocuments({
      distributor: { $in: distributorIds },
    });

    query = Shipment.find({
      distributor: { $in: distributorIds },
    });
  }

  // Case 2: Sunrise Coordinator access
  else if (
    user &&
    Array.isArray(salesPersonsUnderCoordinator) &&
    salesPersonsUnderCoordinator.length > 0
  ) {
    const salesPersonIds = salesPersonsUnderCoordinator.map((sp) =>
      sp._id.toString()
    );

    const distributorIds = await Distributor.distinct("_id", {
      salespersonId: { $in: salesPersonIds },
    });

    totalCount = await Shipment.countDocuments({
      distributor: { $in: distributorIds },
    });
    query = Shipment.find({
      distributor: { $in: distributorIds },
    });
  }

  // Apply filters, sorting, and pagination
  const features = new APIFeatures(query, req.query)
    .filter()
    .sort()
    .limitFields()
    .paginate();

  const doc = await features.query;

  res.status(200).json({
    status: "success",
    result: totalCount,
    data: {
      data: doc,
    },
  });
});

export const returnShipmentDataWithActions = async (req, res) => {
  const { id } = req.params;
  console.log("---id", id);
  try {
    const shipment = await Shipment.findById(id).populate();
    if (!shipment) {
      res.send({ error: "No shipment found with given id." });
    }
    const orderId = shipment.order._id;
    const order = await Order.findById(orderId).populate("distributor");
    if (!order) {
      return res.send({ error: "No order found for the shipment." });
    }
    const distributorId = order.distributor._id;

    const distributor = await Distributor.findById(distributorId).select(
      "companyDetails"
    );
    if (!distributor) {
      return res.send({ error: "Distributor not found." });
    }
    const { companyDetails } = distributor;
    const { businessAddress, pincode, country } = companyDetails;

    const current_status = shipment.status._id;
    const actions = await StatusActionLink.aggregate([
      {
        $match: {
          current_status_id: new ObjectId(current_status),
          // enabled: true,
        },
      },
      {
        $lookup: {
          from: "actions",
          localField: "action_id",
          foreignField: "_id",
          as: "action",
        },
      },
      {
        $unwind: "$action",
      },
      {
        $project: {
          action_display_name: 1,
          formData: 1,
          actionRoute: "$action.route",
          action_unique_key: "$action.action_unique_key",
        },
      },
    ]);
    res.send({
      shipment,
      actions,
      address: { businessAddress, pincode, country },
    });
  } catch (error) {
    console.log("returnShipmentDataWithActions", error);
    res.send({ error: "Something went wrong" });
  }
};

export const downloadShipmentExcel = async (req, res, next) => {
  try {
    const shipmentId = req.query.shipment_id;

    if (!shipmentId) {
      return next(
        new NewAppError("BAD_REQUEST", `Shipment Id is required.`, 400)
      );
    }
    const shipment = await Shipment.findOne({ _id: shipmentId });
    if (!shipment) {
      return next(
        new NewAppError("NOT_FOUND", `Shipment not found for provided Id.`, 400)
      );
    }

    const status = await Status.findById(shipment.status);
    if (!status) {
      return next(
        new NewAppError(
          "MISSING_DATA",
          "Missing required data to process.",
          400
        )
      );
    }

    //hardcoded status ,needs to be dynamic.Checking that shipment is not already allocated.
    //Reference Awaiting Courier Partial
    if (status._id.toString() !== "673afbb0a7f9a80fac8a4944") {
      return next(
        new NewAppError(
          "CONFLICT",
          `Shipment already processed or cancelled.`,
          409
        )
      );
    }

    let formattedStatus = "";

    formattedStatus = status.status.includes(" ")
      ? status.status.replace(/\s+/g, "-")
      : status;

    let sheetPayload = [];
    let sheetHeader = ["Title", "Sku", "OrderedQuantity", "AllocatedQuantity"];
    sheetPayload.push(sheetHeader);
    shipment.lineItems.forEach((lineItem) => {
      sheetPayload.push([
        lineItem.productTitle,
        lineItem.sku,
        lineItem.requested || 0,
        lineItem.requested_quantity || 0,
      ]);
    });

    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(sheetPayload);

    xlsx.utils.book_append_sheet(workbook, sheet, "Sheet1");

    const fileName = `${formattedStatus}_${shipment.name}.xlsx`;

    const fileBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    // Set headers for file download
    res.setHeader("Content-disposition", `attachment; filename=${fileName}`);
    res.setHeader(
      "Content-type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );

    // Send the file buffer directly in the response
    return res.status(200).send(fileBuffer);
  } catch (error) {
    return next(new NewAppError("SERVER_ERROR", `Something went wrong.`, 500));
  }
};

export const generateShipmentPISheet = async (req, res, next) => {
  try {
    // console.log("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx");
    const shipmentId = req.query.shipment_id;
    if (!shipmentId) return next(new AppError("Missing shipment Id", 400));
    const shipment = await Shipment.findOne({ _id: shipmentId });
    const distributorId = shipment.order.distributor._id;
    const distributorDetail = await Distributor.findOne({ _id: distributorId });
    const distributorName =
      distributorDetail.firstName + "-" + distributorDetail.lastName;

    if (!shipment)
      return next(new AppError(`No Shipment found with ${shipmentId}`, 400));
    let sheetPayload = [];
    let sheetHeader = [
      "Latest Sku",
      "IBD SKU",
      "Brand",
      "Type",
      "Cluster",
      "Description",
      "Qty",
      "FOB",
      "Value",
    ];
    // console.log("zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz");

    sheetPayload.push(sheetHeader);
    const totalQuatity = shipment.lineItems.reduce((sum, lineItem) => {
      return (sum += lineItem.requested * 1);
    }, 0);
    // console.log("totalQuatity", totalQuatity);
    const totalPrice = shipment.lineItems.reduce((sum, lineItem) => {
      return (sum += lineItem.price * lineItem.requested);
    }, 0);
    // console.log("totalPrice", totalPrice);
    // console.log("000000000000000000000000000000000000");

    shipment.lineItems.forEach((lineItem) => {
      sheetPayload.push([
        lineItem.sku,
        lineItem.sku,
        "WN",
        "G",
        "GSMRT",
        lineItem.productTitle,
        lineItem.requested || 0,
        lineItem.price,
        (lineItem.price * lineItem.requested).toFixed(2),
      ]);
    });
    sheetPayload.push([
      "",
      "",
      "",
      "",
      "Total",
      "",
      totalQuatity,
      "",
      totalPrice,
    ]);
    // sheetLineItemPayload.push(sheetPayload);
    // console.log(sheetPayload);
    //TODO: Query shipment and generate Data
    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(sheetPayload);

    xlsx.utils.book_append_sheet(workbook, sheet, "Sheet1");

    // const fileName = `pi_${shipmentId}_${Date.now()}.xlsx`;
    const fileName = `Performa-Invoice-${distributorName}-V1.xlsx`;

    // console.log("============================ DIRNAME ==========================================")
    // console.log(__dirname)
    // console.log("======================================================================")
    // console.log("============================ FILE NAME ==========================================")
    // console.log(fileName)
    // console.log("======================================================================")

    const piSheetPath = path.join(
      __dirname,
      "..",
      "asset",
      "pi",
      `${fileName}`
    );
    xlsx.writeFile(workbook, piSheetPath);

    //! GENERATE PDF
    const items = shipment.lineItems.map((lineItem, index) => {
      return {
        item: index,
        description: lineItem.productTitle,
        quantity: lineItem.requested || 0,
        price: lineItem.price,
      };
    });

    const client = {
      name: "Alok",
      email: "<EMAIL>",
      address: "test",
      city: "test",
      state: "test",
      country: "France",
      postal_code: 4654654,
    };
    const paid = 10;
    const moreDetails = "nothing";
    // const invoiceNumber = `pi_${shipmentId}_${Date.now()}`;
    const invoiceNumber = `Performa-Invoice-${distributorName}-V1`;

    // Calculate sum per item
    items.forEach((item) => {
      item.amountSum = item.price
        ? item.price * (item.quantity ? item.quantity : 0)
        : 0;
    });

    // getting subtotal ->
    const subtotal = items.reduce((prev, curr) => {
      return curr.amountSum + prev;
    }, 0);

    const pdfFileName = invoiceNumber + ".pdf";
    const pdfFilePath = path.join(
      __dirname,
      "..",
      "asset",
      "pi",
      `${pdfFileName}`
    );
    try {
      const invoiceDetails = {
        items,
        invoiceNumber,
        paid,
        shipping_address: shipment.order.shipping_address,
        subtotal,
        client: {
          ...shipment.order.distributor,
          ...shipment.order.shipping_address,
        },
      };
      generateInvoicePdf(invoiceDetails, pdfFilePath);
    } catch (error) {
      console.log(error);
    }

    const files = [pdfFilePath];
    const uploadResponse = await uploadToS3(
      piSheetPath,
      process.env.AWS_S3_BUCKET,
      fileName
    );

    const uploadResponsePdf = await uploadToS3(
      pdfFilePath,
      process.env.AWS_S3_BUCKET,
      pdfFileName
    );
    fs.unlinkSync(piSheetPath);
    fs.unlinkSync(pdfFilePath);

    await Shipment.updateOne(
      { _id: shipment._id },
      {
        piSheet: uploadResponse.Location,
        piPdf: uploadResponsePdf.Location,
      }
    );
    const distributerId = shipment.order.distributor._id;
    const distributor = await Distributor.findOne({ _id: distributerId });
    const departmentTypeId = shipment.order.status.departmentType._id;
    const departmentPeoples = await Department.find({
      departmentType: { $in: [departmentTypeId] },
    });

    const recipients = departmentPeoples.map((departmentPeople) => ({
      name: departmentPeople.name,
      email: departmentPeople.email,
    }));

    if (recipients.length > 0) {
      await EmailNotification.create({
        emailCategory: "SHIPMENT",
        emailType: "PI_GENERATED",
        reciepient: recipients,
        emailPayload: {
          shipmentId: shipmentId,
          shipmetRef: shipment.ref,
          shipmentName: shipment.name,
          distributorName: distributor.name,
          piSheet: uploadResponse.Location,
          PiPdf: uploadResponsePdf.Location,
          attachments: [uploadResponse.Location, uploadResponsePdf.Location],
        },
      });
    }

    res.status(200).send({
      data: {
        status: "success",
        sheetDownloadUrl: uploadResponse.Location,
        pdfDownloadUrl: uploadResponsePdf.Location,
      },
    });
  } catch (error) {
    res.status(200).send(error);
  }
};

export const editShipment = async (req, res, next) => {
  try {
    const shipmentId = req.params.shipmentId;
    const { action_link_id, shipment_id } = req.query;
    const { form_data, current_user } = req.body;

    if (!action_link_id || !shipment_id) {
      return res.send({ error: "Action link id and shipment id is mandatory" });
    }

    const shipment = await Shipment.findById(shipment_id).populate();

    if (!shipment) {
      return res.send({ error: "Shipment not found with id:" + shipment_id });
    }
    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return res.send({ error: "statusActionLinkData not present" });
    }

    const current_shipment_status = shipment.status._id;
    const current_actionable_deptt =
      shipment.status.departmentType._id.toString();
    const current_user_deptt = current_user.departmentType.map((dept) =>
      dept._id.toString()
    );

    if (!current_user_deptt.includes(current_actionable_deptt)) {
      return res.send({ error: "You aren't authorized" });
    }

    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return res.send({ error: "Invalid status change request" });
    }

    if (!req.file) return next(new AppError("No file attached"));
    // const filePath = req.file.path;
    const filePath =
      "C:/Users/<USER>/Documents/GitHub/Sunrise-trade-OMS-backend/assets/pi/Awaiting-Courier-Partial_#1065_2.xlsx";

    const workbook = xlsx.readFile(filePath);

    // Convert the first sheet to JSON
    const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
    const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    const existingShipment = await Shipment.findById(shipmentId);

    const comparison = compareItems(jsonData, existingShipment.lineItems);

    if (comparison.error) {
      return res.send({ error: comparison.error });
    }

    const allShipments = await Shipment.find({
      order: existingShipment.order._id,
    });

    const status = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });

    const length = allShipments.length;

    if (comparison.remainingItemsShipment.length) {
      const nonAligned = {
        name: existingShipment.order.name + `_${length + 1}`,
        order: existingShipment.order._id,
        status: "663c4ac9864c679bd21974a8", //this need to be dynamic
        amount: 5000,
        shipping_address: existingShipment.shipping_address,
        lineItems: comparison.remainingItemsShipment,
        timeline: [
          {
            time: new Date(),
            comment: `Non-Aligned shipment created from Shipment:${existingShipment.name}`,
          },
        ],
      };
      await new Shipment(nonAligned).save();
      const updateorder = await Order.findByIdAndUpdate(
        existingShipment.order._id,
        {
          $push: {
            timeline: {
              time: new Date(),
              comment: `Non-Aligned shipment:${nonAligned.name} created from Shipment:${existingShipment.name}`,
            },
            status_change_history: {
              status: "663c4ac9864c679bd21974a8", //this need to be dynamic
            },
          },
        }
      );
    }

    //release inventory
    // if (comparison.inventoryToRelease.length) {
    //   const bulkUpdateOps = comparison.inventoryToRelease.map((x) => ({
    //     updateOne: {
    //       filter: { sku: x.sku },
    //       update: {
    //         $inc: { quantity: x.quantity, on_hold: -1 * x.on_hold },
    //       },
    //     },
    //   }));
    //   const bulkwrite = await Inventory.bulkWrite(bulkUpdateOps);
    // }

    if (shipment.escalation_status) {
      await endEscalationStatus(shipment);
    }

    const updateShipment = await Shipment.findByIdAndUpdate(shipmentId, {
      $set: {
        lineItems: comparison.updatedShipment.filter((x) => x.requested),
        status: statusActionLinkData.next_status_id,
        status_data: {
          name: status.status,
        },
      },
      $push: {
        timeline: {
          time: new Date(),
          comment: `Shipment has been edited by ${current_user.name}`,
        },
        status_change_history: {
          status: statusActionLinkData.next_status_id,
        },
      },
    });

    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });
    const departmentPeoples = await Department.find({
      departmentType: { $in: [nextStatus.departmentType._id] },
    });

    const distributerId = shipment.order.distributor._id;
    const distributor = await Distributor.findOne({ _id: distributerId });

    const departmentNotified = await getDepartmentNotifiedEmails(nextStatus);

    const recipients = departmentPeoples.map((departmentPeople) => ({
      name: departmentPeople.name,
      email: departmentPeople.email,
      cc: [...departmentNotified],
    }));
    if (recipients.length > 0) {
      await EmailNotification.create({
        emailCategory: "SHIPMENT",
        emailType: "SHIPMENT_STATUS_CHANGE",
        reciepient: recipients,
        emailPayload: {
          orderName: shipment.order.name,
          distributorName: distributor.name,
          date: shipment.order.createdAt,
          shipmentName: shipment.name,
          shipmentStatus: nextStatus.status,
          shipmetRef: shipment.ref,
          currentStatus: currentStatus.status,
          nextStatus: nextStatus.status,
          actionByUser: current_user.name,
        },
      });
    }

    res.status(201).json(updateShipment);
  } catch (error) {
    console.log("editShipmentError", error);
    res.send({ error: "Something went wrong" });
  }
};

export const allocateShipmentManually = async (req, res, next) => {
  try {
    const shipmentId = req.params.shipmentId;
    const { form_data, current_user } = req.body;
    const { action_link_id } = req.query;

    if (!shipmentId) {
      return next(
        new NewAppError("BAD_REQUEST", "Shipment id is mandatory.", 400)
      );
    }

    if (!action_link_id) {
      return next(
        new NewAppError(
          "BAD_REQUEST",
          "Action link id and shipment id is mandatory.",
          400
        )
      );
    }

    const existingShipment = await Shipment.findById(shipmentId);

    if (!existingShipment) {
      return next(
        new NewAppError(
          "NOT_FOUND",
          `Shipment not found with id:${shipmentId}.`,
          404
        )
      );
    }

    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return next(
        new NewAppError(
          "MISSING_ACTION_LINK",
          "Status Action Link Data not present.",
          400
        )
      );
    }

    const current_shipment_status = existingShipment.status._id;

    // const current_actionable_deptt =
    //   existingShipment.status.departmentType._id.toString();
    // const current_user_deptt = current_user.departmentType.map((dept) =>
    //   dept._id.toString()
    // );
    // if (!current_user_deptt.includes(current_actionable_deptt)) {
    //   return next(
    //     new NewAppError("UNAUTHORIZED", "You aren't authorized.", 403)
    //   );
    // }
    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return next(
        new NewAppError("BAD_REQUEST", "Invalid status change request.", 400)
      );
    }

    //Reference:Awaiting Courier Partial
    //hardcode status id ,needs to be dynamic.Check as only non aligned shipment can be processed.
    if (existingShipment.status._id.toString() !== "673afbb0a7f9a80fac8a4944") {
      return next(
        new NewAppError("CONFILCT", `Already processed or cancelled.`, 409)
      );
    }

    if (!req.file) {
      return next(new NewAppError("FILE_MISSING", `No file attached.`, 400));
    }
    const filePath = req.file.path;

    const workbook = xlsx.readFile(filePath);

    const sheetName = workbook.SheetNames[0];
    const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    const validate = shipmentUploadValidateService(jsonData);

    if (!validate.valid) {
      return next(new NewAppError("INVALID_INPUT", validate.error, 400));
    }

    const shipmentWithInvalidQuantity = jsonData.filter(
      (shipment) => !Number.isInteger(shipment.AllocatedQuantity)
    );
    if (shipmentWithInvalidQuantity.length > 0) {
      return next(
        new NewAppError(
          "INVALID_INPUT",
          "Some Items have Invalid AllocatedQuantity.",
          400
        )
      );
    }

    const skusSubsetOfShipment = validateSkusSubsetUtility(
      jsonData,
      existingShipment.lineItems
    );
    if (!skusSubsetOfShipment.valid) {
      return next(
        new NewAppError("INVALID_INPUT", skusSubsetOfShipment.error, 400)
      );
    }

    const allocatedMoreThanOrdered = await allocatedMoreThanOrderedCheck(
      existingShipment.lineItems,
      jsonData,
      "shipment"
    );

    if (!allocatedMoreThanOrdered) {
      return next(
        new NewAppError("SERVER_ERROR", "Error while validating input.", 500)
      );
    }
    if (!allocatedMoreThanOrdered.valid) {
      return next(
        new NewAppError(
          "INVALID_INPUT",
          `Allocated quantity cannot be greater than order quantity for skus:${allocatedMoreThanOrdered.errors.map(
            (sku) => sku
          )}.`,
          400
        )
      );
    }

    const validateAllocationWithERP =
      await validateAllocationQuantityWithERPService(jsonData);

    if (validateAllocationWithERP instanceof NewAppError) {
      return next(validateAllocationWithERP);
    }

    if (!validateAllocationWithERP.allocationValid) {
      return next(
        new NewAppError(
          "INVALID_INPUT",
          `Allocated quantity not Available on ERP for skus:${validateAllocationWithERP.skusWithInvalidAllocation.join()}`,
          400
        )
      );
    }

    const comparison = compareShipmentItems(
      jsonData,
      existingShipment.lineItems
    );

    if (comparison instanceof NewAppError) {
      return next(comparison);
    }

    //for naming
    const allShipments = await Shipment.find({
      order: existingShipment.order,
    });
    const order = await Order.findOne({ _id: existingShipment.order });

    if (!order || allShipments.length <= 0) {
      return next(new NewAppError("MISSING", "Missing required data.", 404));
    }

    //Reference InProgress
    //New Order status after shipment split.
    const statusToAlotOnOrder = await Status.findOne({
      _id: new mongoose.Types.ObjectId("674e8bf7ce77438d64f79c8f"),
    });

    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });

    const length = allShipments.length;
    let splitShipmentIds = [];
    let createdShipment;
    let updatedShipment;
    if (comparison.updatedShipment.length > 0) {
      const updatedShipmentAmountTotal = comparison.updatedShipment.reduce(
        (acc, lineItem) => {
          let itemTotal = (lineItem.fulfilled || 0) * (lineItem.price || 0);
          return acc + itemTotal;
        },
        0
      );

      updatedShipment = await Shipment.findByIdAndUpdate(
        shipmentId,
        {
          $set: {
            lineItems: comparison.updatedShipment.filter((x) => x.requested),
            status: nextStatus._id,
            amount: updatedShipmentAmountTotal,
          },
          $push: {
            timeline: {
              time: new Date(),
              comment: `Shipment has been edited by ${current_user.name}`,
            },
            status_change_history: {
              status: nextStatus._id,
            },
          },
        },
        { new: true }
      );

      const createShipmentOnERP = await createERPOrderController(shipmentId);
      if (createShipmentOnERP instanceof NewAppError) {
        next(createShipmentOnERP);
      }
      const addErpIdentifier = await addErpIdentifierShipmentDbService(
        shipmentId,
        createShipmentOnERP
      );
      if (addErpIdentifier instanceof NewAppError) {
        next(addErpIdentifier);
      }

      splitShipmentIds.push({ updatedShipmentId: shipmentId });
    }

    if (comparison.remainingItemsShipment.length > 0) {
      const amountTotal = comparison.remainingItemsShipment.reduce(
        (acc, lineItem) => {
          let itemTotal = (lineItem.fulfilled || 0) * (lineItem.price || 0);
          return acc + itemTotal;
        },
        0
      );
      const nonAligned = {
        name: order.name + `_${length + 1}`,
        order: order._id,
        distributor: order.distributor,
        status: currentStatus._id,
        amount: amountTotal,
        initial_status: "non-aligned",
        lineItems: comparison.remainingItemsShipment,
        timeline: [
          {
            time: new Date(),
            comment: `Non-Aligned shipment created from Shipment:${existingShipment.name}`,
          },
        ],
      };

      createdShipment = await new Shipment(nonAligned).save();
      const createdShipmentId = createdShipment._id.toString();
      const updateorder = await Order.findByIdAndUpdate(
        existingShipment.order,
        {
          $push: {
            timeline: {
              time: new Date(),
              comment: `Non-Aligned shipment:${nonAligned.name} created from Shipment:${existingShipment.name}`,
            },
            status_change_history: {
              status: statusToAlotOnOrder._id,
            },
          },
        }
      );
      splitShipmentIds.push({ createdShipmentId });
    }

    if (existingShipment.escalation_status) {
      await endEscalationStatus(existingShipment);
    }

    const notify = await shipmentEmailNotification(
      order,
      updatedShipment,
      createdShipment,
      current_user,
      nextStatus,
      currentStatus
    );

    if (notify instanceof NewAppError) {
      return next(notify);
    }

    fs.unlinkSync(filePath);
    res
      .status(201)
      .json({ responseCode: 0, status: "success", data: splitShipmentIds });
  } catch (error) {
    console.log(error.message, "error");
    return next(new NewAppError("SERVER_ERROR", `Something went wrong.`, 500));
  }
};

export const autoAllocateShipmentController = async (req, res, next) => {
  try {
    const { form_data, current_user, shipment_id, action_link_id } = req.body;

    const failedShipments = [];
    const successfulShipments = [];
    if (!shipment_id) {
      return new NewAppError(
        "BAD_REQUEST",
        `Pass atleast one shipment id.`,
        400
      );
    }

    const shipment = await Shipment.findOne({
      _id: shipment_id,
    });

    if (!shipment) {
      return next(
        new NewAppError(
          "NOT_FOUND",
          `No shipment found for provided names.`,
          404
        )
      );
    }

    if (!action_link_id) {
      return next(
        new NewAppError(
          "BAD_REQUEST",
          "Action link id and shipment id is mandatory.",
          400
        )
      );
    }

    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return next(
        new NewAppError(
          "MISSING_ACTION_LINK",
          "Status Action Link Data not present.",
          400
        )
      );
    }

    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });

    const current_shipment_status = shipment.status._id;
    // const current_actionable_deptt =
    //   shipment.status.departmentType._id.toString();

    // const current_user_deptt = current_user.departmentType.map((dept) =>
    //   dept._id.toString()
    // );
    // if (!current_user_deptt.includes(current_actionable_deptt)) {
    //   return next(
    //     new NewAppError("UNAUTHORIZED", "You aren't authorized.", 403)
    //   );
    // }
    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return next(
        new NewAppError("BAD_REQUEST", "Invalid status change request.", 400)
      );
    }

    //Reference InProgress
    //New Order status after shipment split.
    const statusToAlotOnOrder = await Status.findOne({
      _id: new mongoose.Types.ObjectId("674e8bf7ce77438d64f79c8f"),
    });

    const processShipments = await processShipmentService(
      shipment,
      current_user,
      nextStatus,
      currentStatus,
      statusToAlotOnOrder
    );
    if (processShipments instanceof NewAppError) {
      failedShipments.push({
        shipmentName: shipment.name,
        shipmentId: shipment._id,
        error: `Error while processing shipment.Message:${processShipments.message}`,
      });
    } else {
      successfulShipments.push({
        shipmentName: shipment.name,
        shipmentId: shipment._id,
        processShipments,
      });
    }

    res.status(200).send({
      responseCode: 0,
      status: "success",
      data: {
        successfulShipments,
        failedShipments,
        message: "Shipments Processed.",
      },
    });
  } catch (error) {
    console.log(error.message, "error");
    return next(
      new NewAppError(
        "SERVER_ERROR",
        `Something went wrong while auto Allocating.`,
        500
      )
    );
  }
};

export const autoAllocateBulkShipmentsController = async (req, res, next) => {
  try {
    const { current_user, shipmentIds } = req.body;
    const failedShipments = [];
    const successfulShipments = [];
    const shipments = await shipmentsToBulkProcess(shipmentIds, req);
    if (!shipments) {
      return next(
        new NewAppError(
          "SERVER_ERROR",
          `Error while processing shipments.`,
          500
        )
      );
    }

    if (shipments.length <= 0) {
      return next(new NewAppError("NOT_FOUND", `No shipments found.`, 404));
    }

    for (const shipment of shipments) {
      const processShipments = await processBulkShipmentsService(
        shipment,
        current_user
      );
      if (processShipments instanceof NewAppError) {
        failedShipments.push({
          shipmentName: shipment.name,
          shipmentId: shipment._id,
          error: `Error while processing shipment.Message:${processShipments.message}`,
        });
      } else {
        successfulShipments.push({
          shipmentName: shipment.name,
          shipmentId: shipment._id,
          processShipments,
        });
      }
    }

    res.status(200).send({
      responseCode: 0,
      status: "success",
      data: {
        successfulShipments,
        failedShipments,
        message: "Shipments Processed.",
      },
    });
  } catch (error) {
    return next(
      new NewAppError(
        "SERVER_ERROR",
        `Something went wrong while auto Allocating.`,
        500
      )
    );
  }
};

function compareItems(requestedItems, existingItems) {
  const existingItemsMap = new Map();

  existingItems.forEach(function (doc) {
    existingItemsMap.set(doc.sku, doc);
  });

  const updatedShipment = [];
  const remainingItemsShipment = [];

  let valid = true;
  let allitemExist = true;
  let emptyQuantity = false;
  let totalQuantity = 0;

  requestedItems.forEach((reqItem) => {
    if (valid && allitemExist) {
      const existingItem = existingItemsMap.get(reqItem.ProductCode);

      if (!(reqItem.Quantity || reqItem.Quantity === 0)) {
        emptyQuantity = true;
        return;
      }

      totalQuantity += reqItem.Quantity;
      if (!existingItem) {
        allitemExist = false;
        return;
      }
      const decrement = existingItem.requested - reqItem.Quantity;
      if (decrement < 0) {
        valid = false;
      } else {
        if (decrement === 0) {
          updatedShipment.push(existingItem);
        } else {
          updatedShipment.push({
            ...existingItem,
            requested: reqItem.Quantity,
            fulfilled: reqItem.Quantity,
          });
          remainingItemsShipment.push({
            ...existingItem,
            requested: decrement,
            fulfilled: 0,
          });
        }
      }
    }
  });

  if (totalQuantity === 0) {
    return new NewAppError(
      "BAD_REQUEST",
      "Enter valid Quantity, at least 1 sku should have quantity greater than 0.",
      400
    );
  }

  if (emptyQuantity) {
    return new NewAppError(
      "BAD_REQUEST",
      "All Quantity boxes should be filled, value can be 0.",
      400
    );
  }

  if (!valid) {
    return new NewAppError(
      "BAD_REQUEST",
      "Quantity can't be greater than previously allocated.",
      400
    );
  }

  if (!allitemExist) {
    return new NewAppError(
      "BAD_REQUEST",
      "Uploaded items must be same as downloaded.",
      400
    );
  }
  return {
    updatedShipment: updatedShipment,
    remainingItemsShipment: remainingItemsShipment,
  };
}

async function shipmentsToBulkProcess(shipmentIds, req) {
  try {
    let shipments = [];
    if (Array.isArray(shipmentIds) && shipmentIds.length > 0) {
      shipments = await Shipment.find({
        _id: { $in: shipmentIds },
      });
    } else if (req.salesPersonDept) {
      const user = req.user;
      const salesPersonDept = req.salesPersonDept;

      if (user && salesPersonDept) {
        const distributorIds = await Distributor.distinct("_id", {
          salespersonId: user._id.toString(),
        }); // Get distributors belonging to the salesperson

        // Find the status ID for pseudoId: "AWAITING_COURIER_PARTIAL"
        const status = await Status.findOne({
          pseudoId: "AWAITING_COURIER_PARTIAL",
        });

        // Fetch shipments with matching distributors and status
        shipments = await Shipment.find({
          distributor: { $in: distributorIds },
          status: status._id,
        });
      }
    } else {
      //Refrence AWAITING_COURIER_PARTIAL
      //below status is for all unaligned shipments.
      const status = await Status.findOne({
        pseudoId: "AWAITING_COURIER_PARTIAL",
      });
      shipments = await Shipment.find({
        status: status._id,
      });
    }
    return shipments;
  } catch (error) {
    console.log("Error", error.message);
    return null;
  }
}

export const editShipmentWithSla = async (req, res, next) => {
  try {
    const shipmentId = req.params.shipmentId;
    const { action_link_id, shipment_id, sla } = req.query;
    const { form_data, current_user } = req.body;

    if (!action_link_id || !shipment_id) {
      return res.send({ error: "Action link id and shipment id is mandatory" });
    }

    const shipment = await Shipment.findById(shipment_id).populate();

    if (!shipment) {
      return res.send({ error: "Shipment not found with id:" + shipment_id });
    }
    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return res.send({ error: "statusActionLinkData not present" });
    }

    const current_shipment_status = shipment.status._id;
    const current_actionable_deptt =
      shipment.status.departmentType._id.toString();
    const current_user_deptt = current_user.departmentType.map((dept) =>
      dept._id.toString()
    );

    if (!current_user_deptt.includes(current_actionable_deptt)) {
      return res.send({ error: "You aren't authorized" });
    }

    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return res.send({ error: "Invalid status change request" });
    }

    if (!req.file) return next(new AppError("No file attached"));
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);

    // Convert the first sheet to JSON
    const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
    const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    const existingShipment = await Shipment.findById(shipmentId);

    const comparison = compareItems(jsonData, existingShipment.lineItems);

    if (comparison.error) {
      return res.send({ error: comparison.error });
    }

    const allShipments = await Shipment.find({
      order: existingShipment.order._id,
    });

    let length = allShipments.length;

    const valuesToPushInOrder = [];

    const groupedBySla = comparison.updatedShipment
      .filter((x) => x.requested)
      .reduce((acc, curr) => {
        const index = acc.findIndex((item) => item[0]?.sla === curr.sla);
        if (index !== -1) {
          acc[index].push(curr);
        } else {
          acc.push([curr]);
        }

        return acc;
      }, []);

    if (shipment.escalation_status) {
      await endEscalationStatus(shipment);
    }

    for (let i = 0; i < groupedBySla.length; i++) {
      const subArray = groupedBySla[i];
      if (i === 0) {
        const updateShipment = await Shipment.findByIdAndUpdate(shipmentId, {
          $set: {
            lineItems: subArray,
            status: statusActionLinkData.next_status_id,
            sla: {
              days: sla ? Number(sla) : 0,
            },
          },
          $push: {
            timeline: {
              time: new Date(),
              comment: `Shipment has been edited by ${current_user.name}`,
            },
            status_change_history: {
              status: statusActionLinkData.next_status_id,
            },
          },
        });
      } else {
        const remaining = {
          name: existingShipment.order.name + `_${length + 1}`,
          order: existingShipment.order._id,
          status: statusActionLinkData.next_status_id, //this need to be dynamic
          amount: 5000,
          shipping_address: existingShipment.shipping_address,
          lineItems: comparison.remainingItemsShipment,
          timeline: [
            {
              time: new Date(),
              comment: `Awaiting with SLA shipment created from Shipment:${existingShipment.name}`,
            },
          ],
          status_change_history: [
            {
              status: statusActionLinkData.next_status_id,
            },
          ],
        };
        await new Shipment(remaining).save();
        length++;
        valuesToPushInOrder.push({
          time: new Date(),
          comment: `Shipment ${remaining.name} created with SLA.`,
        });
      }
    }

    if (comparison.remainingItemsShipment.length) {
      const nonAligned = {
        name: existingShipment.order.name + `_${length + 1}`,
        order: existingShipment.order._id,
        status: "663c4ac9864c679bd21974a8", //this need to be dynamic
        amount: 5000,
        shipping_address: existingShipment.shipping_address,
        lineItems: comparison.remainingItemsShipment,
        timeline: [
          {
            time: new Date(),
            comment: `Non-Aligned shipment created from Shipment:${existingShipment.name}`,
          },
        ],
        status_change_history: [
          {
            status: "663c4ac9864c679bd21974a8", //this need to be dynamic
          },
        ],
      };
      await new Shipment(nonAligned).save();
    }

    const updateorder = await Order.findByIdAndUpdate(
      existingShipment.order._id,
      {
        $set: {
          status: "664372f129f6db844ee8bc0b", //this need to be dynamic
        },
        $push: { timeline: { $each: valuesToPushInOrder } },
      }
    );

    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });
    const departmentPeoples = await Department.find({
      departmentType: { $in: [nextStatus.departmentType._id] },
    });

    const distributerId = shipment.order.distributor._id;
    const distributor = await Distributor.findOne({ _id: distributerId });

    const departmentNotified = await getDepartmentNotifiedEmails(nextStatus);

    const recipients = departmentPeoples.map((departmentPeople) => ({
      name: departmentPeople.name,
      email: departmentPeople.email,
      cc: [...departmentNotified],
    }));

    if (recipients.length > 0) {
      await EmailNotification.create({
        emailCategory: "SHIPMENT",
        emailType: "SHIPMENT_STATUS_CHANGE",
        reciepient: recipients,
        emailPayload: {
          orderName: shipment.order.name,
          distributorName: distributor.name,
          date: shipment.order.createdAt,
          shipmentName: shipment.name,
          shipmentStatus: nextStatus.status,
          shipmetRef: shipment.ref,
          currentStatus: currentStatus.status,
          nextStatus: nextStatus.status,
          actionByUser: current_user.name,
        },
      });
    }

    //*delete file
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error("Error deleting file:", err);
      } else {
        console.log("File deleted successfully");
      }
    });

    res.status(201).json({ success: true });
  } catch (error) {
    console.log("editShipmentError", error);
    res.send({ error: "Something went wrong" });
  }
};

// calculate the shipments aligned
export const shipmentsAligned = async (req, res) => {
  try {
    const currentDate = new Date();
    const pastDate = new Date();
    pastDate.setDate(currentDate.getDate() - 30);

    const previousMonth = new Date(pastDate);
    previousMonth.setDate(pastDate.getDate() - 30);

    const orderIds = await Order.distinct("_id", {
      createdAt: {
        $gte: pastDate,
        $lte: currentDate,
      },
    });

    const pastOrderIds = await Order.distinct("_id", {
      createdAt: {
        $gte: previousMonth,
        $lte: pastDate,
      },
    });

    const pastShipments = await Shipment.find({ order: { $in: pastOrderIds } });
    const shipments = await Shipment.find({ order: { $in: orderIds } });
    const calculateShipments = calculateShipmentStats(shipments);
    const calculatePreviousShipments = calculateShipmentStats(pastShipments);

    const totalShipments = Object.values(calculateShipments).reduce(
      (a, b) => a + b,
      0
    );
    const totalPreviousShipments = Object.values(
      calculatePreviousShipments
    ).reduce((a, b) => a + b, 0);

    const statusArray = Object.keys(calculateShipments).map((status) => {
      const currentCount = calculateShipments[status] || 0;
      const previousCount = calculatePreviousShipments[status] || 0;
      const currentPercentage = (currentCount / totalShipments) * 100;
      const previousPercentage = (previousCount / totalPreviousShipments) * 100;
      const percentageChange =
        ((currentCount - previousCount) / (previousCount || 1)) * 100;
      const changeType = percentageChange > 0 ? "increased" : "decreased";

      return {
        status,
        currentCount,
        previousCount,
        currentPercentage,
        previousPercentage,
        percentageChange,
        totalPreviousShipments,
        totalShipments,
        changeType,
      };
    });

    res.send(statusArray);
  } catch (error) {
    console.error(error);
    res.status(500).send("Something went wrong.");
  }
};

export function calculateShipmentStats(shipments) {
  const shipmentsByOrder = shipments.reduce((acc, shipment) => {
    const orderId = shipment.order._id.toString();
    if (!acc[orderId]) {
      acc[orderId] = [];
    }
    acc[orderId].push(shipment);
    return acc;
  }, {});

  const processedShipments = [];
  for (const orderId in shipmentsByOrder) {
    const orderShipments = shipmentsByOrder[orderId];
    const initialStatuses = new Set(
      orderShipments.map((shipment) => {
        if (shipment.initial_status != undefined)
          return shipment.initial_status;
      })
    );
    initialStatuses.delete(undefined);

    const isPartialAligned = initialStatuses.size > 1;

    orderShipments.forEach((shipment) => {
      const finalStatus = isPartialAligned
        ? "partial-aligned"
        : shipment.initial_status;
      processedShipments.push({ ...shipment, final_status: finalStatus });
    });
  }

  // const statusCounts = processedShipments.reduce((acc, shipment) => {
  //   const status = shipment.final_status;
  //   if (!acc[status]) {
  //     acc[status] = 0;
  //   }
  //   acc[status]++;
  //   return acc;
  // }, {});

  const statusCounts = processedShipments.reduce(
    (acc, shipment) => {
      const orderName = shipment.order.name;
      const status = shipment.final_status;

      if (!acc.uniqueOrders[orderName]) {
        acc.uniqueOrders[orderName] = true;
        if (!acc.statusCounts[status]) {
          acc.statusCounts[status] = 0;
        }
        acc.statusCounts[status]++;
      }

      return acc;
    },
    { uniqueOrders: {}, statusCounts: {} }
  ).statusCounts;
  return statusCounts;
}

// export const shipmentsSummary = async (req, res) => {
//   try {
//     const currentDate = new Date();
//     const pastDate = new Date();
//     pastDate.setDate(currentDate.getDate() - 30);

//     const previousMonth = new Date(pastDate);
//     previousMonth.setDate(pastDate.getDate() - 30);

//     const [shipments, pastShipments] = await Promise.all([
//       Shipment.find({
//         createdAt: {
//           $gte: pastDate,
//           $lte: currentDate
//         }
//       }),
//       Shipment.find({
//         createdAt: {
//           $gte: previousMonth,
//           $lte: pastDate,
//         }
//       })
//     ]);

//     const calculateCounts = (shipments) => {
//       let nonAlignedCount = 0;
//       let fulfilledCount = 0;
//       let nonFulfilledCount = 0;

//       shipments.forEach((value) => {
//         if (value.initial_status === "non-aligned") nonAlignedCount++;
//         if (value.status.flowType === "happy") fulfilledCount++;
//         if (value.status.flowType === "unhappy") nonFulfilledCount++;
//       });

//       return { nonAlignedCount, fulfilledCount, nonFulfilledCount };
//     };

//     const currentCounts = calculateCounts(shipments);
//     const pastCounts = calculateCounts(pastShipments);

//     const createShipmentStats = (currentCount, pastCount, totalCurrent, totalPast) => ({
//       count: currentCount,
//       previousCount: pastCount,
//       totalCount: currentCount + pastCount,
//       changeType: (((currentCount - pastCount) / (pastCount || 1)) * 100) > 0 ? 'increased' : 'decreased',
//       percentageChange: ((currentCount - pastCount) / (pastCount || 1)) * 100,
//       currentPercentage: (currentCount / totalCurrent) * 100,
//       previousPercentage: (pastCount / totalPast) * 100
//     });

//     const totalShipments = shipments.length;
//     const totalPreviousShipments = pastShipments.length;

//     const result = [
//       {
//         type: 'Total Non-Aligned Shipments',
//         ...createShipmentStats(currentCounts.nonAlignedCount, pastCounts.nonAlignedCount, totalShipments, totalPreviousShipments)
//       },
//       {
//         type: 'Fulfilled using Domestic Divergence',
//         ...createShipmentStats(currentCounts.fulfilledCount, pastCounts.fulfilledCount, totalShipments, totalPreviousShipments)
//       },
//       {
//         type: 'Pending',
//         ...createShipmentStats(currentCounts.nonFulfilledCount, pastCounts.nonFulfilledCount, totalShipments, totalPreviousShipments)
//       }
//     ];

//     res.send(result);

//   } catch (err) {
//     res.send("Something went wrong.");
//   }

// };

export const shipmentsSummary = async (req, res) => {
  try {
    const currentDate = new Date();
    const pastDate = new Date();
    pastDate.setDate(currentDate.getDate() - 30);

    const previousMonth = new Date(pastDate);
    previousMonth.setDate(pastDate.getDate() - 30);

    const [orders, pastOrders] = await Promise.all([
      Order.find({
        createdAt: {
          $gte: pastDate,
          $lte: currentDate,
        },
      }),
      Order.find({
        createdAt: {
          $gte: previousMonth,
          $lte: pastDate,
        },
      }),
    ]);

    const orderNotFullyAligned = orders.map((value) => {
      if (value.status.pseudoId != "ORDER_FULLY_ALIGNED") {
        return value;
      }
    });

    const pastOrderNotFullyAligned = pastOrders.map((value) => {
      if (value.status.pseudoId != "ORDER_FULLY_ALIGNED") {
        return value;
      }
    });

    const calculateCounts = (orders) => {
      let nonAlignedCount = 0;
      let completedCount = 0;

      orders.forEach((value) => {
        if (value.status.pseudoId != "ORDER_COMPLETED") nonAlignedCount++;
        if (value.status.pseudoId == "ORDER_COMPLETED") completedCount++;
      });

      return { nonAlignedCount, completedCount };
    };

    const currentCounts = calculateCounts(orderNotFullyAligned);
    const pastCounts = calculateCounts(pastOrders);

    const createShipmentStats = (
      currentCount,
      pastCount,
      totalCurrent,
      totalPast
    ) => ({
      count: currentCount,
      previousCount: pastCount,
      totalCount: currentCount + pastCount,
      changeType:
        ((currentCount - pastCount) / (pastCount || 1)) * 100 > 0
          ? "increased"
          : "decreased",
      percentageChange: ((currentCount - pastCount) / (pastCount || 1)) * 100,
      currentPercentage: (currentCount / totalCurrent) * 100,
      previousPercentage: (pastCount / totalPast) * 100,
    });

    const totalOrders = orders.length;
    const totalPreviousOrders = pastOrders.length;

    const result = [
      {
        type: "Total Non-Aligned Shipments",
        ...createShipmentStats(
          orderNotFullyAligned.length,
          pastOrderNotFullyAligned.length,
          totalOrders,
          totalPreviousOrders
        ),
      },
      {
        type: "Fulfilled using Domestic Divergence",
        ...createShipmentStats(
          currentCounts.completedCount,
          pastCounts.completedCount,
          totalOrders,
          totalPreviousOrders
        ),
      },
      {
        type: "Pending",
        ...createShipmentStats(
          currentCounts.nonAlignedCount,
          pastCounts.nonAlignedCount,
          totalOrders,
          totalPreviousOrders
        ),
      },
    ];

    res.send(result);
  } catch (err) {
    res.send("Something went wrong.");
  }
};

export const allocateInventoryManually = async (req, res) => {
  try {
    const { action_link_id, shipment_id } = req.body;

    if (!action_link_id || !shipment_id) {
      return res.send({ error: "Action link id and shipment id is mandatory" });
    }
    const shipment = await Shipment.findById(shipment_id).populate();

    if (!shipment) {
      return res.send({ error: "Shipment not found with id:" + shipment_id });
    }
    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return res.send({ error: "statusActionLinkData not present" });
    }
    const result = await alignmentPendingOrderProcessTrigger(
      [shipment.name],
      statusActionLinkData
    );
    res.status(200).json({
      error: `All shipments are processed successfully ${result.createdShipments.length} new shipments created and ${result.updatedShipments.length} shipments updated`,
      data: result,
    });
  } catch (error) {
    console.log("processOrderError", error);
    return res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
};

export async function cancelPendingShipmentController(req, res, next) {
  try {
    const { shipmentId } = req.params;
    const { form_data, current_user, action_link_id } = req.body;

    //Reference :Awaiting Courier Partial
    //hardcoded staus for shipment non aligned.Needs to be dynamic.

    const nonAlignedStatus = "673afbb0a7f9a80fac8a4944";
    const shipment = await Shipment.findById(shipmentId);
    if (!shipment) {
      return next(
        new NewAppError(
          "BAD_REQUEST",
          `Shipment not found for provide shipmentId:${shipmentId}.`,
          400
        )
      );
    }

    if (!action_link_id) {
      return next(
        new NewAppError("BAD_REQUEST", "Action link id and is mandatory.", 400)
      );
    }

    const existingShipment = await Shipment.findById(shipmentId);

    if (!existingShipment) {
      return next(
        new NewAppError(
          "NOT_FOUND",
          `Shipment not found with id:${shipmentId}.`,
          404
        )
      );
    }

    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return next(
        new NewAppError(
          "MISSING_ACTION_LINK",
          "Status Action Link Data not present.",
          400
        )
      );
    }
    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });

    const current_shipment_status = existingShipment.status._id;
    // const current_actionable_deptt =
    //   existingShipment.status.departmentType._id.toString();
    // const current_user_deptt = current_user.departmentType.map((dept) =>
    //   dept._id.toString()
    // );
    // if (!current_user_deptt.includes(current_actionable_deptt)) {
    //   return next(
    //     new NewAppError("UNAUTHORIZED", "You aren't authorized.", 403)
    //   );
    // }
    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return next(
        new NewAppError("BAD_REQUEST", "Invalid status change request.", 400)
      );
    }

    if (currentStatus._id.toString() !== nonAlignedStatus) {
      return next(
        new NewAppError(
          "BAD_REQUEST",
          `Only non-aligned pending Shipment can be cancelled.`,
          400
        )
      );
    }

    const cancelledShipment = await cancelPendingShipmentService(
      shipment,
      nextStatus
    );

    if (cancelledShipment instanceof NewAppError) {
      return next(cancelledShipment);
    }

    res.status(200).send({
      responseCode: 0,
      status: "success",
      data: { cancelledShipment },
    });
  } catch (error) {
    console.log(error.message);
    return next(new NewAppError("SERVER_ERROR", `Something went wrong.`, 500));
  }
}

