import express from 'express';
import { createOrder, getCompanyList } from '../controller/shopify.controller.js';
import multer from "multer";
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);


const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '..', 'asset', 'upload', 'order'));
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname.split(" ").join("_")); // Retains the original file name
  },
});

const upload = multer({ storage });
const router = express.Router();

router
  .route('/order')
  .post(createOrder);

router
  .route('/company')
  .post(getCompanyList);



// router.post('/upload', upload.single('file'), createCartFromSheet);

export default router;