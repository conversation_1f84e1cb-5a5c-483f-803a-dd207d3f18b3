import Inventory from "../model/inventory.model.js";

export const calculatePMRType = async (req, res) => {
    try {
        const currentDate = new Date();
        const pastDate = new Date();
        pastDate.setDate(currentDate.getDate() - 30);
    
        const pmrData = await Inventory.aggregate([
            {
              $match: {
                createdAt: {
                  $gte: pastDate,
                  $lte: currentDate
                },
                pmrStatus: { $in: ['new', 'active', 'dropped'] }
              }
            },
            {
              $group: {
                _id: "$pmrStatus",
                count: { $sum: 1 }
              }
            },
            {
              $group: {
                _id: null,
                total: { $sum: "$count" },
                statuses: { 
                  $push: { 
                    status: "$_id", 
                    count: "$count" 
                  }
                }
              }
            },
            {
              $unwind: "$statuses"
            },
            {
              $project: {
                _id: 0,
                status: "$statuses.status",
                count: "$statuses.count",
                total: 1,
                percentage: { 
                  $multiply: [{ $divide: ["$statuses.count", "$total"] }, 100]
                }
              }
            }
          ]);
      
          const statuses = {
            new: { count: 0, percentage: 0 },
            active: { count: 0, percentage: 0 },
            dropped: { count: 0, percentage: 0 }
          };
      
          pmrData.forEach(item => {
            statuses[item.status] = {
              count: item.count,
              percentage: item.percentage
            };
          });

          const totalCount = pmrData.length > 0 ? pmrData[0].total : 0;
      
          const result = Object.keys(statuses).map(status => ({
            total: totalCount,
            pmrStatus: status,
            count: statuses[status].count,
            percentage: statuses[status].percentage
          }));
      
      
          
      res.send(result);
    } catch (error) {
      res.send({ error: "Something went wrong" });
    }
};  

