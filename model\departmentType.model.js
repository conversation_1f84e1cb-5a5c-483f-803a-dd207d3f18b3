import mongoose from "mongoose";
import { toScreamingSnakeCase } from "../utils/helperFunction.js";

const departmentSchema = new mongoose.Schema({
  department: {
    type: String,
  },
  pseudoId: {
    type: String,
    index: true
  }
}, { timestamps: true });

departmentSchema.pre("save", async function (next) {
  this.pseudoId = toScreamingSnakeCase(this.department)
  next();
});

const DepartmentType = mongoose.model("DepartmentType", departmentSchema);
export default DepartmentType;
