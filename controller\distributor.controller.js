import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import fs from "fs";
import mongoose from "mongoose";
import axios from "axios";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import Distributor from "../model/distributor.model.js";
import DepartmentType from "../model/departmentType.model.js";
import catchAsync from "../utils/catchAsync.js";
import APIFeatures from "../utils/apiFeatures.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import {
  createOne,
  deleteOne,
  getAll,
  getOne,
  searchWithCustomFilters,
  updateOne,
} from "../utils/controllerFactory.js";
import AppError from "../utils/appError.js";
import EmailNotification from "../model/emailNotification.model.js";
import Order from "../model/order.model.js";
import Department from "../model/department.model.js";
import {
  COMPANY_LOCATION_QUERY,
  CONTACT_ROLES_QUERY,
  CONTACTS_QUERY,
  ASSIGN_ROLE_MUTATION,
  VERIFY_META_TRUE_MUTATION,
  VERIFY_META_FALSE_MUTATION,
  CHECK_COMPANY_QUERY,
  LOCATION_ADDRESS_MUTATION,
  COMPANY_REVOKE_ROLE,
} from "../queries/internalQueries.js";

import { uploadToS3 } from "../utils/helperFunction.js";
import {
  GetDistributorRecord,
  UpdateDistributorRecord,
} from "../service/distributor/companyDistributor.service.js";
import { ConvertFromGID, ConvertToGID } from "../helpers/companyHelper.js";
import NewAppError from "../utils/newAppError.js";
import {
  revokeCustomerCompanyLocationRoleService,
  updateCustomerService,
} from "../service/ERPSync/customerService.js";
import {
  downloadDistributorSheetService,
  uploadDistributorSheetService,
} from "../service/distributor/companyDistributor.service.js";
import { createCustomersController } from "./erp.customer.controller.js";

// Helper function to update salesperson metafields
const updateSalespersonMetafields = async (
  shopifyCustomerId,
  salespersonInfo
) => {
  try {
    const metafields = [];

    if (salespersonInfo.email) {
      metafields.push({
        key: "sales_person_email",
        namespace: "custom",
        ownerId: `gid://shopify/Customer/${shopifyCustomerId}`,
        type: "single_line_text_field",
        value: salespersonInfo.email,
      });
    }

    if (salespersonInfo.name) {
      metafields.push({
        key: "sales_person_name",
        namespace: "custom",
        ownerId: `gid://shopify/Customer/${shopifyCustomerId}`,
        type: "single_line_text_field",
        value: salespersonInfo.name,
      });
    }

    if (metafields.length > 0) {
      const metafieldResponse = await fetchGraphqlDataShopify(
        VERIFY_META_TRUE_MUTATION,
        { metafields }
      );
      return true;
    }
    return false;
  } catch (error) {
    console.error("Error updating salesperson metafields:", error);
    return false;
  }
};

// Helper function to perform rollback operations for external systems
const performRollback = async (transactionState) => {
  console.log("Starting rollback operations...", transactionState);

  try {
    // Rollback role assignment if it was successful
    if (
      transactionState.roleAssigned &&
      transactionState.assignedRoleAssignmentIds.length > 0 &&
      transactionState.assignedCompanyLocationId
    ) {
      try {
        const revokeResponse = await fetchGraphqlDataShopify(
          COMPANY_REVOKE_ROLE,
          {
            companyLocationId: transactionState.assignedCompanyLocationId,
            rolesToRevoke: transactionState.assignedRoleAssignmentIds,
          }
        );

        // Check if the response has errors at the top level
        if (revokeResponse.errors && revokeResponse.errors.length > 0) {
          console.error(
            "ROLLBACK FAILED: GraphQL errors in role revocation:",
            revokeResponse.errors
          );
        } else if (
          revokeResponse.data &&
          revokeResponse.data.companyLocationRevokeRoles
        ) {
          // Check for user errors in the mutation response
          if (
            revokeResponse.data.companyLocationRevokeRoles.userErrors?.length >
            0
          ) {
            console.error(
              "ROLLBACK WARNING: Role revocation had user errors:",
              revokeResponse.data.companyLocationRevokeRoles.userErrors
            );
          } else {
            console.log("ROLLBACK SUCCESS: Role assignment reverted");
          }
        } else {
          console.error(
            "ROLLBACK FAILED: Unexpected response structure - companyLocationRevokeRoles is null"
          );
        }
      } catch (rollbackError) {
        console.error(
          "ROLLBACK FAILED: Error revoking role assignment:",
          rollbackError.message
        );
      }
    }

    // Note: We cannot rollback ERP operations or address updates easily
    // These would need to be handled by the respective systems
    if (transactionState.erpSynced) {
      console.warn(
        "ROLLBACK WARNING: ERP sync cannot be automatically rolled back"
      );
    }

    if (transactionState.addressUpdated) {
      console.warn(
        "ROLLBACK WARNING: Address update cannot be automatically rolled back"
      );
    }

    if (transactionState.metafieldUpdated) {
      console.warn(
        "ROLLBACK WARNING: Metafield update cannot be automatically rolled back"
      );
    }
  } catch (error) {
    console.error("Error during rollback operations:", error.message);
  }
};

export const getDistributors = getAll(Distributor);
export const filterDistributors = searchWithCustomFilters(Distributor);
export const getOneDistributor = getOne(Distributor);
// export const createDistributor = createOne(Distributor);
export const updateDistributor = updateOne(Distributor);
export const deleteDistributor = deleteOne(Distributor);

export const updateDistributorInOmsErpController = async (req, res, next) => {
  try {
    if (!req.params.id) {
      return next(
        new NewAppError("BAD_REQUEST", "Missing required parameters.", 400)
      );
    }

    //Updates customer oms db
    const doc = await Distributor.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    //updates customer ERP.If edit after customer created on SAGE.
    if (doc.status === "Customer Created in Sage") {
      const response = await updateCustomerService(doc);

      if (response.status !== 204) {
        return next(
          new NewAppError(response.code, response.message, response.status)
        );
      }
    }
    res.status(200).json({
      status: "success",
      result: 1,
      data: {
        data: doc,
      },
    });
  } catch (error) {
    return next(
      new NewAppError("SERVER_ERROR", "Could not update customer.", 500)
    );
  }
};
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const getDeptWiseDistributors = catchAsync(async (req, res, next) => {
  const user = req.user;
  const salesPersonDept = req.salesPersonDept;
  const salesPersonsUnderCoordinator = req.salesPersonsUnderCoordinator;

  let query = Distributor.find();
  let totalCount = await Distributor.countDocuments();

  // CASE 1: Direct salesperson
  if (user && salesPersonDept) {
    totalCount = await Distributor.countDocuments({
      salespersonId: user._id.toString(),
    });
    query = Distributor.find({ salespersonId: user._id.toString() });
  }

  // CASE 2: Sunrise coordinator
  if (
    user &&
    !salesPersonDept &&
    salesPersonsUnderCoordinator &&
    salesPersonsUnderCoordinator.length > 0
  ) {
    const salesPersonIds = salesPersonsUnderCoordinator.map((sp) =>
      sp._id.toString()
    );

    totalCount = await Distributor.countDocuments({
      salespersonId: { $in: salesPersonIds },
    });
    query = Distributor.find({ salespersonId: { $in: salesPersonIds } });
  }
  // Populate salespersonId with Department name
  query = query.populate({
    path: "salespersonId",
    model: "Department",
    select: "name",
  });

  const features = new APIFeatures(query, req.query)
    .filter()
    .sort()
    .limitFields()
    .paginate();

  const doc = await features.query;
  // Map the data to include salesperson name
  const dataWithSalesperson = doc.map((distributor) => {
    const salesPerson = distributor.salespersonId;
    return {
      ...distributor.toObject(),
      salespersonName: salesPerson?.name || null,
    };
  });

  res.status(200).json({
    status: "success",
    result: totalCount,
    data: {
      data: dataWithSalesperson,
    },
  });
});

const ObjectId = mongoose.Types.ObjectId;

export const getUserToken = catchAsync(async (req, res, next) => {
  try {
    // Check for the Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res
        .status(401)
        .json({ message: "Unauthorized: Token missing or invalid" });
    }
    const token = authHeader.split(" ")[1];
    const decoded = jwt.verify(token, process.env.JWT_TOKEN_SECRET);
    req.user = decoded.userId;
    next();
  } catch (error) {
    console.error("Error in auth middleware:", error);
    return res.status(401).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "UNAUTHORIZED",
          message: "Unauthorized: Inavalid Token.",
        },
      ],
    });
  }
});
export const createDistributor = catchAsync(async (req, res, next) => {
  const token = req.body.token;
  if (!req.body.password) {
    return res.status(400).json({ message: "Password is required" });
  }
  const createdDistributor = await Distributor.create(req.body);
  createdDistributor.password = undefined;
  res.status(201).json({ createdDistributor, token });
});

export const removeEmailFromPayload = catchAsync(async (req, res, next) => {
  const myHeaders = new Headers();
  myHeaders.append(
    "X-Shopify-Access-token",
    `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`
  );

  const requestOptions = {
    method: "GET",
    headers: myHeaders,
    redirect: "follow",
  };

  const responsePromise = await fetch(
    `${process.env.SHOP_URL}/admin/api/2021-07/customers/search.json?query=${req.body.email}&fields=email`,
    requestOptions
  );
  const response = await responsePromise.json();

  if (response.customers.length > 0) {
    return next(
      new AppError("Cannot update email. As same email already exists.", 400)
    );
  }
  next();
});

export const updateDistributorPriority = catchAsync(async (req, res, next) => {
  const distributorData = req.body.distributors;
  const bulkOps = distributorData.map((update) => ({
    updateOne: {
      filter: { _id: update._id },
      update: { $set: { priority: update.priority } },
    },
  }));

  const result = await Distributor.bulkWrite(bulkOps);
  res.status(200).json({
    status: "success",
    // matchedCount: result.matchedCount,
    // modifiedCount: result.modifiedCount
    data: result,
  });
});

export const createCustomerInShopify = async (req, res, next) => {
  try {
    const customerDetails = req.body;
    if (
      !customerDetails.shopifyCompanyId ||
      !customerDetails.email ||
      !customerDetails.firstName ||
      !customerDetails.lastName ||
      !customerDetails.phone
    ) {
      return next(
        new AppError(
          "Missing any of the following required fields [shopifyCompanyId, email, firstName, lastName, phone]",
          400
        )
      );
    }
    if (customerDetails.shopifyCompanyId.toString().includes("gid")) {
      return next(new AppError("Invalid CompanyId", 400));
    }
    const data = JSON.stringify({
      query: `mutation companyContactCreate($companyId: ID!, $input: CompanyContactInput!) {
        companyContactCreate(companyId: $companyId, input: $input) {
          companyContact {
            id
            customer {
              id
            }
          }
          userErrors {
            field
            message
          }
        }
      }`,
      variables: {
        companyId: `gid://shopify/Company/${customerDetails.shopifyCompanyId}`,
        input: {
          email: `${customerDetails.email}`,
          firstName: `${customerDetails.firstName}`,
          lastName: `${customerDetails.lastName}`,
          // "phone": `${customerDetails.phone}`
        },
      },
    });

    const config = {
      method: "post",
      url: `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
        "Content-Type": "application/json",
      },
      data: data,
    };
    const customerCreateResponse = await axios(config);
    const customerId =
      customerCreateResponse.data?.data?.companyContactCreate?.companyContact?.customer?.id
        ?.split("/")
        ?.pop();

    if (!customerId) {
      return next(
        new AppError(
          `Error occured while creating customer in shopify [${customerCreateResponse?.data?.data?.companyContactCreate?.userErrors[0]?.message}]`,
          400
        )
      );
    }

    // const activationUrlConfig = {
    //   method: 'post',
    //   url: `${process.env.SHOP_URL}/admin/api/2024-01/customers/${customerId}/account_activation_url.json`,
    //   headers: {
    //     'X-Shopify-Access-Token': `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
    //     'Content-Type': 'application/json'
    //   },
    //   data: JSON.stringify({})
    // };
    // console.log('herew22');

    // const accountActivationUrlResponse = await axios(activationUrlConfig);
    // const activationUrl = accountActivationUrlResponse.data?.account_activation_url;
    // console.log('accountActivationUrlResponse', accountActivationUrlResponse);
    // console.log('activationUrl', activationUrl);
    // if (activationUrl) {
    //   await EmailNotification.create(({
    //     emailCategory: 'CUSTOMER',
    //     emailType: 'CUSTOMER_ACCOUNT_ACTIVATION',
    //     reciepient: {
    //       name: customerDetails.firstName,
    //       email: customerDetails.email
    //     },
    //     emailPayload: {
    //       activationUrl: activationUrl
    //     },
    //   }));
    // }
    req.body.shopifyCustomerId = customerId;
    // return customerCreateResponse.data;
    next();
  } catch (error) {
    console.error(error);
    return next(new AppError(error, 400));
  }
};

export const createCustomerAddressShopify = async (req, res, next) => {
  const customerId = req.body.shopifyCustomerId;
  const distributorId = req.body.distributor;
  const first_name = req.body.first_name;
  const last_name = req.body.last_name;
  const company = req.body.company || "";
  const address1 = req.body.address1;
  const address2 = req.body.address2 || "";
  const city = req.body.city;
  const province = req.body.province;
  const country = req.body.country;
  const zip = req.body.zip;
  const province_code = req.body.province_code || "";
  const country_code = req.body.country_code || "";
  const isDefault = req.body.isDefault || false;
  const country_name = req.body.country_name || "";
  if (
    !customerId ||
    !first_name ||
    !last_name ||
    !address1 ||
    !distributorId ||
    !city ||
    !province ||
    !country ||
    !zip
  ) {
    return next(
      new AppError(
        "Missing any of the following field [distributor, shopifyCustomerId, first_name, last_name, address1, city, province, country, zip]",
        400
      )
    );
  }
  const data = JSON.stringify({
    customer_address: {
      first_name: first_name,
      last_name: last_name,
      company: company,
      address1: address1,
      address2: address2,
      city: city,
      // "province": province,
      country: country,
      zip: zip,
      // "phone": "************",
      // "province_code": province_code,
      country_code: country_code,
      country_name: country_name,
      default: isDefault,
    },
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/customers/${customerId}/addresses.json`,
    headers: {
      "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config)
    .then(function (response) {
      req.body.shopifyAddressId = response.data.customer_address.id;
      next();
    })
    .catch(function (error) {
      console.log(error);
      return next(
        new AppError(
          error?.response?.data?.errors
            ? JSON.stringify(error?.response?.data?.errors)
            : error
        )
      );
    });
};

export const updateCustomerAddressShopify = async (req, res, next) => {
  const customerId = req.body.shopifyCustomerId;
  const addressId = req.body.shopifyAddressId;
  if (!customerId || !addressId) {
    return next(
      new AppError(
        "Missing any of the following field [shopifyCustomerId, shopifyAddressId]",
        400
      )
    );
  }
  const payload = req.body;
  var data = JSON.stringify({
    customer_address: payload,
  });

  var config = {
    method: "put",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/customers/${customerId}/addresses/${addressId}.json`,
    headers: {
      "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
      "Content-Type": "application/json",
    },
    data: data,
  };
  axios(config)
    .then(function (response) {
      next();
    })
    .catch(function (error) {
      console.log(error);
      return next(
        new AppError(
          error?.response?.data?.errors
            ? JSON.stringify(error?.response?.data?.errors)
            : error
        )
      );
    });
};

export const deleteCustomerAddressShopify = async (req, res, next) => {
  const customerId = req.body.shopifyCustomerId;
  const addressId = req.body.shopifyAddressId;
  if (!customerId || !addressId) {
    return next(
      new AppError(
        "Missing any of the following field [shopifyCustomerId, shopifyAddressId]",
        400
      )
    );
  }
  const config = {
    method: "delete",
    url: `${process.env.SHOP_URL}/admin/api/2024-01/customers/${customerId}/addresses/${addressId}.json`,
    headers: {
      "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
    },
  };
  axios(config)
    .then(function (response) {
      next();
    })
    .catch(function (error) {
      console.log(error);
      return next(
        new AppError(
          error?.response?.data?.errors
            ? JSON.stringify(error?.response?.data?.errors)
            : error
        )
      );
    });
};

export const checkIfDistributorHasOrderOrNot = catchAsync(
  async (req, res, next) => {
    const distributorObjectId = new ObjectId(req.params.id);
    const distributorOrderExist = await Order.find({
      distributor: distributorObjectId,
    });
    const distributorId = await Distributor.findOne({
      _id: distributorObjectId,
    });

    if (distributorOrderExist.length <= 0) {
      const myHeaders = new Headers();
      myHeaders.append(
        "X-Shopify-Access-Token",
        `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`
      );
      myHeaders.append("Content-Type", "application/json");

      const graphql = JSON.stringify({
        query: `mutation MyMutation {\r\n    customerDelete(input: {id: \"gid://shopify/Customer/${distributorId.shopifyCustomerId}\"}) {\r\n        deletedCustomerId\r\n    }\r\n}`,
        variables: {},
      });
      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: graphql,
        redirect: "follow",
      };

      const responsePromise = await fetch(
        `${process.env.SHOP_URL}/admin/api/2024-01/graphql.json`,
        requestOptions
      );
      const response = await responsePromise.json();
    }

    next();
  }
);

export const updateDistributorInShopify = catchAsync(async (req, res, next) => {
  const distributorData = req.body;
  const customer = await Distributor.findById(distributorData._id);
  const data = JSON.stringify({
    query: `mutation customerUpdate($input: CustomerInput!) {
      customerUpdate(input: $input) {
        userErrors {
          field
          message
        }
        customer {
          id
          firstName
          lastName
        }
      }
    }`,
    variables: {
      input: {
        id: `gid://shopify/Customer/${distributorData.shopifyCustomerId}`,
        firstName: distributorData.name,
        lastName: "",
        phone: distributorData.phone,
        note: distributorData.note,
      },
    },
  });

  const config = {
    method: "post",
    url: `${process.env.SHOP_URL}/admin/api/graphql.json`,
    headers: {
      "X-Shopify-Access-token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      "Content-Type": "application/json",
    },
    data: data,
  };

  const response = await axios(config);

  // Update salesperson metafields if salespersonInfo is provided
  if (distributorData.salespersonInfo) {
    try {
      const { name, email } = distributorData.salespersonInfo;
      await updateSalespersonMetafields(distributorData.shopifyCustomerId, {
        name,
        email,
      });
    } catch (error) {
      console.error("Error updating customer salesperson metafields:", error);
      // Continue with the process even if metafield update fails
    }
  }
  // If salespersonInfo is not provided but salespersonId exists, try to fetch from DB
  else if (customer.salespersonId) {
    try {
      const salesperson = await Department.findById(customer.salespersonId);
      if (salesperson) {
        await updateSalespersonMetafields(distributorData.shopifyCustomerId, {
          name: salesperson.name,
          email: salesperson.email,
        });
      }
    } catch (error) {
      console.error("Error fetching salesperson info from database:", error);
      // Continue with the process even if metafield update fails
    }
  }

  const companyLocationResponse = await fetchGraphqlDataShopify(
    COMPANY_LOCATION_QUERY,
    {
      customerId: `gid://shopify/Customer/${customer.shopifyCustomerId}`,
    }
  );
  const companyData =
    companyLocationResponse?.data?.customer?.companyContactProfiles?.[0]
      ?.company;

  if (!companyData) {
    return res.status(404).json({ error: "Company data not found in Shopify" });
  }

  const locationId = companyData.locations?.edges?.[0]?.node?.id;
  if (!locationId) {
    return res
      .status(404)
      .json({ error: "Location ID not found in Shopify company data" });
  }

  const addressInput = {
    address1: distributorData.companyDetails.businessAddress,
    city: distributorData.companyDetails.city,
    zip: distributorData.companyDetails.pincode,
    countryCode: "SG",
    zoneCode: distributorData.companyDetails.state,
  };
  const addressTypes = ["SHIPPING"];
  const variables = {
    address: addressInput,
    addressTypes,
    locationId,
  };

  const companyLocationAddressUpdate = await fetchGraphqlDataShopify(
    LOCATION_ADDRESS_MUTATION,
    variables
  );

  next();
});

export const changeCustomerStatus = catchAsync(async (req, res, next) => {
  const {
    customerId,
    loggedInUser,
    comment,
    salespersonId,
    customerNumber,
    email,
    name,
  } = req.body;
  const user = req.user;
  let metafieldValue;
  try {
    const requiredFields = [
      { field: "customerId", value: customerId },
      { field: "loggedInUser", value: loggedInUser },
      { field: "comment", value: comment },
    ];

    const missingFields = requiredFields.filter((item) => !item.value);
    if (missingFields.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        error: "error",
        errors: missingFields.map((item) => ({
          status: "BAD_REQUEST",
          message: `${item.field} is required`,
        })),
      });
    }

    // Fetch the customer from the database
    const customer = await Distributor.findById(customerId);
    const departmentType = user.departmentType;

    // Use salespersonId from request body, or fallback to customer's salespersonId
    const finalSalespersonId = salespersonId || customer?.salespersonId;

    let salesPerson = null;
    if (finalSalespersonId) {
      salesPerson = await Department.findById(finalSalespersonId);
    }

    const isFinanceExecutive = departmentType.some((dept) => {
      return dept.pseudoId === "FINANCE_EXECUTIVE";
    });
    const isSunriseAdmin = departmentType.some((dept) => {
      return dept.pseudoId === "SUNRISE_ADMIN";
    });
    const isFinanceManager = departmentType.some((dept) => {
      return dept.pseudoId === "FINANCE_MANAGER";
    });

    if (customer.status === "Approved: Level 1" && isFinanceExecutive) {
      return res.status(401).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "UNAUTHORIZED",
            message: "You are not allowed to create customer in SAGE.",
          },
        ],
      });
    }

    // Handle customer status updates
    switch (customer.status) {
      case "Rejected":
        customer.status = "New Customer";
        metafieldValue = "true";
        break;
      case "Deactivated":
        customer.status = "Customer Created in Sage";
        break;

      case "New Customer":
        if (!salespersonId) {
          return res.status(400).json({
            responseCode: 1,
            status: "error",
            errors: [
              {
                code: "BAD_REQUEST",
                message: "Salesperson ID are required to proceed",
              },
            ],
          });
        }
        if (!customerNumber) {
          return res.status(400).json({
            responseCode: 1,
            status: "error",
            errors: [
              {
                code: "BAD_REQUEST",
                message: "Customer number missing",
              },
            ],
          });
        }
        customer.customerNumber = customerNumber;

        if ((isSunriseAdmin || isFinanceExecutive) && salespersonId) {
          customer.status = "Approved: Level 1";
          customer.salespersonId = salespersonId;

          if (!salesPerson) {
            console.log("salesperson not found");
            return;
          }

          const recipients = [
            {
              name: customer.name,
              email: customer.email,
              cc: [],
            },
          ];

          await EmailNotification.create({
            emailCategory: "ORDER",
            emailType: "CUSTOMER_APPROVAL_1",
            reciepient: recipients,
            emailPayload: {
              customerName: customer.name,
              customerEmail: customer.email,
              salesPersonEmail: salesPerson?.email,
              storeName: "Sunrise Trade",
              salesPersonName: salesPerson?.name,
            },
          });

          // Update customer email and name in Shopify if provided
          if (email || name) {
            try {
              await updateSalespersonMetafields(customer.shopifyCustomerId, {
                name,
                email,
              });
            } catch (error) {
              console.error("Error updating customer metafields:", error);
              // Continue with the process even if metafield update fails
            }
          }
        } else {
          return res.status(401).json({
            responseCode: 1,
            status: "error",
            errors: [
              {
                code: "UNAUTHORIZED",
                message: "You are not allowed new customer to approve Level 1",
              },
            ],
          });
        }
        break;
      case "Approved: Level 1":
        // if (!customerNumber) {
        //   return res.status(400).json({
        //     responseCode: 1,
        //     status: "error",
        //     errors: [
        //       {
        //         code: "BAD_REQUEST",
        //         message: "Customer number missing",
        //       },
        //     ],
        //   });
        // }
        // customer.customerNumber = customerNumber;

        if (isSunriseAdmin || isFinanceManager) {
          // Start MongoDB session for atomic transaction
          const session = await mongoose.startSession();

          // Transaction state tracking for rollback
          const transactionState = {
            dbUpdated: false,
            roleAssigned: false,
            addressUpdated: false,
            erpSynced: false,
            metafieldUpdated: false,
            emailSent: false,
            assignedRoleAssignmentIds: [],
            assignedCompanyLocationId: null,
            originalCustomerStatus: customer.status,
          };

          try {
            await session.withTransaction(async () => {
              // Step 1: Fetch company location from Shopify
              let companyLocationResponse;
              try {
                companyLocationResponse = await fetchGraphqlDataShopify(
                  COMPANY_LOCATION_QUERY,
                  {
                    customerId: `gid://shopify/Customer/${customer.shopifyCustomerId}`,
                  }
                );
              } catch (error) {
                throw new NewAppError(
                  "SHOPIFY_ERROR",
                  `Failed to fetch company location from Shopify: ${error.message}`,
                  400
                );
              }

              const companyData =
                companyLocationResponse.data.customer.companyContactProfiles[0]
                  .company;
              let locationId;
              let companyId;
              let orderingOnlyRoleId;
              let companyContactId;

              const addressInput = {
                address1: customer.companyDetails.businessAddress,
                zip: customer.companyDetails.pincode,
                city: customer.companyDetails.city,
                countryCode: customer.companyDetails.country,
                zoneCode: customer.companyDetails.state,
              };
              const addressTypes = ["SHIPPING"];

              companyId = companyData.id;
              locationId = companyData.locations.edges[0].node.id;
              transactionState.assignedCompanyLocationId = locationId;

              const variables = {
                address: addressInput,
                addressTypes,
                locationId,
              };

              const paymentTermsVariables = {
                id: `gid://shopify/Customer/${customer.shopifyCustomerId}`,
                paymentTerms: {
                  dueInDays: 60,
                  paymentTermsType: "NET",
                },
              };

              // Step 2: Fetch contact roles for the company
              if (companyId && locationId) {
                try {
                  const contactRolesResponse = await fetchGraphqlDataShopify(
                    CONTACT_ROLES_QUERY,
                    { companyId }
                  );
                  const contactRoles =
                    contactRolesResponse.data.company.contactRoles.edges;
                  const orderingOnlyRole = contactRoles.find(
                    (role) => role.node.name === "Ordering only"
                  );
                  orderingOnlyRoleId = orderingOnlyRole.node.id;
                } catch (error) {
                  throw new NewAppError(
                    "SHOPIFY_ERROR",
                    400,
                    `Failed to fetch contact roles from Shopify: ${error.message}`
                  );
                }
              } else {
                throw new NewAppError(
                  "SHOPIFY_ERROR",
                  400,
                  "Failed to fetch company and location id"
                );
              }

              // Step 3: Fetch contacts if "Ordering only" role is successfully retrieved
              if (orderingOnlyRoleId) {
                try {
                  const contactsResponse = await fetchGraphqlDataShopify(
                    CONTACTS_QUERY,
                    { companyId }
                  );
                  const mainContact =
                    contactsResponse.data.company.contacts.edges.find(
                      (contact) => contact.node.isMainContact
                    );
                  companyContactId = mainContact.node.id;
                } catch (error) {
                  throw new NewAppError(
                    "SHOPIFY_ERROR",
                    400,
                    `Failed to fetch contacts from Shopify: ${error.message}`
                  );
                }
              } else {
                throw new NewAppError(
                  "SHOPIFY_ERROR",
                  "Failed to fetch contacts - ordering only role not found",
                  400
                );
              }

              // Step 4: Assign role to company location
              if (locationId && orderingOnlyRoleId && companyContactId) {
                try {
                  const assignRoleResponse = await fetchGraphqlDataShopify(
                    ASSIGN_ROLE_MUTATION,
                    {
                      locationId,
                      orderingOnlyRoleId,
                      companyContactId,
                    }
                  );

                  if (
                    assignRoleResponse.data.companyLocationAssignRoles
                      .userErrors.length
                  ) {
                    throw new NewAppError(
                      "SHOPIFY_ERROR",
                      "Failed to assign role to company location - Shopify user errors",
                      400
                    );
                  }

                  // Store assigned role assignment IDs for potential rollback
                  // Note: We need the roleAssignment IDs, not the role IDs for revocation
                  transactionState.assignedRoleAssignmentIds =
                    assignRoleResponse.data.companyLocationAssignRoles.roleAssignments.map(
                      (assignment) => assignment.id
                    );
                  transactionState.roleAssigned = true;
                  transactionState.assignedCompanyLocationId = locationId;
                } catch (revokeError) {
                  // If role assignment fails and we have already assigned roles, revoke them
                  if (
                    transactionState.roleAssigned &&
                    transactionState.assignedRoleAssignmentIds.length > 0 &&
                    transactionState.assignedCompanyLocationId
                  ) {
                    try {
                      console.log(
                        "Attempting to revoke assigned roles due to error..."
                      );
                      const revokeResponse = await fetchGraphqlDataShopify(
                        COMPANY_REVOKE_ROLE,
                        {
                          companyLocationId:
                            transactionState.assignedCompanyLocationId,
                          rolesToRevoke:
                            transactionState.assignedRoleAssignmentIds,
                        }
                      );

                      if (
                        revokeResponse.data.companyLocationRevokeRoles
                          .userErrors?.length > 0
                      ) {
                        console.error(
                          "Role revocation had user errors:",
                          revokeResponse.data.companyLocationRevokeRoles
                            .userErrors
                        );
                      } else {
                        console.log("Successfully revoked assigned roles");
                        transactionState.roleAssigned = false;
                        transactionState.assignedRoleAssignmentIds = [];
                      }
                    } catch (revokeError) {
                      console.error(
                        "Failed to revoke assigned roles:",
                        revokeError.message
                      );
                    }
                  }

                  throw new NewAppError(
                    "SHOPIFY_ERROR",
                    `Failed to assign role to company location: ${revokeError?.message}`,
                    400
                  );
                }
              } else {
                throw new NewAppError(
                  "SHOPIFY_ERROR",
                  "Missing required data for role assignment",
                  400
                );
              }

              // Step 5: Update company location address
              try {
                const companyLocationAddressUpdate =
                  await fetchGraphqlDataShopify(
                    LOCATION_ADDRESS_MUTATION,
                    variables
                  );
                transactionState.addressUpdated = true;
              } catch (error) {
                throw new NewAppError(
                  "SHOPIFY_ERROR",
                  `Failed to update company location address: ${error.message}`,
                  400
                );
              }

              // Step 6: ERP SYNC (Create customer in Sage)
              try {
                await createCustomersController(customer.shopifyCustomerId);

                transactionState.erpSynced = true;
              } catch (error) {
                // Handle specific error cases for better frontend messaging
                const errorMessage = error.message || "";
                const errorCode = error.code || "";

                // Check for customer already exists patterns
                if (
                  errorCode === "CUSTOMER_EXISTS" ||
                  errorCode === "DUPLICATE_CUSTOMER" ||
                  errorMessage
                    .toLowerCase()
                    .includes("customerid already exists") ||
                  errorMessage.toLowerCase().includes("duplicate customer") ||
                  errorMessage
                    .toLowerCase()
                    .includes("customer number already exists")
                ) {
                  throw new NewAppError(
                    "CUSTOMER_EXISTS",
                    "Customer already exists in ERP system. Please use a different customer number.",
                    400
                  );
                }

                // Handle other ERP errors
                throw new NewAppError(
                  errorCode || "ERP_ERROR",
                  errorMessage || "Failed to sync customer with ERP system",
                  error.statusCode || 400
                );
              }

              // Step 7: Update database status (within transaction)
              try {
                await Distributor.findByIdAndUpdate(
                  customer._id,
                  {
                    $set: {
                      status: "Customer Created in Sage",
                      customerNumber: customer.customerNumber,
                    },
                  },
                  { session, new: true }
                );
                transactionState.dbUpdated = true;
              } catch (error) {
                throw new NewAppError(
                  "DATABASE_ERROR",
                  `Failed to update customer status in database: ${error.message}`,
                  400
                );
              }

              // Step 8: Meta update (verified metafield)
              try {
                const metafieldResponse = await fetchGraphqlDataShopify(
                  VERIFY_META_TRUE_MUTATION,
                  {
                    metafields: [
                      {
                        key: "verified",
                        namespace: "custom",
                        ownerId: `gid://shopify/Customer/${customer.shopifyCustomerId}`,
                        type: "boolean",
                        value: "true",
                      },
                    ],
                  }
                );
                transactionState.metafieldUpdated = true;
              } catch (error) {
                throw new NewAppError(
                  "DATABASE_ERROR",
                  `Failed to update verified metafield: ${error.message}`,
                  400
                );
              }

              // Step 9: Send Level 2 Approval Email (non-critical, but track for completeness)
              try {
                const recipient = [
                  {
                    name: customer.name,
                    email: customer.email,
                    cc: [],
                  },
                ];

                // Alternative approach: Create document instance and save with session
                const emailNotification = new EmailNotification({
                  emailCategory: "ORDER",
                  emailType: "CUSTOMER_APPROVAL_2",
                  reciepient: recipient,
                  emailPayload: {
                    customerName: customer.name,
                    customerEmail: customer.email,
                    salesPersonEmail: salesPerson?.email,
                    salesPersonName: salesPerson?.name,
                    storeName: "Sunrise Trade",
                    portalUrl: "",
                  },
                });

                await emailNotification.save({ session });
                transactionState.emailSent = true;
              } catch (error) {
                // Email failure shouldn't fail the entire transaction, just log it
                console.error(
                  "Failed to send Level 2 approval email:",
                  error.message
                );
              }

              // Update customer status for response
              customer.status = "Customer Created in Sage";
            });

            // If we reach here, all operations succeeded
            console.log(
              "All Level 2 approval operations completed successfully"
            );
          } catch (transactionError) {
            // Transaction failed - MongoDB will automatically rollback database changes
            console.error("Transaction failed:", transactionError);

            // Perform manual rollback for external systems (Shopify, ERP)
            await performRollback(transactionState);

            // Determine error type and message
            let errorCode = "SERVER_ERROR";
            let errorMessage = "Customer approval process failed";

            // Handle different error types (NewAppError vs regular Error)
            let errorMsg = "";

            if (transactionError && typeof transactionError === "object") {
              if (transactionError.message) {
                errorMsg = String(transactionError.message);
              } else if (transactionError.toString) {
                errorMsg = transactionError.toString();
              } else {
                errorMsg = JSON.stringify(transactionError);
              }
            } else {
              errorMsg = String(transactionError);
            }

            if (errorMsg.includes("Shopify") || errorMsg.includes("SHOPIFY")) {
              errorCode = "SHOPIFY_ERROR";
              errorMessage = `Shopify operation failed: ${errorMsg}`;
            } else if (errorMsg.includes("ERP")) {
              errorCode = "ERP_ERROR";
              errorMessage = `ERP sync failed: ${errorMsg}`;
            } else if (
              errorMsg.includes("database") ||
              errorMsg.includes("DATABASE")
            ) {
              errorCode = "DATABASE_ERROR";
              errorMessage = `Database operation failed: ${errorMsg}`;
            } else {
              errorMessage = `Customer approval process failed: ${errorMsg}`;
            }

            return res.status(500).json({
              responseCode: 1,
              status: "error",
              errors: [
                {
                  code: errorCode,
                  message: errorMessage,
                },
              ],
            });
          } finally {
            await session.endSession();
          }
        } else {
          return res.status(401).json({
            responseCode: 1,
            status: "error",
            errors: [
              {
                code: "UNAUTHORIZED",
                message: "Only a Finance Executive can approve beyond Level 1",
              },
            ],
          });
        }

        break;

      default:
        return res.status(200).json({
          responseCode: 0,
          status: "success",
          data: {
            message: "No status update required",
          },
        });

        const shopifyCustomerId = customer.shopifyCustomerId;
        const variables = {
          metafields: [
            {
              key: "verified",
              namespace: "custom",
              ownerId: `gid://shopify/Customer/${shopifyCustomerId}`,
              type: "boolean",
              value: metafieldValue,
            },
          ],
        };
        const metaMutationResponse = await fetchGraphqlDataShopify(
          VERIFY_META_TRUE_MUTATION,
          variables
        );
    }

    customer.timeline.push({
      comment,
      date: new Date(),
    });
    await customer.save();
    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        message: `Customer updated to ${customer.status}`,
      },
    });
  } catch (error) {
    console.error("Error in role assignment or metafield update:", error);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: "Failed to assign role or update Shopify metafield",
        },
      ],
    });
  }
});

export const updateCustomerFiles = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const files = req.files || {};

  // Parse details from the request body
  const details = JSON.parse(req.body.details || "{}");

  const existingRecord = await GetDistributorRecord(ConvertFromGID(id));
  const existingCompanyDetails = existingRecord?.companyDetails || {};

  // Upload files to S3
  const fileUploadPromises = Object.keys(files).map(async (key) => {
    const file = files[key][0]; // Get the first file for each key
    const filePath = path.join(
      __dirname,
      "..",
      "asset",
      "upload",
      "company",
      file.filename
    );
    try {
      // Upload to S3
      const uploadResult = await uploadToS3(
        filePath,
        process.env.AWS_S3_BUCKET,
        file.filename
      );
      const fileUrl = uploadResult.Location; // Get the S3 URL

      // Delete the local file after upload
      fs.unlinkSync(filePath);

      return {
        fileName: key, // Use the field name (form9, form24, etc.)
        fileUrl,
      };
    } catch (err) {
      console.error("S3 Upload Error:", err);
      throw new NewAppError(
        "S3_UPLOAD_ERROR",
        `File upload failed for ${file.originalname}`,
        400
      );
    }
  });

  // Wait for all files to be uploaded
  const uploadedFiles = await Promise.all(fileUploadPromises);

  // Create an object to store file URLs
  const fileUrls = uploadedFiles.reduce((acc, file) => {
    acc[file.fileName] = file.fileUrl;
    return acc;
  }, {});

  // Prepare the update object
  const toUpdateObj = {
    currentStep: 3,
    companyDetails: {
      ...existingCompanyDetails,
      entityType: details.entityType, // Entity type from request
      documents: {
        ...existingCompanyDetails.documents, // Existing documents
        ...fileUrls, // Newly uploaded files
      },
    },
  };

  // Update the database
  const dbUpdateResult = await UpdateDistributorRecord(
    ConvertFromGID(id),
    toUpdateObj
  );

  if (!dbUpdateResult) {
    return res.status(500).json({
      success: false,
      errors: [
        {
          code: "DB_UPDATE_FAILURE",
          message: `Step 3 Update Failed in DB`,
        },
      ],
    });
  }

  // Send success response
  return res.status(200).json({
    success: true,
    message: "Files uploaded and details updated successfully",
    updatedDetails: toUpdateObj,
  });
});

export const banCustomer = catchAsync(async (req, res, next) => {
  const { customerId, loggedInUser, comment } = req.body;
  const user = req.user;
  try {
    if (!customerId) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            error: "BAD_REQUEST",
            message: "Customer not found",
          },
        ],
      });
    }
    // Fetch the customer from the database
    const customer = await Distributor.findById(customerId);
    const departmentType = user.departmentType;

    const isFinanceExecutive = departmentType.some((dept) => {
      return dept.pseudoId === "FINANCE_EXECUTIVE";
    });
    const isSunriseAdmin = departmentType.some((dept) => {
      return dept.pseudoId === "SUNRISE_ADMIN";
    });
    const isFinanceManager = departmentType.some((dept) => {
      return dept.pseudoId === "FINANCE_MANAGER";
    });

    // Fetch the department of the logged-in user (hardcoded for now)
    // const department = await Department.findById("66e7bd9761f1f80dcaefcd3c");
    // if (!department) {
    //   return res.status(400).json({
    //     responseCode: 1,
    //     status: "error",
    //     errors: [
    //       {
    //         error: "BAD_REQUEST",
    //         message: "Department not found",
    //       },
    //     ],
    //   });
    // }

    // const departmentTypes = department.departmentType;
    // console.log("Department Types: ", departmentTypes, department.name);

    // const financeDepartmentId = new mongoose.Types.ObjectId(
    //   "6637f8fd622d0e63dc195b7e"
    // ); // Hardcoded "FINANCE" department ID

    // const isFinanceDepartment = departmentTypes.some((deptType) =>
    //   deptType.equals(financeDepartmentId)
    // );

    if (isFinanceManager || isFinanceExecutive || isSunriseAdmin) {
      let metafieldValue;

      switch (customer.status) {
        case "New Customer":
          // case "Approved: Level 1":
          // case "Pending Salesperson Allocation":
          customer.status = "Rejected";
          metafieldValue = "false"; // Set metafield to false for "Rejected"
          break;
        default:
          return res.status(401).json({
            responseCode: 1,
            status: "error",
            errors: [
              {
                code: "UNAUTHORIZED",
                message: "No valid status change applicable for this customer",
              },
            ],
          });
      }

      const shopifyCustomerId = customer.shopifyCustomerId;
      const variables = {
        metafields: [
          {
            key: "verified",
            namespace: "custom",
            ownerId: `gid://shopify/Customer/${shopifyCustomerId}`,
            type: "boolean",
            value: metafieldValue,
          },
        ],
      };

      const metaMutationResponse = await fetchGraphqlDataShopify(
        VERIFY_META_FALSE_MUTATION,
        variables
      );
    }

    // Save the updated customer status and timeline entry
    customer.timeline.push({
      comment,
      date: new Date(),
    });
    await customer.save();

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        message: `Customer updated to ${customer.status} `,
      },
    });
  } catch (error) {
    console.error("Error updating Shopify metafield: ", error);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: "Failed to update Shopify metafield",
        },
      ],
    });
  }
});

export const getDistributorStatusCount = catchAsync(async (req, res) => {
  try {
    const user = req.user;
    const salesPersonDept = req.salesPersonDept;
    const statusCounts = await Distributor.aggregate([
      // Add $match stage only if user && salesPersonDept are true
      ...(user && salesPersonDept
        ? [
            {
              $match: { salespersonId: user._id.toString() },
            },
          ]
        : []), // If false, no $match is added, and the aggregation remains unchanged
      {
        $group: {
          _id: { $toString: "$status" },
          count: { $sum: 1 },
        },
      },
    ]);

    const formattedCounts = {};
    statusCounts.forEach((item) => {
      formattedCounts[item._id] = item.count;
    });

    res.status(200).json({
      responseCode: 0,
      status: "success",
      data: { formattedCounts },
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        { code: "SERVER_ERROR", message: "Error to fetch customer count" },
      ],
    });
  }
});

// export const updateCustomerNumber = catchAsync(async (req, res, next) => {
//   const { customerNumber, shopifyCustomerId } = req.body;

//   try {
//     const key = "customernumber";
//     const namespace = "custom";
//     const ownerId = `gid://shopify/Customer/${shopifyCustomerId}`;
//     const type = "single_line_text_field";
//     const value = customerNumber;

//     const data = JSON.stringify({
//       query: `mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
//         metafieldsSet(metafields: $metafields) {
//           metafields {
//             key
//             namespace
//             value
//             createdAt
//             updatedAt
//           }
//           userErrors {
//             field
//             message
//             code
//           }
//         }
//       }`,
//       variables: {
//         metafields: [
//           {
//             key: key,
//             namespace: namespace,
//             ownerId: ownerId,
//             type: type,
//             value: value,
//           },
//         ],
//       },
//     });

//     const shopifyResponse = await axios({
//       method: "post",
//       url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/graphql.json`,
//       headers: {
//         "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
//         "Content-Type": "application/json",
//       },
//       data: data,
//     });

//     console.log("Shopify metafield update response: ", shopifyResponse.data);
//     await Distributor.findOneAndUpdate(
//       { shopifyCustomerId },
//       { customerNumber: customerNumber },
//       { new: true, upsert: true }
//     );
//     return res.status(200).json({
//       message: "Customer Number updated successfully",
//       data: shopifyResponse.data,
//     });
//   } catch (error) {
//     console.error("Error updating Shopify metafield: ", error);
//     return res
//       .status(500)
//       .json({ message: "Failed to update Shopify metafield" });
//   }
// });

export const checkCompany = catchAsync(async (req, res, next) => {
  const { customerId } = req.body;

  try {
    if (!customerId) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [{ status: "BAD_REQUEST", message: "CustomerId not found" }],
      });
    }
    const customer = mongoose.Types.ObjectId.isValid(customerId)
      ? await Distributor.findById(customerId)
      : await Distributor.findOne({ shopifyCustomerId: customerId });
    const variables = {
      customerId: `gid://shopify/Customer/${customer.shopifyCustomerId}`,
    };

    const companyLocationResponse = await fetchGraphqlDataShopify(
      CHECK_COMPANY_QUERY,
      variables
    );
    const companyData =
      companyLocationResponse.data.customer.companyContactProfiles;

    if (!companyData || companyData.length === 0) {
      // No company exists
      return res.status(200).json({
        responseCode: 0,
        status: "success",
        data: {
          companyExists: false,
        },
      });
    }

    // If company data exists
    const companyId = companyData[0].company.id;
    const locationId = companyData[0].company.locations.edges[0].node.id;

    return res.status(200).json({
      responseCode: 1,
      status: "success",
      data: {
        companyExists: true,
      },
    });
  } catch (error) {
    console.error("Error fetching company data:", error);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: "An error occurred while checking the company.",
        },
      ],
    });
  }
});

export const downloadDistributorSheet = catchAsync(async (req, res, next) => {
  const excelBuffer = await downloadDistributorSheetService();

  // Set headers for file download
  res.setHeader(
    "Content-Type",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  );
  res.setHeader(
    "Content-Disposition",
    "attachment; filename=distributors.xlsx"
  );

  // Send the buffer
  res.send(excelBuffer);
});

export const uploadDistributorSheet = catchAsync(async (req, res, next) => {
  if (!req.files || !req.files.sheet) {
    return next(new AppError("No file uploaded", 400));
  }

  const fileBuffer = req.files.sheet[0].buffer;
  const updatedDistributors = await uploadDistributorSheetService(fileBuffer);
  // Update Shopify metafields for each successfully updated distributor
  if (updatedDistributors.successful && updatedDistributors.successful.items) {
    // Process each successful update
    const metafieldUpdatePromises = updatedDistributors.successful.items.map(
      async (item) => {
        try {
          // Find the distributor in the database
          const distributor = await Distributor.findOne({
            name: item.customerName,
            customerNumber: item.customerNumber,
          });

          if (!distributor || !distributor.shopifyCustomerId) {
            console.error(
              `Could not find distributor with name ${item.distributorName} and customer number ${item.customerNumber}`
            );
            return null;
          }

          // Find the salesperson details
          const salesperson = await Department.findOne({
            salespersonkey: item.newSalespersonKey,
          });

          if (!salesperson) {
            console.error(
              `Could not find salesperson with key ${item.newSalespersonKey}`
            );
            return null;
          }

          // Prepare metafields for update
          const metafields = [];

          if (salesperson.email) {
            metafields.push({
              key: "sales_person_email",
              namespace: "custom",
              ownerId: `gid://shopify/Customer/${distributor.shopifyCustomerId}`,
              type: "single_line_text_field",
              value: salesperson.email,
            });
          }

          if (salesperson.name) {
            metafields.push({
              key: "sales_person_name",
              namespace: "custom",
              ownerId: `gid://shopify/Customer/${distributor.shopifyCustomerId}`,
              type: "single_line_text_field",
              value: salesperson.name,
            });
          }

          if (metafields.length > 0) {
            // Update metafields in Shopify
            const success = await updateSalespersonMetafields(
              distributor.shopifyCustomerId,
              {
                name: salesperson.name,
                email: salesperson.email,
              }
            );

            return {
              distributorName: distributor.name,
              customerNumber: distributor.customerNumber,
              shopifyCustomerId: distributor.shopifyCustomerId,
              salespersonName: salesperson.name,
              salespersonEmail: salesperson.email,
              metafieldUpdateSuccess: success,
            };
          }

          return null;
        } catch (error) {
          console.error(
            `Error updating metafields for distributor ${item.distributorName}:`,
            error
          );
          return {
            distributorName: item.distributorName,
            customerNumber: item.customerNumber,
            metafieldUpdateSuccess: false,
            error: error.message,
          };
        }
      }
    );

    // Wait for all metafield updates to complete
    const metafieldUpdateResults = await Promise.all(metafieldUpdatePromises);
    const successfulMetafieldUpdates = metafieldUpdateResults.filter(
      (result) => result && result.metafieldUpdateSuccess
    );

    // Add metafield update results to the response
    updatedDistributors.metafieldUpdates = {
      count: successfulMetafieldUpdates.length,
      items: successfulMetafieldUpdates,
    };
  }

  res.status(200).json({
    status: "success",
    message: "Distributor sheet processed successfully",
    data: updatedDistributors,
  });
});
