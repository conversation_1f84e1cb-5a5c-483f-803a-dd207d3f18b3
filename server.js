import * as dotenv from "dotenv";
dotenv.config();
import mongoose from "mongoose";
import { Email } from "./service/rule/index.js";
import path from "path";
import SftpClient from "ssh2-sftp-client";

// process.on('uncaughtException', error => {
//   console.log('UncaughtException Shuting down ...');
//   console.log(error);
//   process.exit(1);
// });

import app from "./app.js";
import Order from "./model/order.model.js";
import Shipment from "./model/shipment.model.js";
import { syncAppInventoryManually } from "./utils/scripts/index.js";
import inventorySyncShopify from "./service/shopify/inventorySyncShopify.service.js";

const PORT = process.env.PORT || 3600;

const __filename = new URL(import.meta.url).pathname;
const __dirname = path.dirname(__filename);

const sftpConfig = {
  host: "************",
  username: "DDrive",
  port: "22",
  password: "JkI404$ha83#08",
  remoteDir: "/home/<USER>/DEV/Indent/Process",
};

export const getFileDetailsFromSftp = async () => {
  const sftp = new SftpClient();
  try {
    await sftp.connect(sftpConfig);
    console.log("connected");
    console.log(
      await sftp.list("/home/<USER>/DEV/Indent/Process"),
      "success list"
    );
    return await sftp.list("/home/<USER>/DEV/Indent/Process");
  } catch (error) {
    await sftp.end();
    console.log(error, "catch");
    return error;
  } finally {
    await sftp.end();
  }
};

mongoose
  .connect(`${process.env.MONGO_DB_URL}`)
  .then(async (con) => {
    //await getFileDetailsFromSftp()

    console.log("DB connection Successful");
    console.log("Github Action Deployed");  
    console.log(process.env.MONGO_DB_URL, "MONGO_DB_URL");
    // console.log(await Inventory.deleteMany({}))
    // await syncAppInventoryManually()
    // await Shipment.deleteMany({})
    //const filePath = path.join(__dirname, 'asset/upload', '6645f189bc12e2886447add2_1715859946456.xlsx');
    // await uploadToS3(filePath, 'titan-d2d')
    // generateEscalationNotification()

    // inventorySyncShopify();
  })
  .catch((error) => {
    console.log(error.name, error.message);
  });

const server = app.listen(PORT, () => {
  console.log(`Server started on ${PORT}`);
});

// process.on('unhandledRejection', error => {
//   console.log('UnhandledRejection Shuting down ...');
//   console.log(error);
//   server.close(() => {
//     process.exit(1);
//   });
// });

// db connection
