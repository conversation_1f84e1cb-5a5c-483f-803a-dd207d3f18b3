import express from "express";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import {
  getShipment,
  createShipment,
  getOneShipment,
  updateShipment,
  deleteShipment,
  updateShipmentStatus,
  returnShipmentDataWithActions,
  downloadShipmentExcel,
  editShipment,
  allocateShipmentManually,
  editShipmentWithSla,
  generateShipmentPISheet,
  allocateInventoryManually,
  autoAllocateShipmentController,
  autoAllocateBulkShipmentsController,
  cancelPendingShipmentController,
  getDeptWiseShipments,
} from "../controller/shipment.controller.js";
import { notifyStatusChange } from "../controller/common/index.js";
import { generateRandomNumber } from "../utils/helperFunction.js";
import {
  authenticateDepttPeopleAccess,
  authenticateDepttPeopleAccessModuleWise,
} from "../middlewares/authenticateDepttPeople.js";
import { alignmentPendingOrderProcess } from "../controller/action.controller.js";
// import { downloadShipmentExcel, uploadShipmentExcel } from '../../controller/shipment/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "shipment"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".")[1];
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Retains the original file name
  },
});

const upload = multer({ storage });

const router = express.Router();
const setStatusUpdateType = async (req, res, next) => {
  req.body.middlewareStatusCategory = "Shipment";
  next();
};
router
  .route("/")
  .get(
    authenticateDepttPeopleAccessModuleWise("shipment", "list", "read"),
    getDeptWiseShipments
  )
  .post(setStatusUpdateType, createShipment);

router.route("/generate_pi").get(generateShipmentPISheet);

router.route("/download_shipment_items").get(downloadShipmentExcel);

router
  .route("/:shipmentId/manualAllocate")
  .post(
    upload.single("file"),
    authenticateDepttPeopleAccessModuleWise("shipment", "manualAllocate"),
    allocateShipmentManually
  );

router
  .route("/:shipmentId/cancel")
  .post(
    authenticateDepttPeopleAccessModuleWise("shipment", "cancelShipment"),
    cancelPendingShipmentController
  );
router
  .route("/autoAllocate")
  .post(
    authenticateDepttPeopleAccessModuleWise("shipment", "autoAllocate"),
    autoAllocateShipmentController
  );
router
  .route("/bulkProcess")
  .post(
    authenticateDepttPeopleAccessModuleWise("shipment", "bulkProcess"),
    autoAllocateBulkShipmentsController
  );

router
  .route("/:shipmentId/pending_sla_upload")
  .post(
    upload.single("file"),
    authenticateDepttPeopleAccess(),
    editShipmentWithSla
  );

router
  .route("/:id")
  .get(returnShipmentDataWithActions)
  // .patch(setStatusUpdateType, updateShipment)
  .patch(setStatusUpdateType, notifyStatusChange, updateShipment)
  .delete(deleteShipment);

router.route("/:id/status").post(updateShipmentStatus);

router.route("/manually_inventory_allocation").post(allocateInventoryManually);

export default router;
