import { productScheduledService } from "../service/ERPSync/productScheduledService.js";

const manualProductsSyncController = async (req, res) => {
  try {
    const response = await productScheduledService(true);
    if (response?.status !== 200) {
      return res.status(500).send({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "SERVER_ERROR",
            message: "Something went wrong.",
          },
        ],
      });
    }

    res.status(200).send({
      responseCode: 0,
      status: "success",
      data: { message: response?.message },
    });
  } catch (error) {
    console.log(error?.message, "error");
    res.status(500).send({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "SERVER_ERROR",
          message: error?.message,
        },
      ],
    });
  }
};

export { manualProductsSyncController };
