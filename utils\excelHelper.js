import ExcelJ<PERSON> from "exceljs";
import <PERSON>WS from "aws-sdk";

/**
 * Upload buffer directly to S3 (for Excel files generated in memory)
 * @param {Buffer} buffer - File buffer
 * @param {string} key - S3 key/filename
 * @param {string} contentType - MIME type
 * @returns {Object} - Upload result with downloadUrl
 */
const uploadBufferToS3 = async (buffer, key, contentType) => {
  try {
    const s3 = new AWS.S3({
      credentials: {
        region: process.env.AWS_REGION,
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_ACCESS_KEY_ID_SECRET,
      },
    });

    const params = {
      Bucket: process.env.AWS_S3_BUCKET,
      Key: key,
      Body: buffer,
      ContentType: contentType,
    };

    return new Promise((resolve, reject) => {
      s3.upload(params, {}, (error, data) => {
        if (error) {
          reject(error);
          return;
        }
        resolve({
          downloadUrl: data.Location,
          key: data.Key,
          etag: data.ETag,
        });
      });
    });
  } catch (error) {
    throw new Error(`S3 upload failed: ${error.message}`);
  }
};

/**
 * Generate Excel sheet with ExcelJS and optional column coloring
 * @param {Array} data - Array of objects to convert to Excel
 * @param {string} filename - Optional filename for S3 upload
 * @param {Object} columnColors - Optional object mapping column names to colors
 * @param {string} worksheetName - Optional worksheet name (default: 'Sheet1')
 * @returns {Buffer|string} - Buffer for direct download or S3 URL for email attachments
 */
export const generateExcelSheetWithExcelJS = async (
  data,
  filename = null,
  columnColors = {},
  worksheetName = "Sheet1"
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(worksheetName);

  if (data.length === 0) {
    return null;
  }

  // Get headers from the first row
  const headers = Object.keys(data[0]);

  // Add headers to worksheet
  worksheet.addRow(headers);

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" }, // Light gray background for headers
  };

  // Add data rows
  data.forEach((rowData) => {
    const row = worksheet.addRow(Object.values(rowData));

    // Apply column colors if specified
    Object.keys(columnColors).forEach((columnName) => {
      const columnIndex = headers.findIndex((header) =>
        header.toLowerCase().includes(columnName.toLowerCase())
      );

      if (columnIndex !== -1) {
        const cell = row.getCell(columnIndex + 1);
        const colorConfig = columnColors[columnName];

        // Apply background color
        if (colorConfig.backgroundColor) {
          cell.fill = {
            type: "pattern",
            pattern: "solid",
            fgColor: { argb: colorConfig.backgroundColor },
          };
        }

        // Apply text color
        if (colorConfig.textColor) {
          cell.font = {
            ...cell.font,
            color: { argb: colorConfig.textColor },
          };
        }
      }
    });
  });

  // Auto-fit columns
  worksheet.columns.forEach((column) => {
    let maxLength = 0;
    column.eachCell({ includeEmpty: true }, (cell) => {
      const columnLength = cell.value ? cell.value.toString().length : 10;
      if (columnLength > maxLength) {
        maxLength = columnLength;
      }
    });
    column.width = Math.min(maxLength + 2, 50);
  });

  // Generate buffer
  const buffer = await workbook.xlsx.writeBuffer();

  if (filename) {
    // Upload to S3 and return download URL
    const s3Key = `excel-sheets/${filename}.xlsx`;
    const uploadResult = await uploadBufferToS3(
      buffer,
      s3Key,
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    return uploadResult.downloadUrl;
  }

  return buffer;
};

/**
 * Generate error sheet with light red highlighting for error columns
 * @param {Array} errors - Array of error objects
 * @param {string} filename - Optional filename for S3 upload
 * @returns {Buffer|string} - Buffer for direct download or S3 URL for email attachments
 */
export const generateErrorSheetWithExcelJS = async (
  errors,
  filename = null
) => {
  // Define error column colors (light red background, dark red text)
  const errorColumnColors = {
    error: {
      backgroundColor: "FFFFC7CE", // Light red background
      textColor: "FF9C0006", // Dark red text
    },
  };

  return await generateExcelSheetWithExcelJS(
    errors,
    filename,
    errorColumnColors,
    "Errors"
  );
};

/**
 * Generate error sheet and upload to S3 for email attachments
 * @param {Array} errors - Array of error objects
 * @param {string} sheetName - Sheet name for filename
 * @returns {string} - S3 download URL
 */
export const generateErrorSheetForEmail = async (
  errors,
  sheetName = "error_sheet"
) => {
  const filename = `${sheetName}_${Date.now()}`;
  return await generateErrorSheetWithExcelJS(errors, filename);
};

/**
 * Legacy function for backward compatibility
 * @param {Array} errors - Array of error objects
 * @param {string} originalFilePath - Original file path (unused, for compatibility)
 * @returns {string} - S3 download URL
 */
export const generateErrorSheet = async (errors, originalFilePath) => {
  const filename = `error_sheet`;
  return await generateErrorSheetWithExcelJS(errors, filename);
};

/**
 * Predefined color schemes for common use cases
 */
export const COLOR_SCHEMES = {
  // Light colors for highlighting
  LIGHT_YELLOW: {
    backgroundColor: "FFFFFF99", // Light yellow
    textColor: "FF000000", // Black text
  },
  LIGHT_RED: {
    backgroundColor: "FFFFC7CE", // Light red
    textColor: "FF9C0006", // Dark red text
  },
  LIGHT_GREEN: {
    backgroundColor: "FFC6EFCE", // Light green
    textColor: "FF006100", // Dark green text
  },
  LIGHT_BLUE: {
    backgroundColor: "FFB7DEE8", // Light blue
    textColor: "FF0F2080", // Dark blue text
  },
  LIGHT_ORANGE: {
    backgroundColor: "FFFDE9D9", // Light orange
    textColor: "FF974706", // Dark orange text
  },
};
