// const fs = require("fs");
// const PDFDocument = require("pdfkit");
import PDFDocument from "pdfkit";
import fs from "fs";
import path from "path";


const __filename = new URL(import.meta.url).pathname;
const __dirname = path.dirname(__filename);


function generateInvoicePdf(invoice, path) {
  //!TODO Get from Db
  console.log("Generating invoice")
  const piNumber = "PI 0051/UAE-ILG/APR24";
  const address = {
    companyName: invoice.client.name || "ILG EMEA DWC LLC",
    address1:
      `${invoice.client.address1}, ${invoice.client.address2}` ||
      "WB-27 & 28, Logistics District, Dubai South",
    pinCode: invoice.client.zip || "712777",
    customerName:
      `${invoice.client.first_name}, ${invoice.client.last_name}` ||
      "Rakes<PERSON> Jain",
    country: invoice.client.country || "United Arab Emirates",
    state: invoice.client.province || "<PERSON><PERSON><PERSON>",
    city: invoice.client.city || "Dubai",
    phone: invoice.client.phone || "+971 55 966 5458",
  };
  let doc = new PDFDocument({ size: "A4", margin: 50 });
  generateLogo(doc);
  generateHeader(doc, { piNumber, address });
  generatePaymentTermSection(doc);
  generateBankDetailSection(doc);
  generateCustomerInformation(doc, invoice);
  generateInvoiceTable(doc, invoice);
  generateFooter(doc);

  doc.end();
  doc.pipe(fs.createWriteStream(path));
}

function generateLogo(doc) {
  const logoPath = path.join(
    __dirname,
    "..",
    "asset",
    "static",
    "titan_logo.png"
  );
 console.log('logoPathhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh', logoPath);
 console.log('logoPathhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh', logoPath);
 console.log('logoPathhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh', logoPath);
  doc
    .image(
      logoPath,
      250,
      50,
      { width: 80, align: "center" }
    )
    .fillColor("#444444");
  // .moveDown()
  // generateHr(doc, 185);
}

function generateHeader(doc, payload) {
  const piNumber = payload.piNumber;
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  const date = currentDate.getDate();
  const formattedDate = `${date}.${month}.${year}`;
  const { companyName, address1, pinCode, customerName, phone, city, country } =
    payload.address;
  doc
    // .image("/home/<USER>/STC/titan/project/titan-d2d-backend/asset/static/titan_logo.png", 50, 45, { width: 50 })
    // .fillColor("#444444")
    // .fontSize(20)
    // .moveDown(4)
    .fontSize(9)
    .font("Helvetica-Bold")
    .text(`${companyName}`, 45, 145)
    .text(`PO Box ${pinCode}`, 45, 157)
    .text(`${address1}`, 45, 168)
    .text(`${city}, ${country}`, 45, 179)
    .text(`Contact Person: ${customerName}`, 45, 190)
    .text(`Mobile: ${phone}`, 45, 201)
    .fontSize(9)
    .text(`${piNumber}`, 200, 140, { align: "right" })
    .text(`DATE ${formattedDate}`, 200, 151, { align: "right" })
    .moveDown();
}

function generatePaymentTermSection(doc, payload) {
  doc
    // .image("/home/<USER>/STC/titan/project/titan-d2d-backend/asset/static/titan_logo.png", 50, 45, { width: 50 })
    // .fillColor("#444444")
    // .fontSize(20)
    // .moveDown(4)
    .fontSize(7)
    .font("Helvetica-Bold")
    .text(`Payment Terms : 100% TT in Advance.`, 45, 400)
    .text(`INCO TERMS : FOB Bangalore`, 45, 410)
    .text(`BENIFICIARY : Titan Company Limited`, 45, 420)
    .text(`Integrity#193, Veerasandra`, 80, 430)
    .text(`Electronics City P.O.,Off Hosur Main Road,`, 80, 440)
    .text(`Bangalore 560100`, 80, 450)
    .text(`INDIA`, 80, 460)
    .text(`Shipping Instruction : By Air From India to UAE`, 45, 470)
    .text(`DELIVERY : IMMEDIATE`, 45, 480)
    .text(`PARTSHIPMENT : ALLOWED`, 45, 490)
    .moveDown();
}

function generateBankDetailSection(doc, payload) {
  doc
    // .image("/home/<USER>/STC/titan/project/titan-d2d-backend/asset/static/titan_logo.png", 50, 45, { width: 50 })
    // .fillColor("#444444")
    // .fontSize(20)
    // .moveDown(4)
    .fontSize(6)
    .font("Helvetica-Bold")
    .text(`Bank Account details`, 45, 520)
    .font("Helvetica")
    .text(`Beneficiary Name : TITAN COMPANY LIMITED`, 45, 530)
    .text(`Bank Name : HDFC BANK LTD , RICHMOND RD , B'LORE - 560025`, 45, 540)
    .text(`Account No. : **************`, 45, 550)
    .text(`Swift Code : HDFCINBBBNG,`, 45, 560)
    .text(`IFSC Code : HDFC0000523`, 45, 570)
    .text(
      `Correspondent Bank: J P Morgan Chase Bank New York CHASUS33 (payment should be routed either via`,
      45,
      580
    )
    .text(`CHIPS ABA 0002 Or FED ABA *********).`, 45, 590)
    .text(`For credit to : 001-1-406717 HDFC BANK LTD., Mumbai`, 45, 600)
    .fontSize(12)
    .font("Helvetica-Bold")
    .text(`AUTHORISED SIGNATORY`, 45, 665)
    .moveDown();
}

function generateCustomerInformation(doc, invoice) {
  doc
    .fillColor("#444444")
    .fontSize(10)
    .text("PROFORMA INVOICE", 50, 220, { align: "center" });

  // generateHr(doc, 235);

  // const customerInformationTop = 250;

  // doc
  //     .fontSize(10)
  //     .text("Invoice no:", 50, customerInformationTop)
  //     .font("Helvetica-Bold")
  //     .text(invoice.invoiceNumber, 150, customerInformationTop)
  //     .font("Helvetica")
  //     .text("Invoice Date:", 50, customerInformationTop + 15)
  //     .text(formatDate(new Date()), 150, customerInformationTop + 15)
  //     .text("Balance Due:", 50, customerInformationTop + 30)
  //     .text(
  //         formatCurrency(invoice.subtotal - invoice.paid),
  //         150,
  //         customerInformationTop + 30
  //     )

  //     .font("Helvetica-Bold")
  //     .text(invoice.client.name, 300, customerInformationTop)
  //     .font("Helvetica")
  //     .text(invoice.client.address, 300, customerInformationTop + 15)
  //     .text(
  //         invoice.client.city +
  //         ", " +
  //         invoice.client.state +
  //         ", " +
  //         invoice.client.country,
  //         300,
  //         customerInformationTop + 30
  //     )
  //     .moveDown();

  // generateHr(doc, 252);
}

function generateInvoiceTable(doc, invoice) {
  let i;
  const invoiceTableTop = 250;
  const { client } = invoice;
  const { pricePerSession } = client;

  generateHr(doc, 240);

  doc.font("Helvetica-Bold");
  generateTableRow(
    doc,
    invoiceTableTop,
    "SL NO.",
    "DESCRIPTION",
    // "Unit Cost",
    "QTY/PCS",
    "AMOUNT (USD)"
  );
  generateHr(doc, invoiceTableTop + 10);
  doc.font("Helvetica");
  const clubbedLineItems = invoice.items.reduce(
    (acc, product) => {
      acc.quantity += product.quantity;
      acc.price += product.price;
      acc.amountSum += product.amountSum;
      return acc;
    },
    {
      item: 0,
      description: "Clubbed Product",
      quantity: 0,
      price: "0.00", // This can be updated based on your specific logic for price
      amountSum: 0,
    }
  );
  const clubbedLineItemsArray = [clubbedLineItems];
  // console.log("77777777777777777777777clubbedLineItemsclubbedLineItems77777777777777777", clubbedLineItems);

  for (i = 0; i < clubbedLineItemsArray.length; i++) {
    const item = clubbedLineItemsArray[i];
    const position = invoiceTableTop + (i + 1) * 20;
    const wrappedLines = wrapText(item.description, 5);
    wrappedLines.forEach((line, index) => {
      //     .text(invoice.invoiceNumber, 150, customerInformationTop)
      doc.text("", 150);
      generateTableRow(
        doc,
        position + index * 8,
        index <= 0 ? item.item + 1 : "",
        line || item.description, //*Not adding index condition to render multiline description
        // formatCurrency(pricePerSession),
        index <= 0 ? item.quantity : "",
        index <= 0 ? formatCurrency(item.amountSum) : ""
      );
    });
    generateHr(
      doc,
      invoiceTableTop + (i + 1) * 20 + 10
    );
  }

  const subtotalPosition = invoiceTableTop + (i + 1) * 20;
  generateTableRow(
    doc,
    subtotalPosition,
    "",
    "",
    "TOTAL FOB VALUE IN USD",
    "",
    formatCurrency(invoice.subtotal)
  );

  // const paidToDatePosition = subtotalPosition + 20;
  // generateTableRow(
  //     doc,
  //     paidToDatePosition,
  //     "",
  //     "",
  //     "Paid To Date",
  //     "",
  //     formatCurrency(invoice.paid)
  // );

  // const duePosition = paidToDatePosition + 25;
  // doc.font("Helvetica-Bold");
  // generateTableRow(
  //     doc,
  //     duePosition,
  //     "",
  //     "",
  //     "Balance Due",
  //     "",
  //     formatCurrency(invoice.subtotal - invoice.paid)
  // );
  // doc.font("Helvetica");
}
function wrapText(text, maxWordsPerLine) {
  const words = text.split(" ");
  const lines = [];
  for (let i = 0; i < words.length; i += maxWordsPerLine) {
    lines.push(words.slice(i, i + maxWordsPerLine).join(" "));
  }
  return lines;
}
function generateFooter(doc) {
  doc

    // .image("/home/<USER>/STC/titan/project/titan-d2d-backend/asset/static/titan_logo_sign.png", 300, 500, { width: 50, align: "center" })
    .fontSize(6)
    .text("Titan Company Limited", 50, 730, { align: "center" })
    .text(
      "INTEGRITY' No.193, Veerasandra, Electronics City P.O Off Hosur Main Road, Bengaluru - 560 100 India, Tel: 91 80 - 67047000, Fax: 91 80 - 67046262",
      50,
      740,
      { align: "center" }
    )
    .text(
      "Registered Office No. 3, SIPCOT Industrial Complex Hosur 635 126 TN India, Tel 91 4344 664 199, Fax 91 4344 276037, CIN: L74999TZ1984PLC001456",
      50,
      750,
      { align: "center" }
    )
    .text("www.titan.co.in", 50, 760, {
      align: "center",
      link: "https://www.titan.co.in",
      underline: true,
    })
    .text("A TATA Enterprise", 50, 770, { align: "center" });
}

function generateTableRow(
  doc,
  y,
  item,
  description,
  unitCost,
  quantity,
  lineTotal
) {
  doc
    .fontSize(10)
    .text(item, 50, y)
    .text(description, 150, y)
    .text(unitCost, 280, y, { width: 90, align: "right" })
    .text(quantity, 370, y, { width: 90, align: "right" })
    .text(lineTotal, 0, y, { align: "right" });
}

function generateHr(doc, y) {
  doc.strokeColor("#aaaaaa").lineWidth(1).moveTo(50, y).lineTo(550, y).stroke();
}

function formatCurrency(val) {
  return "€" + val?.toFixed(2);
}

function formatDate(date) {
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();

  return year + "/" + month + "/" + day;
}

export { generateInvoicePdf };
