import express from 'express';
import {
  getStatus,
  createStatus,
  getOneStatus,
  updateStatus,
  deleteStatus,
  deleteStatusCustomField,
  updateStatusCustomField,
  filterStatusPayloadKeys
} from '../controller/status.controller.js';

const router = express.Router();

router
  .route('/')
  .get(getStatus)
  .post(createStatus);

router.route('/:id/custom_field/:fieldId').delete(deleteStatusCustomField);

router
  .route('/:id')
  .get(getOneStatus)
  .patch(filterStatusPayloadKeys, updateStatusCustomField, updateStatus)
  .delete(deleteStatus);

export default router;