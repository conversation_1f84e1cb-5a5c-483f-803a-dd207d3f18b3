import axios from "axios";
import xlsx from "xlsx";
import NewAppError from "../utils/newAppError.js";
import Department from "../model/department.model.js";
import Distributor from "../model/distributor.model.js";
import EmailNotification from "../model/emailNotification.model.js";
import Order from "../model/order.model.js";
import Shipment from "../model/shipment.model.js";
import Status from "../model/status.model.js";
import Timeline from "../model/timeline.model.js";
import AppError from "../utils/appError.js";
import { getDepartmentForCreated } from "../utils/findNotifiedDepartment.js";
import { createAddressInDB } from "./addressService.js";
import { getErpItemsAvailableInventoryUtility } from "../utils/ERPUtils/erpItemUtils.js";
import { validateSkusSubsetUtility } from "../utils/helperFunction.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import {
  MARK_ORDER_PAID,
  ORDER_FINANCIAL_STATUS,
} from "../queries/internalQueries.js";

export const breakOrderBasedOnShipment = async (orderPayload, order_id) => {
  const variantIds = orderPayload.line_items.map(
    (item) => `gid://shopify/ProductVariant/${item.variant_id}`
  );
  const getInventoryAvailable = await getTotalInventoryAvailable(variantIds);
  const availableShipment = [];
  const pendingShipment = [];

  // orderPayload.line_items.forEach(item => {
  //   availableShipment.push(item);
  // });

  if (getInventoryAvailable.data.nodes.length > 0) {
    orderPayload.line_items.forEach((item) => {
      const inventoryItem = getInventoryAvailable.data.nodes.find(
        (inventory) =>
          inventory.id === `gid://shopify/ProductVariant/${item.variant_id}`
      );
      console.log("inventoryItem", inventoryItem);
      if (inventoryItem) {
        console.log("item.quatity", item.quatity);
        console.log(
          "inventoryItem.quantityAvailable",
          inventoryItem.quantityAvailable
        );
        if (item.quantity >= inventoryItem.quantityAvailable) {
          pendingShipment.push(item);
          console.log(
            `Item with variant_id ${item.variant_id} has more quantity ordered than available.`
          );
        } else {
          availableShipment.push(item);
          console.log(
            `Item with variant_id ${item.variant_id} has sufficient quantity available.`
          );
        }
      }
    });
  }

  const totalAvaiableQuantity = availableShipment.reduce((sum, item) => {
    return sum + (item.quantity * 1 || 0);
  }, 0);

  const totalAvaiableAmount = availableShipment.reduce((sum, item) => {
    return sum + (item.price * 1 || 0);
  }, 0);

  const totalPendingQuantity = pendingShipment.reduce((sum, item) => {
    return sum + (item.quantity * 1 || 0);
  }, 0);

  const totalPendingAmount = pendingShipment.reduce((sum, item) => {
    return sum + (item.price * 1 || 0);
  }, 0);

  console.log(availableShipment, " availableShipment");
  console.log(pendingShipment, " pendingShipment");

  let createdAlignedShipment;
  let createdUnAlignedShipment;
  if (availableShipment.length > 0) {
    const initialShipmentStatus = await Status.findOne({
      statusType: "Shipment",
      isInitialStatus: true,
    });
    console.log("initialShipmentStatus", initialShipmentStatus);
    createdAlignedShipment = await Shipment.create({
      name: "WATCHES",
      sku: orderPayload.line_items[0].sku,
      order: order_id,
      ref: `${order_id}_01`,
      shopifyOrderId: orderPayload._id,
      items: totalAvaiableQuantity,
      status: initialShipmentStatus._id,
      amount: totalAvaiableAmount * 1,
      // attributes: [
      //   {
      //     name: "created_notifications_mail",
      //     type: "JSON",
      //     value: orderPayload.customer.email
      //   }
      // ]
    });
    console.log("ALLIGNED createdAlignedShipment", createdAlignedShipment);
    await Timeline.create({
      category: "Shipment",
      message: `Shipment ${createdAlignedShipment?._id || "ALIGNED"} created`,
      order: order_id,
    });
  }

  if (pendingShipment.length > 0) {
    const pendingShipmentStatus = await Status.findOne({
      pseudoId: "PENDING_SHIPMENT",
    });
    const isAlignedShipmentAvailable = availableShipment.length > 0;
    createdUnAlignedShipment = await Shipment.create({
      name: "WATCHES",
      sku: orderPayload.line_items[0].sku,
      order: order_id,
      ref: isAlignedShipmentAvailable ? `${order_id}_02` : `${order_id}_01`,
      shopifyOrderId: orderPayload.id,
      items: totalPendingQuantity,
      //TODO: CHANGE THIS TO DYAMIC
      status: pendingShipmentStatus._id,
      // status: "663802e1fd5805a629133c20",
      amount: totalPendingAmount * 1,
      // attributes: [
      //   {
      //     name: "created_notifications_mail",
      //     type: "JSON",
      //     value: orderPayload.customer.email
      //   }
      // ]
    });
    console.log("PENDING createdAlignedShipment", createdAlignedShipment);

    await Timeline.create({
      category: "Shipment",
      message: `Shipment ${createdUnAlignedShipment?._id || "Pending"} created`,
      order: order_id,
    });
  }
  const orderPartialStatusDetail = await Status.findOne({
    statusType: "Order",
    pseudoId: "ORDER_PARTIALLY_ALIGNED",
  });
  const orderFullyStatusDetail = await Status.findOne({
    statusType: "Order",
    pseudoId: "ORDER_FULLY_ALIGNED",
  });
  if (pendingShipment.length > 0) {
    // TODO: BY DEFAULT MAKING ORDER FULLY ALLIGNED
    await Order.updateOne(
      { _id: order_id },
      { status: orderPartialStatusDetail._id }
    );
    // await Order.updateOne({_id: order_id}, {status: orderFullyStatusDetail._id})
  } else {
    await Order.updateOne(
      { _id: order_id },
      { status: orderFullyStatusDetail._id }
    );
  }
  return {
    createdAlignedShipment,
    createdUnAlignedShipment,
  };
};

export const getTotalInventoryAvailable = async (variantIds) => {
  const myHeaders = new Headers();
  myHeaders.append(
    "X-Shopify-Storefront-Access-Token",
    process.env.SHOPIFY_STOREFRONT_ACCESS_TOKEN
  );
  myHeaders.append("Content-Type", "application/json");

  const graphql = JSON.stringify({
    query: `{\r\n  nodes(ids: ${JSON.stringify(
      variantIds
    )}) {\r\n    ... on ProductVariant {\r\n      id\r\n      quantityAvailable\r\n    }\r\n  }\r\n}\r\n`,
    variables: {},
  });

  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: graphql,
    redirect: "follow",
  };

  const resultPromise = await fetch(
    `${process.env.SHOP_URL}/api/${process.env.VERSION}/graphql.json`,
    requestOptions
  );
  const result = await resultPromise.json();

  return result;
};

// export const createOrderWithAvailableQuantity = async () => {
//   const myHeaders = new Headers();
//   myHeaders.append("Content-Type", "application/json");
//   myHeaders.append("X-Shopify-Access-Token", process.env.SHOPIFY_ADMIN_ACCESS_TOKEN);
//   const requestOptions = {
//     method: "POST",
//     headers: myHeaders,
//     body: raw,
//     redirect: "follow"
//   };

//   const resultPromise = await fetch(`${process.env.SHOP_URL}/api/createOrder`, requestOptions);
//   const finalResult = await resultPromise.json();

//   return finalResult;
// }

export const extractAllTheOrderIds = async (pastDate, currentDate) => {
  const ids = await Order.distinct("_id", {
    createdAt: {
      $gte: pastDate,
      $lte: currentDate,
    },
  });
  return ids;
};

export async function createOrderInDBService(orderData) {
  try {
    const { line_items } = orderData;

    if (!line_items) {
      return new AppError("line items are mandatory.", 400);
    }

    const lineItemArray = line_items.map((item) => ({
      lineItemId: item?.id?.toString(),
      shopifyVariantId: item.variant_id?.toString(),
      shopifyProductId: item.product_id?.toString(),
      productTitle: item.title,
      variantTitle: item.variant_title,
      image: "",
      quantity: item.quantity,
      price: parseFloat(item.price),
      sku: item.sku,
      productCategory: "",
      metaFieldBudgetCategory: item.meta_field_budget_category,
      metaFieldPreOrder: item.meta_field_pre_order,
    }));

    const initialOrderStatus = await Status.findOne({
      statusType: "Order",
      isInitialStatus: true,
    });
    if (!initialOrderStatus) {
      return new AppError(
        "Missing Initial Order Status. Please create one.",
        400
      );
    }
    const originalDistributor = await Distributor.findOne({
      shopifyCustomerId: orderData?.customer?.id
        ? parseInt(orderData.customer.id)
        : null,
    });
    if (!originalDistributor) {
      return new AppError("Customer is missing!", 400);
    }

    const orderPayload = {
      order_id: orderData.id,
      subOrderId: orderData.subOrderId,
      type: orderData.orderType, // Change from orderData.type to orderData.orderType
      preOrderValue: orderData.preOrderValue,
      budgetCategoryValue: orderData.budgetCategoryValue,
      category: orderData.category,
      shopifyCompanyId: originalDistributor.shopifyCompanyId,
      status: "created",
      distributor: originalDistributor._id,
      customer: orderData.customer,
      line_items: lineItemArray,
      current_subtotal_price: orderData.current_subtotal_price,
      name: orderData.name,
    };

    const createdOrder = await Order.create(orderPayload);

    const departmentPeoples = await Department.find({
      departmentType: { $in: [initialOrderStatus.departmentType._id] },
    });

    const departmentDetails = await getDepartmentForCreated(
      initialOrderStatus._id
    );

    const recipients = departmentPeoples.map((departmentPeople) => ({
      name: departmentPeople.name,
      email: departmentPeople.email,
      cc: Array.isArray(departmentDetails) ? [...departmentDetails] : [],
    }));

    // if (recipients.length > 0) {
    //   await EmailNotification.create({
    //     emailCategory: "ORDER",
    //     emailType: "ORDER_CREATE",
    //     reciepient: recipients,
    //     emailPayload: {
    //       orderName: createdOrder.name,
    //       date: createdOrder?.createdAt,
    //       orderStatus: initialOrderStatus.status,
    //       distributorName: originalDistributor.name,
    //       cartProducts: createdOrder.line_items,
    //     },
    //   });
    // }

    let countryManagerEmails = [];

    if (originalDistributor) {
      if (
        originalDistributor.countryManager &&
        originalDistributor.countryManager.length > 0
      ) {
        await Promise.all(
          originalDistributor.countryManager.map(async (x) => {
            const manager = await Department.findById(x);
            if (manager) {
              countryManagerEmails.push({
                email: manager.email,
                name: manager.name,
              });
            }
          })
        );
      }
    }

    const recipientsPeople = countryManagerEmails.map((manager) => ({
      name: manager.name,
      email: manager.email,
      cc: Array.isArray(departmentDetails) ? [...departmentDetails] : [],
    }));

    if (recipientsPeople.length > 0) {
      await EmailNotification.create({
        emailCategory: "ORDER",
        emailType: "ORDER_CREATE_NOTIFY_MANAGER",
        reciepient: recipientsPeople,
        emailPayload: {
          orderName: createdOrder.name,
          orderStatus: initialOrderStatus.status,
          date: createdOrder.createdAt,
          // attachments: [orderSheetUploadResponse.Location],
          originalDistributorName: originalDistributor.name,
          originalDistributorEmail: originalDistributor.email,
          cartProducts: createdOrder.line_items,
        },
      });
    }
    return createdOrder;
  } catch (error) {
    console.error("Error in createOrderInDBService: ", error);
    return new AppError("Failed to create order in database.", 500);
  }
}

export const validateAllocationQuantityWithERPService = async (jsonData) => {
  try {
    const allSkus = jsonData.map((x) => x.Sku);

    const itemsInventoryAvailableErp =
      await getErpItemsAvailableInventoryUtility(allSkus);
    if (itemsInventoryAvailableErp instanceof NewAppError) {
      return itemsInventoryAvailableErp;
    }

    const allocatedMoreThanAvailable = jsonData.filter((order) => {
      const matchingSku = itemsInventoryAvailableErp.data.items.find(
        (item) => order.Sku === item?.sku
      );

      return order.AllocatedQuantity > matchingSku?.quantity || 0;
    });
    const skusWithInvalidAllocation = allocatedMoreThanAvailable.map(
      (items) => items.Sku
    );

    if (allocatedMoreThanAvailable.length > 0) {
      return { allocationValid: false, skusWithInvalidAllocation };
    }
    return { allocationValid: true };
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error valdating sheet allocation from ERP.Error:${error.message}`,
      500
    );
  }
};

export const generateOrderSheetNewService = async (orderId) => {
  try {
    const order = await Order.findOne({ _id: orderId });
    if (!order) {
      return new NewAppError(
        "NOT_FOUND",
        `Order with ID ${orderId} not found`,
        400
      );
    }

    let sheetHeader = ["productTitle", "sku", "quantity", "price"];

    const sheetPayload = [sheetHeader];

    order.line_items.forEach((lineItem) => {
      let filteredItem = [];
      sheetHeader.forEach((key) => {
        if (lineItem.hasOwnProperty(key)) {
          filteredItem.push(lineItem[key] ? lineItem[key] : "");
        } else {
          filteredItem.push("");
        }
      });
      sheetPayload.push(filteredItem);
    });

    sheetPayload[0] = [
      "Title",
      "Sku",
      "OrderedQuantity",
      "Price",
      "AllocatedQuantity",
    ];

    const workbook = xlsx.utils.book_new();
    const sheet = xlsx.utils.aoa_to_sheet(sheetPayload);
    xlsx.utils.book_append_sheet(workbook, sheet, "Sheet1");
    const fileName = `OrderName-${order.name}-m1.xlsx`;

    return {
      fileName,
      workbook,
    };
  } catch (error) {
    console.error("Error while creating or handling the file:", error.message);

    return new NewAppError(
      "SERVER_ERROR",
      "Error while creating or handling the file.",
      500
    );
  }
};

export async function createERPOrderHandler(alignedShipmentId) {
  try {
    const response = await axios.post(
      `${process.env.APP_URL}/api/ERPSync/orders`,
      { shipmentId: alignedShipmentId }
    );

    return response.data.data.order;
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      response.data.data.errors[0].message,
      500
    );
  }
}

export function checkUploadedOrder(requestedItems, existingItems) {
  try {
    const requiredKeys = [
      "Title",
      "OrderedQuantity",
      "Sku",
      "Price",
      "AllocatedQuantity",
    ];

    const skuCount = {};
    let totalQuantity = 0;

    requestedItems.forEach((lineItem) => {
      skuCount[lineItem.Sku] = (skuCount[lineItem.Sku] || 0) + 1;
    });

    const skusSubsetOfOrder = validateSkusSubsetUtility(
      requestedItems,
      existingItems
    );
    if (!skusSubsetOfOrder.valid) {
      return {
        valid: false,
        error: skusSubsetOfOrder.error,
      };
    }

    for (let index = 0; index < requestedItems.length; index++) {
      const lineItem = requestedItems[index];
      totalQuantity += lineItem.AllocatedQuantity;
      // Check for missing keys
      const missingKeys = requiredKeys.filter((key) => !(key in lineItem));

      if (missingKeys.length > 0) {
        return {
          valid: false,
          error: `Line item ${index + 1}: Missing Columns.`,
        };
      }

      const emptyValues = Object.entries(lineItem).filter(
        ([key, value]) => value === null || value === "" || value === undefined
      );

      if (emptyValues.length > 0) {
        return {
          valid: false,
          error: `Line item ${index + 1}: Empty Entry Found.`,
        };
      }

      // Check if Price is a number
      if (typeof lineItem.Price !== "number") {
        return {
          valid: false,
          error: `Line item ${index + 1}: Price must be a number.`,
        };
      }

      // Check for duplicate Sku
      if (skuCount[lineItem.Sku] > 1) {
        return {
          valid: false,
          error: `Line item ${index + 1}: Duplicate Sku found.`,
        };
      }
    }

    if (totalQuantity === 0) {
      return {
        valid: false,
        error:
          "Enter valid Quantity, at least 1 sku should have quantity greater than 0.",
      };
    }
    return { valid: true };
  } catch (error) {
    return {
      valid: false,
      error: `Error while validiting Upload.`,
    };
  }
}

export async function cancelOrderService(orderId) {
  try {
    let data = JSON.stringify({
      restock: true,
      reason: "customer",
      email: true,
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/orders/${orderId}/cancel.json`,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      },
      data: data,
    };

    const response = await axios.request(config);
    const id = response.data.order.id;

    return { id };
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Could not cancel order with ID orderId:${orderId}.`,
      500
    );
  }
}

export async function orderMarkAsPaidService(orderId) {
  try {
    const orderGid = `gid://shopify/Order/${orderId}`;
    const orderPaidResponse = await fetchGraphqlDataShopify(MARK_ORDER_PAID, {
      orderId: orderGid,
    });

    const orderMarkAsPaid = orderPaidResponse.data.orderMarkAsPaid;
    const userErrors = orderMarkAsPaid.userErrors;
    if (userErrors.length > 0) {
      return new NewAppError(
        "SERVER_ERROR",
        `Could not cancel.Message:Mark as paid failed.`,
        500
      );
    }

    const markedOrderId = orderMarkAsPaid.order.id;
    return markedOrderId;
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Could not cancel.Message:Mark as paid failed.`,
      500
    );
  }
}
export async function getOrderFinancialStatusService(orderId) {
  try {
    const orderGid = `gid://shopify/Order/${orderId}`;
    const orderFinancialStatusResponse = await fetchGraphqlDataShopify(
      ORDER_FINANCIAL_STATUS,
      {
        orderId: orderGid,
      }
    );

    const orderFinancialStatus =
      orderFinancialStatusResponse.data.order.displayFinancialStatus;

    return { id: orderId, orderFinancialStatus };
  } catch (error) {
    console.log("error", error.message);
    return new NewAppError(
      "SERVER_ERROR",
      `Could not cancel.Message:Failed while processing shipment.`,
      500
    );
  }
}

export async function addErpIdentifierOrderDbService(orderId, erpResponse) {
  try {
    const addErpIdentifier = await Order.findByIdAndUpdate(
      orderId,
      {
        $set: {
          erpIdentifier: Number(erpResponse.data.order.OrderUniquifier),
          status: "order placed",
          sageOrderNumber: erpResponse.data.order.OrderNumber,
        },
      },
      { new: true }
    );
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error while processing Order.${error.message}`,
      500
    );
  }
}
