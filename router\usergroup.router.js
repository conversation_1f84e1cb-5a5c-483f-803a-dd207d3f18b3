import express from "express";

import {
  getUserGroups,
  getOneUserGroup,
  createUserGroup,
  updateUserGroup,
  deleteUserGroup,
} from "../controller/usergroup.controller.js";
import authenticateUserAccess from "../middlewares/authenticateUserAccess.js";

const router = express.Router();

router.use(authenticateUserAccess());
router.route("/").get(getUserGroups).post(createUserGroup);

router
  .route("/:id")
  .get(getOneUserGroup)
  .patch(updateUserGroup)
  .delete(deleteUserGroup);

export default router;
