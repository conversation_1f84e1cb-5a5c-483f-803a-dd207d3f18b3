import axios from "axios";
import ProductMap from "../../model/erp.productmap.model.js";
import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";
import {
  activateInventoryItemAtLocation,
  createProductWithVariants,
  createVariants,
  getPublications,
  PublishProductToOnlineStore,
  setInventoryLevel,
  updateProductMetafieldMutation,
  updateProductVariants,
} from "../../controller/shopify.controller.js";
import NewAppError from "../../utils/newAppError.js";
import FailedProductLog from "../../model/erp.product.error.model.js";
import { GET_LOCATION_BY_NAME } from "../../queries/internalQueries.js";
import {
  LOCATION_CODE,
  OPTIONAL_FIELDS,
} from "../../constants/erpConstants.js";

const fetchERPItemsBatch = async (page = 1, pageSize = 50000) => {
  try {
    const url = `${process.env.SAGE_CUSTOM_API_ENDPOINT}/api/ICItem/GetICItems`;
    const params = {
      company: process.env.SAGE_COMPANY,
      APIKey: process.env.SAGE_CUSTOM_API_KEY,
      page_no: page,
      page_size: pageSize,
      Location: LOCATION_CODE,
      OptionalFields: OPTIONAL_FIELDS,
      B2BorB2C: "B2B",
      Active: "True",
    };
    const response = await axios.get(url, { params });
    return response.data || [];
  } catch (error) {
    console.error(`Error fetching ERP items on page ${page}:`, error.message);
    return [];
  }
};

const fetchLocationMap = async () => {
  try {
    const names = ["1SR", "2FW", "WH9", "B2B"];
    const locationMap = {};
    for (const name of names) {
      const response = await fetchGraphqlDataShopify(GET_LOCATION_BY_NAME, {
        name,
      });
      const edge = response?.data?.locations?.edges?.[0];
      if (edge) locationMap[name] = edge.node.id;
    }
    return locationMap;
  } catch (error) {
    console.log(error);
    throw new NewAppError("FETCH_LOCATION_ERROR", error.message, 500);
  }
};

const groupBySegment1 = (items) =>
  items.reduce((acc, item) => {
    const segment1 = item.ItemNumber?.split("-")[0]?.trim();
    if (!acc[segment1]) acc[segment1] = [];
    acc[segment1].push(item);
    return acc;
  }, {});

const extractLocationInfo = (item) => {
  try {
    const codes = Object.keys(item)
      .filter((k) => k.endsWith("_QuantityOnHand"))
      .map((k) => k.split("_")[0]);
    for (const code of codes) {
      const onHand = item[`${code}_QuantityOnHand`];
      const available = item[`${code}_QuantityAvailable`];
      if (onHand > 0 || available > 0) {
        return {
          location: code,
          quantityOnHand: onHand,
          quantityAvailable: available,
        };
      }
    }
    return { location: "B2B", quantityOnHand: 0, quantityAvailable: 0 };
  } catch (error) {
    console.log(error);
    throw new NewAppError("CREATE_PRODUCT_ERROR", error.message, 500);
  }
};

const chunkArray = (arr, size) => {
  const result = [];
  for (let i = 0; i < arr.length; i += size)
    result.push(arr.slice(i, i + size));
  return result;
};

const saveFailedProduct = async (segment1, sku, event, data, errorMessage) => {
  try {
    await new FailedProductLog({
      segment1,
      sku,
      event,
      data,
      errorMessage,
    }).save();
  } catch (error) {
    console.error("Failed to log error:", error.message);
  }
};

const createProductWithVariant = async (
  segment1,
  variants,
  baseItem,
  locationMap
) => {
  try {
    const productMetafields = [
      {
        namespace: "custom",
        key: "model",
        type: "single_line_text_field",
        value: segment1,
      },
    ];
    if (baseItem.SHOPIFY1) {
      productMetafields.push({
        namespace: "custom",
        key: "shopify_1",
        type: "single_line_text_field",
        value: baseItem.SHOPIFY1.trim(),
      });
    }
    if (baseItem.SHOPIFY2) {
      productMetafields.push({
        namespace: "custom",
        key: "shopify_2",
        type: "single_line_text_field",
        value: baseItem.SHOPIFY2.trim(),
      });
    }
    if (baseItem.SHOPIFY3) {
      productMetafields.push({
        namespace: "custom",
        key: "budget_category",
        type: "single_line_text_field",
        value: baseItem.SHOPIFY3.trim(),
      });
      productMetafields.push({
        namespace: "custom",
        key: "shopify_3",
        type: "single_line_text_field",
        value: baseItem.SHOPIFY3.trim(),
      });
    }
    const variantInputs = variants.map((item) => {
      const { location, quantityAvailable } = extractLocationInfo(item);
      return {
        price: String(item.BasePrice || 0),
        compareAtPrice: String(item.compareAtPrice || 0),
        sku: item.UnformattedItemNumber,
        inventoryItem: { tracked: true, sku: item.UnformattedItemNumber },
        inventoryPolicy: "CONTINUE",
        inventoryQuantities: [
          {
            locationId: locationMap[location.trim()],
            name: "available",
            quantity: quantityAvailable,
          },
        ],
        metafields: [
          {
            namespace: "custom",
            type: "single_line_text_field",
            key: "compare_at_price",
            value: String(item.compareAtPrice || 0),
          },
        ],
        optionValues: [
          {
            name:
              item.Color?.trim() === "ZZZZ" || !item.Color?.trim()
                ? "NA"
                : item.Color?.trim(),
            optionName: "Color",
          },
          {
            name:
              item.Size?.trim() === "ZZZZ" || !item.Size?.trim()
                ? "NA"
                : item.Size?.trim(),
            optionName: "Size",
          },
        ],
      };
    });

    const uniqueColors = [
      ...new Set(
        variants.map((v) =>
          v.Color?.trim() === "ZZZZ" || !v.Color?.trim()
            ? "NA"
            : v.Color?.trim()
        )
      ),
    ];
    const uniqueSizes = [
      ...new Set(
        variants.map((v) =>
          v.Size?.trim() === "ZZZZ" || !v.Size?.trim() ? "NA" : v.Size?.trim()
        )
      ),
    ];

    const variable = {
      inventoryPolicy: "CONTINUE",
      input: {
        title: baseItem.Description,
        productOptions: [
          {
            name: "Color",
            position: 1,
            values: uniqueColors.map((name) => ({ name })) || "NA",
          },
          {
            name: "Size",
            position: 2,
            values: uniqueSizes.map((name) => ({ name })) || "NA",
          },
        ],
        metafields: productMetafields,
        status: "DRAFT",
        variants: variantInputs,
      },
    };

    const response = await createProductWithVariants(variable);

    const result = response.data?.productSet;
    if (result?.userErrors?.length) {
      throw new NewAppError(
        "SHOPIFY_ERROR",
        result.userErrors.map((e) => e.message).join(", "),
        400
      );
    }
    const publications = await getPublications();

    const publicationData = publications.filter(
      (x) => x.node?.name === "Online Store"
    );
    const publishedToStore = await PublishProductToOnlineStore({
      id: result.product.id,
      input: {
        publicationId: publicationData[0]?.node?.id,
      },
    });

    if (publishedToStore?.data?.publishablePublish?.userErrors?.length) {
      throw new NewAppError(
        "SHOPIFY_ERROR",
        publishedToStore.data.publishablePublish.userErrors
          .map((e) => e.message)
          .join(", "),
        400
      );
    }

    const productId = result.product.id;
    const variantNodes = result.product.variants.nodes;
    await ProductMap.findOneAndUpdate(
      { segment1 },
      {
        $set: { segment1, productId },
        $push: {
          variants: variantNodes.map((v) => ({
            sku: v.sku,
            variantId: v.id,
            price: v.price,
          })),
        },
      },
      { upsert: true }
    );
    return { productId, variants: variantNodes };
  } catch (error) {
    console.log(error);
    throw new NewAppError("CREATE_PRODUCT_ERROR", error.message, 500);
  }
};

export const updateVariantsInBulk = async (
  productId,
  variantsToUpdate = [],
  locationMap = {}
) => {
  if (!variantsToUpdate.length) return;

  try {
    const variantInputs = variantsToUpdate.map((variant) => {
      const metafields = [];

      if (variant.compareAtPrice) {
        metafields.push({
          namespace: "custom",
          type: "single_line_text_field",
          key: "compare_at_price",
          value: String(variant.compareAtPrice || 0),
        });
      }

      return {
        id: variant.id,
        price: variant.price?.toString() || "0.00",
        metafields,
        optionValues: variant.optionValues,
        inventoryItem: {
          sku: variant.sku,
          tracked: true,
        },
        inventoryPolicy: "CONTINUE",
      };
    });

    const response = await updateProductVariants({
      productId,
      variantsInput: variantInputs,
    });
    const errors = response.data?.productVariantsBulkUpdate?.userErrors || [];

    if (errors.length) {
      console.error("Variant update errors:", errors);
      throw new NewAppError(
        "SHOPIFY_BULK_UPDATE_ERROR",
        errors.map((e) => e.message).join(", "),
        400
      );
    }
    const updatedVariants =
      response.data?.productVariantsBulkUpdate?.productVariants || [];

    // Update inventory for each variant
    await Promise.all(
      updatedVariants.map(async (updated, index) => {
        const inventoryItemId = updated.inventoryItem?.id;
        const { itemLocation, quantityAvailable } = variantsToUpdate[index];
        const locationId = locationMap[itemLocation?.trim()] || null;

        if (inventoryItemId && locationId) {
          await activateInventoryItemAtLocation(inventoryItemId, locationId);
          await setInventoryLevel(
            inventoryItemId,
            locationId,
            quantityAvailable
          );
        }
      })
    );

    return updatedVariants;
  } catch (error) {
    console.error("Error during bulk variant update:", error.message);
    throw new NewAppError("SHOPIFY_BULK_UPDATE_ERROR", error.message, 500);
  }
};

export const createVariantsInBulk = async (
  productId,
  variantsToCreate = [],
  locationMap = {}
) => {
  if (!variantsToCreate.length) return;

  try {
    const variantInputs = variantsToCreate.map((variant) => {
      const metafields = [];

      if (variant.compareAtPrice) {
        metafields.push({
          namespace: "custom",
          type: "single_line_text_field",
          key: "compare_at_price",
          value: variant.compareAtPrice?.trim(),
        });
      }

      return {
        price: variant.itemPriceData?.toString() || "0.00",
        // metafields,
        optionValues: variant.optionValues,
        inventoryItem: {
          sku: variant.sku,
          tracked: true,
        },
        inventoryPolicy: "CONTINUE",
      };
    });

    const response = await createVariants({
      productId,
      variantsInput: variantInputs,
    });

    const errors = response.data?.productVariantsBulkCreate?.userErrors || [];

    if (errors.length) {
      console.error("Variant create errors:", errors);
      throw new NewAppError(
        "SHOPIFY_BULK_UPDATE_ERROR",
        errors.map((e) => e.message).join(", "),
        400
      );
    }

    const createdVariants =
      response?.data?.productVariantsBulkCreate?.productVariants || [];

    const variantsToUpdate = createdVariants.map((created, index) => ({
      id: created.id,
      sku: variantsToCreate[index].sku,
      itemLocationId: variantsToCreate[index].itemLocation,
      quantityAvailable: variantsToCreate[index].quantityAvailable,
    }));

    const updatedVariants = await updateVariantsInBulk(
      productId,
      variantsToUpdate,
      locationMap
    );
    // Update inventory for each variant
    await Promise.all(
      createdVariants.map(async (updated, index) => {
        const inventoryItemId = updated.inventoryItem?.id;
        const { itemLocation, quantityAvailable } = variantsToCreate[index];

        const locationId = locationMap[itemLocation?.trim()] || null;

        if (inventoryItemId && locationId) {
          await activateInventoryItemAtLocation(inventoryItemId, locationId);
          await setInventoryLevel(
            inventoryItemId,
            locationId,
            quantityAvailable
          );
        }
      })
    );

    return updatedVariants;
  } catch (error) {
    console.error("Error during bulk variant update:", error.message);
    throw new NewAppError("SHOPIFY_BULK_UPDATE_ERROR", error.message, 500);
  }
};

const addOrUpdateVariants = async (
  productId,
  segment1,
  variants,
  existingVariants,
  locationMap
) => {
  try {
    const existingSKUs = existingVariants.map((v) => v.sku);

    const newVariants = [];
    const updateVariants = [];

    for (const item of variants) {
      const { location, quantityAvailable } = extractLocationInfo(item);
      const variantData = {
        price: String(item.BasePrice || 0),
        sku: item.UnformattedItemNumber,
        compareAtPrice: item.compareAtPrice?.toString() || "0.00",
        optionValues: [
          {
            name:
              item.Color?.trim() === "ZZZZ" ||
              item.Color?.trim() === "ZZZZZZ" ||
              !item.Color?.trim()
                ? "NA"
                : item.Color?.trim(),
            optionName: "Color",
          },
          {
            name:
              item.Size?.trim() === "ZZZZ" ||
              item.Size?.trim() === "ZZZZZZ" ||
              !item.Size?.trim()
                ? "NA"
                : item.Size?.trim(),
            optionName: "Size",
          },
        ],
      };

      if (existingSKUs.includes(item.UnformattedItemNumber)) {
        const matched = existingVariants.find(
          (v) => v.sku === item.UnformattedItemNumber
        );
        if (matched?.variantId) {
          updateVariants.push({
            id: matched.variantId,
            sku: variantData.sku,
            price: variantData.price,
            metafields: variantData.metafields,
            optionValues: variantData.optionValues,
            itemLocation: location,
            quantityAvailable,
          });
        }
      } else {
        newVariants.push({
          ...variantData,
          itemLocation: location,
          quantityAvailable,
        });
      }
    }

    // Create new variants
    if (newVariants.length) {
      const created = await createVariantsInBulk(
        productId,
        newVariants,
        locationMap
      );

      await ProductMap.findOneAndUpdate(
        { segment1 },
        {
          $push: {
            variants: created.map((v) => ({
              sku: v.sku,
              variantId: v.id,
            })),
          },
        }
      );
    }

    // Update existing variants
    if (updateVariants.length) {
      const response = await updateVariantsInBulk(
        productId,
        updateVariants,
        locationMap
      );

      const errors = response.data?.productVariantsBulkUpdate?.userErrors;
      if (errors?.length) {
        console.error("Variant update errors:", errors);
        throw new NewAppError(
          "SHOPIFY_UPDATE_ERROR",
          errors.map((e) => e.message).join(", "),
          400
        );
      }
    }

    // Update product metafields for shopify_1, shopify_2, shopify_3, budget_category
    const baseItem = variants[0] || {};
    const metafieldsToUpdate = [];
    if (baseItem.SHOPIFY1) {
      metafieldsToUpdate.push({
        namespace: "custom",
        key: "shopify_1",
        type: "single_line_text_field",
        value: baseItem.SHOPIFY1.trim(),
        ownerId: productId,
      });
    }
    if (baseItem.SHOPIFY2) {
      metafieldsToUpdate.push({
        namespace: "custom",
        key: "shopify_2",
        type: "single_line_text_field",
        value: baseItem.SHOPIFY2.trim(),
        ownerId: productId,
      });
    }
    if (baseItem.SHOPIFY3) {
      metafieldsToUpdate.push({
        namespace: "custom",
        key: "shopify_3",
        type: "single_line_text_field",
        value: baseItem.SHOPIFY3.trim(),
        ownerId: productId,
      });
      metafieldsToUpdate.push({
        namespace: "custom",
        key: "budget_category",
        type: "single_line_text_field",
        value: baseItem.SHOPIFY3.trim(),
        ownerId: productId,
      });
    }
    if (metafieldsToUpdate.length) {
      const resp = await updateProductMetafieldMutation({ metafields: metafieldsToUpdate });
      // console.log("response",JSON.stringify(resp))
      const metafieldUserErrors = resp.data?.metafieldsSet.userErrors  || [];
      if(metafieldUserErrors.Length){
        throw new NewAppError("SHOPIFY_UPATE_ERROR",400)
      }
    }
  } catch (error) {
    console.error("Error during variant add or  update:", error);
    throw new NewAppError("SHOPIFY_UPDATE_ERROR", error.message, 500);
  }
};

export const syncERPProducts = async () => {
  try {
    const startTime = Date.now();
    let page = 1;
    const pageSize = 50000;
    let processedProducts = 0;
    let totalProducts = 0;
    const locationMap = await fetchLocationMap();

    console.log("Starting ERP to Shopify sync...");

    while (true) {
      const items = await fetchERPItemsBatch(page, pageSize);
      // items = items.filter((x) => x.ItemNumber?.includes("BSHAKAYUSUP5"));
      if (!items.length) break;
      if (page === 1) {
        totalProducts = items.length;
        console.log(`Total products to process: ${totalProducts}`);
      }

      //  Group first by segment1
      const grouped = groupBySegment1(items);

      // Convert grouped object into array of [segment1, variants]
      const groupedArray = Object.entries(grouped);

      // Then batch these entries in chunks of 100 segment1 groups
      const groupedBatches = chunkArray(groupedArray, 100);

      for (const groupBatch of groupedBatches) {
        for (const [segment1, variants] of groupBatch) {
          try {
            const existing = await ProductMap.findOne({ segment1 });
            if (!existing) {
              console.log(`Creating product: ${segment1}`);
              await createProductWithVariant(
                segment1,
                variants,
                variants[0],
                locationMap
              );
            } else {
              console.log(`Updating product: ${segment1}`);
              await addOrUpdateVariants(
                existing.productId,
                segment1,
                variants,
                existing.variants,
                locationMap
              );
            }

            processedProducts += variants.length;
            const remaining = totalProducts - processedProducts;

            console.log(
              `Segment: ${segment1} | Variants: ${variants.length} | Processed: ${processedProducts} | Remaining: ${remaining}`
            );
          } catch (err) {
            console.error(`Error processing ${segment1}:`, err.message);
            await Promise.all(
              variants.map((item) =>
                saveFailedProduct(
                  segment1,
                  item.UnformattedItemNumber,
                  "syncERPProducts",
                  { item },
                  err.message
                )
              )
            );
          }
        }
      }

      if (items.length < pageSize) break;
      page++;
    }

    const endTime = Date.now();
    const durationInMinutes = ((endTime - startTime) / 1000 / 60).toFixed(2);

    console.log(
      `Sync complete! Processed ${processedProducts} out of ${totalProducts} products over ${page} pages.`
    );
    console.log(`Total time taken: ${durationInMinutes} minutes.`);

    return {
      totalProducts,
      processedProducts,
      completedPages: page,
      durationInMinutes,
    };
  } catch (error) {
    console.error("syncERPProducts failed:", error.message);
    throw new NewAppError(
      "ERP_SYNC_FATAL_ERROR",
      error.message || "An unexpected error occurred during ERP sync.",
      500
    );
  }
};
