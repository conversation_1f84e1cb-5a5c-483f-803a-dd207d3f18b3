import mongoose from "mongoose";

const organizationSchema = new mongoose.Schema({
  name: {
    type: String
  },
  email: {
    type: String, 
  },
  designation: {
    type: String
  },
  type: {
    type: String
  },
  reporting: {
    type: String
  },
  distributor: {
    type: String
  },
  department: {
    type: String
  }
}, { timestamps: true });

const Organization = mongoose.model('Organization', organizationSchema);
export default Organization