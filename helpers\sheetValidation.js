const allocatedMoreThanOrderedCheck = async (
  existing,
  requestedSheetItems,
  type
) => {
  try {
    if (!type) {
      return null;
    }
    const errors = [];
    requestedSheetItems.forEach((item) => {
      const matchingItem = existing.find(
        (existingItem) => existingItem.sku === item.Sku
      );
      if (matchingItem) {
        const requestedQuantity =
          type === "order" ? matchingItem.quantity : matchingItem.requested;
        if (item.AllocatedQuantity > requestedQuantity) {
          errors.push(item.Sku);
        }
      }
    });

    if (errors.length > 0) {
      return {
        valid: false,
        errors,
      };
    }
    return { valid: true, message: "All allocations are valid." };
  } catch (error) {
    return null;
  }
};

export { allocatedMoreThanOrderedCheck };
