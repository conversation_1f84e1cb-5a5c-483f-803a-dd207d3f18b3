import Inventory from "../model/inventory.model.js";
import AppError from "./appError.js";
import AWS from "aws-sdk";
import fs from "fs";
import path from "path";
import jwt from "jsonwebtoken";
import newAppError from "../utils/newAppError.js";

export const toScreamingSnakeCase = (word) => {
  if (typeof word !== "string") throw new Error("Input must be a string");
  return word
    .trim()
    .replace(/[\s\-]+/g, "_")
    .toUpperCase();
};

export const filterObj = (obj, ...allowedFields) => {
  const newObj = {};
  Object.keys(obj).forEach((element) => {
    if (allowedFields.includes(element)) newObj[element] = obj[element];
  });
  return newObj;
};

export const removeObjectKeys = (obj, ...allowedFields) => {
  const newObj = {};
  Object.keys(obj).forEach((element) => {
    if (!allowedFields.includes(element)) newObj[element] = obj[element];
  });
  return newObj;
};

export const generateRandomNumber = () => {
  const minm = 10000;
  const maxm = 99999;
  return Math.floor(Math.random() * (maxm - minm + 1)) + minm;
};

export const validateProductSheetHeader = (data) => {
  const availableHeaderKeys = Object.keys(data);
  const allowedHeaderKeys = ["ProductCode", "Quantity"];
  // const allowedHeaderKeys = ['brand', 'ProductCode', 'skuType', 'Quantity', 'ucp', 'variantId'];
  const invalidKeys = allowedHeaderKeys.filter((headerKey) => {
    return !availableHeaderKeys.includes(headerKey);
  });
  return invalidKeys;
};

export const validateProductSheet = async (data) => {
  // const missingQuantitySkus = data.filter((item) => {
  //   return !item.Quantity;
  // }).map((it) => {
  //   return it.ProductCode;
  // });
  // if (missingQuantitySkus.length > 0) return { returnCode: 5, message: `Missing Quantity for following skus [${missingQuantitySkus}]` };
  const skuList = data
    .filter((it) => {
      // console.log((it))
      return it.Quantity && it.Quantity > 0;
    })
    .map((item) => {
      return item.ProductCode;
    });
  // console.log(skuList)
  if (skuList <= 0) return { returnCode: 5, message: `Missing Quantity` };

  const inventoryData = await Inventory.find({ sku: { $in: skuList } });
  return {
    returnCode: 0,
    inventoryData,
  };
};

export const uploadToS3 = async (filePath, bucketName, fileName) => {
  console.log(`Uploading`, filePath, bucketName);
  try {
    const s3 = new AWS.S3({
      credentials: {
        region: process.env.AWS_REGION,
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_ACCESS_KEY_ID_SECRET,
      },
    });
    const fileContent = fs.readFileSync(filePath);
    const fileExtension = path.extname(filePath);
    const newFileName =
      fileName || `line_sheet_${Date.now().toString()}${fileExtension}`;
    console.log(newFileName);
    // const newFileName = `line_sheet_${new Date.now().toString()}.${file.mimetype.split('/')[1]}`;
    const params = {
      Bucket: bucketName,
      Key: newFileName,
      Body: fileContent,
    };
    return new Promise((resolve, reject) => {
      s3.upload(params, {}, (error, data) => {
        console.log(error);
        if (error) {
          reject(error);
          return;
        }
        console.log(data);
        resolve(data);
      });
    });
  } catch (error) {
    console.log(error);
    return {
      status: "fail",
      error,
    };
  }
};

export function generateSignedURL(bucket, key, expiresIn = 86400) {
  const s3 = new AWS.S3({
    credentials: {
      region: process.env.AWS_REGION,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_ACCESS_KEY_ID_SECRET,
    },
  });
  const params = {
    Bucket: bucket,
    Key: key,
    Expires: expiresIn, // in seconds
  };

  return s3.getSignedUrlPromise("getObject", params);
}

export const extractKeyFromURL = (url) => {
  if (!url) {
    return undefined;
  }

  const urlObject = new URL(url);
  return urlObject.pathname.slice(1);
};

export const generateToken = (payload) => {
  return jwt.sign(
    {
      ...payload,
    },
    process.env.JWT_TOKEN_SECRET,
    {
      expiresIn: "1d",
    }
  );
};

export const validateAndCreateInventory = async (data) => {
  // Array to hold the created inventory items
  const createdInventoryItems = [];

  // Loop through each item in the data array
  for (const item of data) {
    const { Title, ProductCode, Quantity, FOB, Image } = item;

    // Check if Title and Quantity are present and valid
    if (!Title || !Quantity || Quantity <= 0) {
      // Skip if Title or Quantity is invalid
      console.log(
        `Missing or invalid Title/Quantity for product with code: ${ProductCode}`
      );
      continue;
    }

    // Check if the ProductCode or Title already exists in the inventory
    const existingInventory = await Inventory.findOne({
      $or: [{ sku: ProductCode }, { sku: Title }],
    });

    if (existingInventory) {
      // If inventory exists, do nothing and continue
      console.log(
        `Product with code/title ${ProductCode} already exists in inventory.`
      );
      continue;
    }

    // Prepare the payload for creating a new inventory item
    const lineItemPayload = {
      shopifyVariantId: "n/a",
      shopifyProductId: "n/a",
      productTitle: Title,
      variantTitle: Title,
      inventoryType: "CUSTOM",
      description: `Inventory for ${Title}`,
      price: FOB || 0,
      sku: ProductCode
        ? ProductCode
        : `${Title.split(" ").join("_")}_${Math.floor(Math.random() * 100000)}`, // SKU logic
      quantity: 0,
      image: Image
        ? Image
        : "https://cdn.shopify.com/s/files/1/0656/0969/5370/files/Titan-Watches-Logo.jpg?v=1725868130",
    };

    // Create the new inventory item
    const customInventory = await Inventory.create(lineItemPayload);

    // Update quantity after creating the inventory
    customInventory.quantity = Quantity;
    await customInventory.save(); // Save the updated quantity

    // Add the newly created inventory to the array
    createdInventoryItems.push(customInventory);
  }

  // Return the array of all created inventory items
  return createdInventoryItems;
};

export const validateSkusSubsetUtility = (requestedItems, existingItems) => {
  try {
    const sheetSkus = requestedItems.map((item) => item.Sku);

    const existingSkusInDB = existingItems.map((item) => item.sku);

    const providedSkusNonExistantInDB = sheetSkus.filter(
      (sku) => !existingSkusInDB.includes(sku)
    );

    if (providedSkusNonExistantInDB.length > 0) {
      return {
        valid: false,
        error: `Some provided Sku's do not exist for the given order/shipment.Skus:${providedSkusNonExistantInDB.join()}`,
      };
    }
    return { valid: true };
  } catch (error) {
    return {
      valid: false,
      error: "Error while validating sheet.",
    };
  }
};
