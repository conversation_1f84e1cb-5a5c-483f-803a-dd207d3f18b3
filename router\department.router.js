import express from "express";
import {
  getDepartments,
  createDepartment,
  getOneDepartment,
  updateDepartment,
  deleteDepartment,
  loginDepartmentPeople,
  hashPassword,
  findPeopleWithKey,
  createDepartmentFromExcel,
  getSalesPerson,
} from "../controller/department.controller.js";
import multer from "multer";

const upload = multer({ dest: "uploads/" });

const router = express.Router();

router.route("/all/salesperson").get(getSalesPerson);

router.route("/login").post(loginDepartmentPeople);

router.route("/").get(getDepartments).post(createDepartment);
router.route("/salesperson/:key").get(findPeopleWithKey);

router
  .route("/:id")
  .get(getOneDepartment)
  .patch(hashPassword, updateDepartment)
  .delete(deleteDepartment);

router.post("/upload", upload.single("file"), createDepartmentFromExcel);

export default router;
