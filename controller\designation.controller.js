import Designation from '../model/designation.model.js';
import catchAsync from '../utils/catchAsync.js';
import { createOne, deleteOne, getAll, getOne, updateOne } from '../utils/controllerFactory.js';


export const getDesignations = getAll(Designation);
export const getOneDesignation = getOne(Designation);
export const createDesignation = createOne(Designation);
export const updateDesignation = updateOne(Designation);
export const deleteDesignation = deleteOne(Designation);

export const checkExistingDesignation = async (req, res, next) => {
  const isCountryManager = req.body.isCountryManager;
  if (isCountryManager) {
    await Designation.updateMany({}, { isCountryManager: false });
  }
  next();
};

export const updateDesignationHierarchy = catchAsync(async (req, res, next) => {
  const designationData = req.body.designations;
  const bulkOps = designationData.map(update => ({
    updateOne: {
      filter: { _id: (update._id) },
      update: { $set: { hierarchy: update.hierarchy } }
    }
  }));

  const result = await Designation.bulkWrite(bulkOps);
  res.status(200).json({
    status: 'success',
    // matchedCount: result.matchedCount,
    // modifiedCount: result.modifiedCount
    data: result
  });
});