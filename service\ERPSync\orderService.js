import axios from "axios";
import mongoose from "mongoose";
import {
  GET_SHOPIFY_ORDER,
  REFUND_CREATE,
} from "../../queries/internalQueries.js";
import Distributor from "../../model/distributor.model.js";
import Department from "../../model/department.model.js";
import ProductMap from "../../model/erp.productmap.model.js";
import {
  DUMMY_ORDER_SHOPIFY,
  DUMMY_SHIPMENT,
  DUMMY_PRE_SYNC,
} from "../../constants/erpConstants.js";
import { saveFailedProduct } from "../ERPSync/productScheduledService.js";
import Shipment from "../../model/shipment.model.js";
import Order from "../../model/order.model.js";
import Status from "../../model/status.model.js";
import InitialOrderFulfillment from "../../model/erp.initial.order.model.js";
import {
  getOrderFinancialStatusService,
  orderMarkAsPaidService,
} from "../../service/orderService.js";
import NewAppError from "../../utils/newAppError.js";
import EmailNotification from "../../model/emailNotification.model.js";
import {
  getOrderFulfillments,
  createFulfillment,
} from "../../controller/shopify.controller.js";

const getShopifyOrder = async (orderId) => {
  try {
    let data = JSON.stringify({
      query: GET_SHOPIFY_ORDER,
      variables: { orderId: orderId },
    });
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/graphql.json`,
      headers: {
        "x-shopify-access-token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
        "Content-Type": API_CONTENT_TYPE,
      },
      data: data,
    };

    const responseData = await axios.request(config);
    if (!responseData?.data?.data?.order || responseData?.data?.data?.errors) {
      return {
        status: 404,
        code: "ERROR",
        message: `Error while fetching order from Shopify.`,
      };
    }
    const order = responseData?.data?.data?.order;
    const response = { status: 200, data: order };
    return response;
  } catch (error) {
    throw error;
  }
};

const getcreatedOrderDataHandler = async (orderId) => {
  try {
    console.log(`Looking for order with ID: ${orderId}`);

    const order = await Order.findById({ _id: orderId }).populate(
      "distributor"
    );

    if (!order) {
      console.log(`No order found with ID: ${orderId}`);

      return null;
    }
    return order;
  } catch (error) {
    console.error("Error in getcreatedOrderDataHandler:", error);
    return null;
  }
};

const formatOrderForERP = async (order) => {
  try {
    if (!order) {
      throw Error(
        `Required details to create ERPorder not found. Order not found.`
      );
    }

    const isFuture = order.isFuture;

    const distributor = await Distributor.findOne({
      _id: order.distributor._id,
    });

    if (!distributor) {
      throw Error(
        `Required details to create ERPorder not found. Distributor not found for ID: ${order.distributor}`
      );
    }

    const { customerNumber, salespersonId } = distributor;

    const lineItems = order.line_items;
    const name = order.subOrderId;

    if (!lineItems || !lineItems.length || !customerNumber) {
      throw Error(
        `Required details to create ERPorder not found. LineItems: ${
          lineItems?.length || 0
        }, customerNumber: ${customerNumber}, salespersonId: ${salespersonId}`
      );
    }

    const salesPerson = await Department.findById({ _id: salespersonId });

    const salesPersonKey = salesPerson?.salespersonkey || salesPerson?.name;

    if (!salesPersonKey) {
      throw Error(
        `Required details to create ERPorder not found. Sales person not found`
      );
    }

    const lineItemsArr = lineItems.map((item) => {
      const location = item?.locationCode;

      // Validate that location code is not undefined/null
      if (!location || location === "undefined") {
        throw new Error(
          `Location code is missing for SKU ${item?.sku}. Please ensure location codes are properly saved in the database.`
        );
      }

      return {
        Item: item?.sku,
        QuantityOrdered: item?.fulfilled || item?.quantity,
        Location: location,
      };
    });

    const response = {
      customerNumber,
      salesPersonKey,
      lineItemsArr,
      name,
      isFuture,
    };

    return response;
  } catch (error) {
    console.log(error.message, "Create ERP Order. Error in Format Shipment.");
    // throw error;
    return null;
  }
};

const createERPOrderService = async (orderData) => {
  const { customerNumber, salesPersonKey, lineItemsArr, name, isFuture } =
    orderData;
  try {
    let data = JSON.stringify({
      ShipToLocationCode: "",
      CustomerNumber: customerNumber || "",
      OrderType: isFuture ? "Future" : "Active",
      OrderDescription: orderData?.Description || "This is a new order.",
      OrderReference: name || "",
      OrderDiscountPercentage: orderData?.discount || 0,
      Salesperson1: salesPersonKey || "",
      TaxAuthority1: "MYSST",
      TaxClass1: 7,
      OrderComment: orderData?.comment || "",
      OrderDetails: lineItemsArr,
      ShipmentTrackingNumber: orderData.name,
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SAGE_API_ENDPOINT}/OE/OEOrders`,
      headers: {
        "Content-Type": `${process.env.API_CONTENT_TYPE}`,
        Accept: `${process.env.API_CONTENT_TYPE}`,
        Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
      },
      data: data,
    };

    const response = await axios.request(config);

    const {
      OrderUniquifier,
      OrderNumber,
      CustomerNumber,
      OrderCompleted,
      SalespersonName1,
    } = response.data;

    const responseData = {
      data: {
        OrderUniquifier,
        OrderNumber,
        CustomerNumber,
        OrderCompleted,
        SalespersonName1,
      },
    };

    return responseData;
  } catch (error) {
    console.log(error);
    throw Error(
      `Error when creating order on ERP.Message:${
        error?.response?.data?.error?.message?.value || error.message
      }`
    );
  }
};

const getERPOrders = async (filterKey = null) => {
  const orders = [];
  let failureFlag = false;
  try {
    const select =
      "$select=OrderUniquifier,OrderNumber,OrderCompleted,OnHold,OrderDetails&$count=true";
    let filter = "";

    if (filterKey === "shipped") {
      filter =
        "$filter=OrderCompleted eq 'CompleteNotIncluded' or OrderCompleted eq 'CompleteIncluded'";
    } else if (filterKey === "cancelled") {
      filter = "$filter=OnHold eq true";
    }

    let skipValue = 0;

    let baseURL = `${process.env.SAGE_API_ENDPOINT}/OE/OEOrders?${select}${
      filter ? `&${filter}` : ""
    }`;
    let nextLink;
    while (true) {
      nextLink = `${baseURL}&$skip=${skipValue}`;
      let config = {
        method: "get",
        maxBodyLength: Infinity,
        url: nextLink,
        headers: {
          "Content-Type": `${process.env.API_CONTENT_TYPE}`,
          Accept: `${process.env.API_CONTENT_TYPE}`,
          Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
        },
      };

      let response;
      try {
        response = await axios.request(config);
      } catch (error) {
        //log
        const errorLog = {
          errorLocation: "getERPOrders Function.",
          message: `Error fetching ERP orders.Message:${error.message}.Link:${nextLink}`,
        };
        console.error("Error fetching ERP orders:", errorLog);
        failureFlag = true;
        break;
      }

      const items = response?.data?.value || [];

      console.log(`Retrieved ${items.length} orders in the current batch.`);

      orders.push(...items);

      const nextLinkURL = response.data["@odata.nextLink"];
      if (!nextLinkURL) {
        console.log("No more data to fetch. Stopping the loop.");
        break;
      }

      const urlParams = new URLSearchParams(nextLinkURL.split("?")[1]);
      skipValue = urlParams.get("$skip") || skipValue;
    }
    if (failureFlag) {
      return null;
    }
    return orders;
  } catch (error) {
    const errorLog = {
      errorLocation: "getERPOrders Function.",
      message: `Error fetching ERP orders.Message:${error.message}`,
    };
    console.error("Error fetching orders:", errorLog);

    //log
    return null;
  }
};

const getShopifyLocationService = async () => {
  try {
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/locations.json`,
      headers: {
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
        "Content-Type": "application/json",
      },
    };

    const response = await axios.request(config);

    const locationsOnShopify = response.data.locations;
    return locationsOnShopify;
  } catch (error) {
    throw error;
  }
};

const shippedOrderSync = async () => {
  try {
    const responseObject = { matchingShipment: [], preSyncOrders: [] };

    //Hardcoded value passed for status needs to be dynamic.
    const processedOrders = await processErpOrdersForSync(
      "shipped",
      "673afb29907394a7758ae393"
    );

    const segregatedERPOrders = await findShipmentsWithErpIdentifiers(
      processedOrders.erpOrders,
      processedOrders.statusAlignedId
    );
    if (!segregatedERPOrders) {
      return;
    }

    if (segregatedERPOrders.matchedShipments.length > 0) {
      const fulfillMatchingShipments = await fulfillMatchingShipmentsService(
        segregatedERPOrders.matchedShipments
      );
      if (fulfillMatchingShipments && fulfillMatchingShipments.length > 0) {
        responseObject.matchingShipment = [...fulfillMatchingShipments];
      }
    }

    if (segregatedERPOrders.preSyncOrders.length > 0) {
      const fulfillPreSyncOrders = await fulfillPreSyncOrdersService(
        segregatedERPOrders.preSyncOrders
      );
      if (fulfillPreSyncOrders && fulfillPreSyncOrders.length > 0) {
        responseObject.preSyncOrders = [...fulfillPreSyncOrders];

        const saveInDb = await InitialOrderFulfillment.insertMany(
          responseObject.preSyncOrders,
          {
            ordered: false,
          }
        );
      }
    }
    return responseObject;
  } catch (error) {
    const errorLog = {
      errorLocation: "shippedOrderSync function",
      message: error.message,
    };
    console.log("error Message in shipped order Sync.", errorLog);
  }
};

const findShipmentsWithErpIdentifiers = async (erpOrders, statusId) => {
  try {
    const erpOrdersUniquifier = erpOrders.map((order) => order.OrderUniquifier);
    let combinedMatchedShipments = [];
    let matchedErpIdentifiers = [];
    //match shipments with ERP Orders
    const matchedShipments = await Shipment.find({
      erpIdentifier: { $in: erpOrdersUniquifier },
      status: statusId,
    });

    if (matchedShipments.length > 0) {
      combinedMatchedShipments = matchedShipments.map((shipment) => {
        const correspondingOrder = erpOrders.find(
          (order) => order.OrderUniquifier === Number(shipment.erpIdentifier)
        );
        return {
          ...shipment,
          erpOrderData: correspondingOrder,
        };
      });

      //for preSyncOrders
      matchedErpIdentifiers = matchedShipments.map(
        (shipment) => shipment.erpIdentifier
      );
    }

    const preSyncOrders = erpOrders.filter(
      (order) => !matchedErpIdentifiers.includes(order.OrderUniquifier)
    );

    return {
      matchedShipments: combinedMatchedShipments,
      preSyncOrders,
    };
  } catch (error) {
    const errorLog = {
      errorLocation: "findShipmentsWithErpIdentifiers function.",
      message: `Error finding shipments with ERP identifiers:
      ${error.message}`,
      data: {},
    };
    console.error(errorLog);
    return null;
  }
};

const fulfillMatchingShipmentsService = async (shipments = []) => {
  try {
    let fulfilledShipments = [];
    // ObjectId for status Shipped.Used if fulilled successfully.Status Id hardcoded needs to be dynamic.
    const newStatusId = new mongoose.Types.ObjectId("674ef3ffce77438d64f79cbf");
    for (const shipment of shipments) {
      const dbOrder = await Order.findById(shipment.order).select("order_id");

      const shopifyOrderId = dbOrder.order_id;

      const getOrderFulfillments = await getOrderFulfillmentsService(
        shopifyOrderId
      );
      if (!getOrderFulfillments) {
        continue;
      }

      const processedFulfillments = processMatchedERPFulfillments(
        getOrderFulfillments,
        shipment
      );
      if (!processedFulfillments) {
        continue;
      }

      if (processedFulfillments.length > 0) {
        let fulfillmentIds = [];
        for (const fulfillment of processedFulfillments) {
          const { fulfillment_order_id, line_items } = fulfillment;

          const fulfillmentResponse = await fulfillLineItemsService(
            fulfillment_order_id,
            line_items
          );
          if (fulfillmentResponse.fulfilled) {
            const updatedShipment = await Shipment.findByIdAndUpdate(
              shipment._id.toString(),
              { status: newStatusId },
              { new: true }
            );
            fulfillmentIds.push(fulfillmentResponse.data.fulfillmentId);
          }
        }
        if (fulfillmentIds.length > 0) {
          fulfilledShipments.push({
            shipment_id: shipment._id.toString(),
            fulfillmentIds,
          });
        }
      }
    }

    return fulfilledShipments;
  } catch (error) {
    const errorLog = {
      errorLocation: "error in fulfillMatchingShipmentsService function.",
      message: error?.message,
      data: {},
    };
    console.log(errorLog);
    return null;
  }
};

const processMatchedERPFulfillments = (fulfillmentData, matchedShipment) => {
  try {
    const results = [];

    //As fulfilments can be multiple.
    for (const fulfillment of fulfillmentData) {
      const fulfillmentOrderId = fulfillment.id;

      const lineItems = [];

      for (const lineItem of fulfillment.line_items) {
        //quantity will be from shipment database fulfilled .
        const matchedItem = matchedShipment.lineItems.find(
          (shipment) =>
            shipment.shopifyVariantId === lineItem.variant_id.toString()
        );

        if (matchedItem) {
          lineItems.push({
            id: lineItem.id,
            quantity: matchedItem.fulfilled,
          });
        }
      }

      if (lineItems.length > 0) {
        results.push({
          fulfillment_order_id: fulfillmentOrderId,
          line_items: lineItems,
        });
      }
    }

    return results;
  } catch (error) {
    const errorLog = {
      errorLocation: "processMatchedERPFulfillments function.",
      message: `Could not process matched ERP fulfillments.${error.message}`,
      data: { shipmentId: matchedShipment._id },
    };
    console.log(errorLog);
    return null;
  }
};

const fulfillPreSyncOrdersService = async (preSyncOrders = []) => {
  try {
    let fulfilledERPOrder = [];
    // const dummyPreSync = DUMMY_PRE_SYNC;

    //Passing initial orders hardcoded value.Will be changed ,we can add a meta to initial orders and then get shopify order with that meta.
    const getInitialOrderFulfillments = await getOrderFulfillmentsService(
      "5441594359908"
    );
    if (!getInitialOrderFulfillments) {
      return null;
    }
    let fulfilledOrders = [];
    for (const order of preSyncOrders) {
      //if items shipped is zero fulfill api will give error.So skipping that order.
      const orderItemsShipped = order.OrderDetails.every(
        (item) => item.QuantityShipped > 0
      );
      if (orderItemsShipped) {
        //to get items in format that can be fulfilled.
        const processedFulfillments = await processPreSyncOrders(
          getInitialOrderFulfillments,
          order
        );

        if (processedFulfillments && processedFulfillments.length > 0) {
          let fulfillmentIds = [];
          for (const fulfillment of processedFulfillments) {
            const { fulfillment_order_id, line_items } = fulfillment;

            const fulfillmentResponse = await fulfillLineItemsService(
              fulfillment_order_id,
              line_items
            );
            if (fulfillmentResponse.fulfilled) {
              fulfillmentIds.push(fulfillmentResponse.data.fulfillmentId);
            }
          }
          if (fulfillmentIds.length > 0) {
            fulfilledERPOrder.push({
              orderUniquifier: order.OrderUniquifier,
              ids: fulfillmentIds,
              idType: "fulfillmentIds",
            });
          }
        }
      }
    }

    return fulfilledERPOrder;
  } catch (error) {
    const errorLog = {
      errorLocation: `fulfillPreSyncOrdersService function`,
      message: error?.message,
      data: {},
    };
    console.log(errorLog);
    return null;
  }
};

const processPreSyncOrders = async (shopifyFulfillmentOrderObject, order) => {
  try {
    const results = [];

    const erpOrderlineItemDetails = order.OrderDetails.map((item) => ({
      unformattedItemNumber: item.UnformattedItemNumber,
      quantityShipped: item.QuantityShipped,
    }));

    const unformattedItemNumbers = erpOrderlineItemDetails.map(
      (item) => item.unformattedItemNumber
    );
    const productMapVariants = await ProductMap.aggregate([
      { $unwind: "$variants" },
      { $match: { "variants.sku": { $in: unformattedItemNumbers } } },
      {
        $project: {
          sku: "$variants.sku",
          variantId: "$variants.variantId",
        },
      },
    ]);

    const skuToVariantMap = productMapVariants.reduce((map, item) => {
      map[item.sku] = item.variantId;
      return map;
    }, {});

    for (const fulfillment of shopifyFulfillmentOrderObject) {
      const fulfillmentOrderId = fulfillment.id;
      const lineItems = [];

      for (const lineItem of fulfillment.line_items) {
        const erpItem = erpOrderlineItemDetails.find((item) => {
          return (
            skuToVariantMap[item.unformattedItemNumber] ===
            lineItem.variant_id.toString()
          );
        });

        if (erpItem) {
          lineItems.push({
            id: lineItem.id,
            quantity: erpItem.quantityShipped,
          });
        }
      }

      if (lineItems.length > 0) {
        results.push({
          fulfillment_order_id: fulfillmentOrderId,
          line_items: lineItems,
        });
      }
    }

    return results;
  } catch (error) {
    const errorLog = {
      errorLocation: `processPreSyncOrders function.`,
      message: error?.message,
      data: { erpIdentifier: order.OrderUniquifier },
    };
    console.error(errorLog);
    return null;
  }
};

const getOrderFulfillmentsService = async (orderId) => {
  try {
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/orders/${orderId}/fulfillment_orders.json`,
      headers: {
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      },
    };

    const response = await axios.request(config);

    const fulfilmentOrders = response.data.fulfillment_orders;
    if (fulfilmentOrders.length <= 0) {
      const errorlog = {
        errorLocation: "getOrderFulfillmentsService function.",
        message: `Order not found.`,
        data: { orderId },
      };
      console.log(errorlog, "errorlog");
      return null;
    }
    return fulfilmentOrders;
  } catch (error) {
    const errorlog = {
      errorLocation: "getOrderFulfillmentsService function.",
      message: `Could not fetch order fulfillents.Message:${error.message}.`,
      data: { orderId },
    };
    console.log(errorlog, "errorlog");
    return null;
  }
};

const fulfillLineItemsService = async (
  fulfillment_order_id,
  fulfillment_order_line_items
) => {
  try {
    let data = JSON.stringify({
      fulfillment: {
        message: "The package is accepted by the Vendor.",
        notify_customer: false,
        line_items_by_fulfillment_order: [
          {
            fulfillment_order_id: fulfillment_order_id,
            fulfillment_order_line_items: fulfillment_order_line_items,
          },
        ],
      },
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/fulfillments.json`,
      headers: {
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
        "Content-Type": "application/json",
      },
      data: data,
    };

    const response = await axios.request(config);

    if (response.status !== 201) {
      return {
        fulfilled: false,
      };
    }

    const lineItemsIds = response.data.fulfillment.line_items.map(
      (lineItem) => lineItem.id
    );
    return {
      fulfilled: true,
      data: { fulfillmentId: response.data.fulfillment.id, lineItemsIds },
    };
  } catch (error) {
    const errorLog = {
      errorLocation: `fulfillLineItemsService function`,
      message: `Error fulfilling lineItems.fulfillment_order_id:${fulfillment_order_id}.Message:${error?.message}`,
    };
    return {
      fulfilled: false,
    };
  }
};

const cancelOrderSync = async () => {
  try {
    //Cancelled ERP orders associated liteItems will be refunded here to sync inventory.
    const responseObject = { matchingShipment: [], preSyncOrders: [] };
    //second argument is statusId for Awaiting courior full status.As only these orders created on ERP.
    //Hardcoded value passed for status needs to be dynamic.
    const processedOrders = await processErpOrdersForSync(
      "cancelled",
      "673afb29907394a7758ae393"
    );
    if (!processedOrders) {
      return;
    }

    const segregatedERPOrders = await findShipmentsWithErpIdentifiers(
      processedOrders.erpOrders,
      processedOrders.statusAlignedId
    );
    if (!segregatedERPOrders) {
      return;
    }

    if (segregatedERPOrders.matchedShipments.length > 0) {
      const refundMatchingShipments = await refundMatchingShipmentsService(
        segregatedERPOrders.matchedShipments
      );
      if (refundMatchingShipments && refundMatchingShipments.length > 0) {
        responseObject.matchingShipment = [...refundMatchingShipments];
      }
    }

    if (segregatedERPOrders.preSyncOrders.length > 0) {
      const refundPreSyncOrders = await refundPreSyncOrdersService(
        segregatedERPOrders.preSyncOrders
      );

      if (refundPreSyncOrders && refundPreSyncOrders.length > 0) {
        responseObject.preSyncOrders = [...refundPreSyncOrders];

        const saveInDb = await InitialOrderFulfillment.insertMany(
          responseObject.preSyncOrders,
          {
            ordered: false,
          }
        );
      }
    }

    return responseObject;
  } catch (error) {
    const errorLog = {
      errorLocation: "cancelOrderSync function",
      message: error.message,
    };
    console.log("error Message in cancel order Sync.", errorLog);
  }
};

const refundMatchingShipmentsService = async (shipments = []) => {
  try {
    let refundedShipments = [];
    // ObjectId for status Shipped.Used if fulilled successfully.Hardcoded value used needs to be dynamic.
    const newStatusId = new mongoose.Types.ObjectId("674ef3d2ce77438d64f79cbe");
    for (const shipment of shipments) {
      const dbOrder = await Order.findById(shipment.order).select("order_id");

      const shopifyOrderId = dbOrder.order_id;

      //getorderFinantialStatus if not paid set to mark as paid.
      const orderFinancialStatus = await getOrderFinancialStatusService(
        shopifyOrderId
      );
      if (orderFinancialStatus instanceof NewAppError) {
        const errorLog = {
          errorLocation: "error in refundMatchingShipmentsService function.",
          message: "error while getting financial status of order",
          data: { orderFinancialStatus },
        };
        continue;
      }
      if (orderFinancialStatus.orderFinancialStatus !== "PAID") {
        //marking as paid as order was created with pending payment,to refund items we need paid items
        const markOrderAsPaid = await orderMarkAsPaidService(shopifyOrderId);
        if (markOrderAsPaid instanceof NewAppError) {
          const errorLog = {
            errorLocation: "error in refundMatchingShipmentsService function.",
            message: "error while marking order as paid.",
            data: { markOrderAsPaid },
          };
          continue;
        }
      }

      const getOrderFulfillments = await getOrderFulfillmentsService(
        shopifyOrderId
      );
      if (!getOrderFulfillments) {
        continue;
      }

      const processedFulfillments =
        await processMatchedERPFulfillmentsforRefund(
          getOrderFulfillments,
          shipment,
          "cancelFulfilled"
        );

      if (processedFulfillments && processedFulfillments.length > 0) {
        let refundIds = [];
        const batchSize = 250; // Max items per request
        const totalbatches = Math.ceil(
          processedFulfillments.length / batchSize
        );

        for (let i = 0; i < totalbatches; i++) {
          const batch = processedFulfillments.slice(
            i * batchSize,
            (i + 1) * batchSize
          );

          for (const fulfillment of batch) {
            const { orderId, refundLineItems } = fulfillment;

            const refundResponse = await refundLineItemsService(
              orderId,
              refundLineItems
            );

            if (refundResponse.refunded) {
              refundIds.push(refundResponse.data.refundId);
            }
          }
        }
        if (refundIds.length > 0) {
          refundedShipments.push({
            shipment_id: shipment._id.toString(),
            refundIds,
          });
        }
        const updatedShipment = await Shipment.findByIdAndUpdate(
          shipment._id.toString(),
          { status: newStatusId },
          { new: true }
        );
      }
    }

    return refundedShipments;
  } catch (error) {
    const errorLog = {
      errorLocation: "error in refundMatchingShipmentsService function.",
      message: error?.message,
      data: {},
    };
    console.log(errorLog);
    return null;
  }
};

const refundPreSyncOrdersService = async (preSyncOrders = []) => {
  try {
    let refundedERPOrder = [];
    // const dummyPreSync = DUMMY_PRE_SYNC;

    //Passing initial orders hardcoded value.Will be changed ,we can add a meta to initial orders and then get shopify order with that meta.
    const getInitialOrderFulfillments = await getOrderFulfillmentsService(
      "5493012791396"
    );
    if (!getInitialOrderFulfillments) {
      return null;
    }
    let fulfilledOrders = [];
    for (const order of preSyncOrders) {
      //if items shipped is zero fulfill api will give error.So skipping that order.
      const orderItemsOrdered = order.OrderDetails.every(
        (item) => item.QuantityOrdered > 0
      );
      if (orderItemsOrdered) {
        //to get items in format that can be fulfilled.
        const processedFulfillments = await processPreSyncOrdersForRefund(
          getInitialOrderFulfillments,
          order
        );

        if (processedFulfillments && processedFulfillments.length > 0) {
          let refundIds = [];
          const batchSize = 250; // Max items per request
          const totalbatches = Math.ceil(
            processedFulfillments.length / batchSize
          );

          for (let i = 0; i < totalbatches; i++) {
            const batch = processedFulfillments.slice(
              i * batchSize,
              (i + 1) * batchSize
            );

            for (const fulfillment of batch) {
              const { orderId, refundLineItems } = fulfillment;

              const refundResponse = await refundLineItemsService(
                orderId,
                refundLineItems
              );

              if (refundResponse.refunded) {
                refundIds.push(refundResponse.data.refundId);
              }
            }
          }
          if (refundIds.length > 0) {
            refundedERPOrder.push({
              orderUniquifier: order.OrderUniquifier,
              ids: refundIds,
              idType: "refundIds",
            });
          }
        }
      }
    }

    return refundedERPOrder;
  } catch (error) {
    const errorLog = {
      errorLocation: `refundPreSyncOrdersService function`,
      message: error?.message,
      data: {},
    };
    console.log(errorLog);
    return null;
  }
};

const refundLineItemsService = async (
  order_id,
  fulfillment_order_line_items
) => {
  try {
    let data = JSON.stringify({
      query: REFUND_CREATE,
      variables: {
        input: {
          orderId: order_id,
          refundLineItems: fulfillment_order_line_items,
          transactions: [],
        },
      },
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/${process.env.VERSION}/graphql.json`,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
      },
      data: data,
    };

    const response = await axios.request(config);

    if (response?.data?.data?.refundCreate?.userErrors?.length > 0) {
      const errorLog = {
        errorLocation: `refundLineItems function`,
        message: `User error refunding lineItems.order_id:${order_id}.`,
        data: { error: response.data.data.refundCreate.userErrors },
      };

      console.log(errorLog);
      return {
        refunded: false,
      };
    } else if (response?.data?.errors?.length > 0) {
      const errorLog = {
        errorLocation: `refundLineItems function`,
        message: `Error refunding lineItems.order_id:${order_id}.`,
        data: JSON.stringify({ error: response.data.errors }),
      };

      console.log(errorLog);
      return {
        refunded: false,
      };
    }

    const lineItemsIds =
      response.data.data.refundCreate.refund.refundLineItems.edges.map(
        (lineItem) => lineItem.node.id
      );
    return {
      refunded: true,
      data: {
        refundId: response.data.data.refundCreate.refund.id,
        lineItemsIds,
      },
    };
  } catch (error) {
    const errorLog = {
      errorLocation: `refundLineItems function`,
      message: `Error refunding lineItems.order_id:${order_id}.Message:${error?.message}`,
      data: {},
    };
    console.log(errorLog);
    return {
      refunded: false,
    };
  }
};

const processMatchedERPFulfillmentsforRefund = async (
  fulfillmentData,
  matchedShipment,
  eventType
) => {
  try {
    const results = [];

    //As fulfilments can be multiple.
    for (const fulfillment of fulfillmentData) {
      const orderId = fulfillment.order_id;

      const lineItems = [];

      for (const lineItem of fulfillment.line_items) {
        //quantity will be from shipment database fulfilled .
        const matchedItem = matchedShipment.lineItems.find(
          (shipment) =>
            shipment.shopifyVariantId === lineItem.variant_id.toString()
        );

        if (matchedItem) {
          const quantityToPass =
            eventType === "cancelFulfilled"
              ? matchedItem.fulfilled
              : matchedItem.requested;
          lineItems.push({
            lineItemId: `gid://shopify/LineItem/${lineItem.line_item_id}`,

            quantity: quantityToPass,
          });
        }
      }

      if (lineItems.length > 0) {
        results.push({
          orderId: `gid://shopify/Order/${orderId}`,
          refundLineItems: lineItems,
        });
      }
    }

    return results;
  } catch (error) {
    const errorLog = {
      errorLocation: "processMatchedERPFulfillmentsforRefund function.",
      message: `Could not process matched ERP fulfillments.${error.message}`,
      data: { shipmentId: matchedShipment._id },
    };
    console.log(errorLog);
    return null;
  }
};

const processPreSyncOrdersForRefund = async (
  shopifyFulfillmentOrderObject,
  order
) => {
  try {
    const results = [];
    // taking QuantityOrdered not QuantityShipped
    const erpOrderlineItemDetails = order.OrderDetails.map((item) => ({
      unformattedItemNumber: item.UnformattedItemNumber,
      quantityOrdered: item.QuantityOrdered,
    }));

    const unformattedItemNumbers = erpOrderlineItemDetails.map(
      (item) => item.unformattedItemNumber
    );

    //done for location and variantId
    const productMapVariants = await ProductMap.aggregate([
      { $unwind: "$variants" },
      { $match: { "variants.sku": { $in: unformattedItemNumbers } } },
      {
        $project: {
          sku: "$variants.sku",
          variantId: "$variants.variantId",
        },
      },
    ]);

    const skuToVariantMap = productMapVariants.reduce((map, item) => {
      map[item.sku] = item.variantId;
      return map;
    }, {});

    for (const fulfillment of shopifyFulfillmentOrderObject) {
      const orderId = fulfillment.order_id;
      const lineItems = [];

      for (const lineItem of fulfillment.line_items) {
        const erpItem = erpOrderlineItemDetails.find((item) => {
          return (
            skuToVariantMap[item.unformattedItemNumber] ===
            lineItem.variant_id.toString()
          );
        });

        if (erpItem) {
          lineItems.push({
            lineItemId: `gid://shopify/LineItem/${lineItem.line_item_id}`,

            quantity: erpItem.quantityOrdered,
          });
        }
      }

      if (lineItems.length > 0) {
        results.push({
          orderId: `gid://shopify/Order/${orderId}`,
          refundLineItems: lineItems,
        });
      }
    }

    return results;
  } catch (error) {
    const errorLog = {
      errorLocation: `processPreSyncOrdersForRefund function.`,
      message: error?.message,
      data: { erpIdentifier: order.OrderUniquifier },
    };
    console.error(errorLog);
    return null;
  }
};

const processErpOrdersForSync = async (event, statusId) => {
  try {
    if (!event || !statusId) {
      console.log("Required values to process ERP orders not provided.");
      return null;
    }
    let erpOrders = await getERPOrders(event);

    if (!erpOrders || erpOrders.length <= 0) {
      console.log("No shipped orders to fetch or some Error.");
      return null;
    }

    //filters already fulfilled preSync ERP orders.
    const orderUniquifiers = await InitialOrderFulfillment.distinct(
      "orderUniquifier"
    );

    if (orderUniquifiers.length > 0) {
      const filteredOrders = erpOrders.filter(
        (item) => !orderUniquifiers.includes(item.OrderUniquifier)
      );
      if (filteredOrders.length <= 0) {
        const errorLog = {
          errorLocation: "processERPOrdersForSync function",
          message: `No shipped orders to process.number of filteredOrders:${filteredOrders.length}`,
        };
        console.log(errorLog, "errorLog");

        return null;
      }
      erpOrders = filteredOrders;
    }
    //objectID of status fulfilled shipment.As only a fulfilled shipment can be shipped.
    const statusAlignedId = new mongoose.Types.ObjectId(statusId);
    return {
      erpOrders,
      statusAlignedId,
    };
  } catch (error) {
    const errorLog = {
      errorLocation: "processERPOrdersForSync function",
      message: error.message,
    };
    console.log(errorLog, "errorLog");
    return null;
  }
};

function chunkArray(arr, size) {
  const chunks = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
}

function getEmailTypeFromStatus(sageStatus) {
  const statusEmailMap = {
    Invoiced: "ORDER_INVOICED",
    "Out for Delivery": "ORDER_OUT_FOR_DELIVERY",
    Delivered: "ORDER_DELIVERED",
    Completed: "ORDER_DELIVERED",
    Cancelled: "ORDER_CANCELLED",
  };

  return statusEmailMap[sageStatus] || null;
}

async function createStatusChangeEmailNotification(
  order,
  emailType,
  sageStatus
) {
  try {
    if (!order.distributor) {
      console.log(`No distributor found for order ${order._id}`);
      return;
    }

    const salesPerson = await Department.findById(
      order.distributor.salespersonId
    );

    const recipients = [
      {
        name: order.distributor.name,
        email: order.distributor.email,
        cc: [],
      },
    ];

    const emailPayload = {
      orderId: order.name,
      customerName: order.distributor.name,
      date: new Date(),
      erpIdentifier: order.erpIdentifier,
      sageOrderNumber: order.sageOrderNumber,
      currentStatus: sageStatus,
      storeName: "Sunrise Trade",
      orderDate: order.createdAt,
      salesPersonEmail: salesPerson?.email,
      salesPersonName: salesPerson?.name,
      totalAmount:
        order.line_items
          ?.reduce(
            (total, item) =>
              total + parseFloat(item.price || 0) * (item.quantity || 0),
            0
          )
          .toFixed(2) || "0.00",
      cartProducts:
        order.line_items?.map((item) => ({
          sku: item.sku,
          title: item.title || item.name || item.productTitle,
          quantity: item.quantity,
          price: parseFloat(item.price || 0).toFixed(2),
          total: (parseFloat(item.price || 0) * (item.quantity || 0)).toFixed(
            2
          ),
        })) || [],
    };

    await EmailNotification.create({
      emailCategory: "ORDER",
      emailType: emailType,
      reciepient: recipients,
      emailPayload: emailPayload,
      isProcessed: false,
    });
  } catch (error) {
    console.error(
      `Failed to create email notification for order ${order._id}:`,
      error
    );
  }
}

async function updateERPStatusesInBatches() {
  try {
    const orders = await Order.find({
      erpIdentifier: { $ne: null },
      status: { $ne: "order completed" },
    }).lean();

    if (orders.length === 0) {
      console.log("No orders to update");
      return;
    }

    const erpIdList = orders.map((order) => order.erpIdentifier);
    const erpIdChunks = chunkArray(erpIdList, 500);

    let totalUpdated = 0;
    let completedOrders = [];

    for (const chunk of erpIdChunks) {
      const erpIds = chunk.join(",");
      const url = `${
        process.env.SAGE_CUSTOM_API_ENDPOINT
      }/api/OEOrder/GetOEOrderStatus?Company=${
        process.env.SAGE_COMPANY
      }&APIKey=${encodeURIComponent(
        process.env.SAGE_CUSTOM_API_KEY
      )}&OrdUniq=${erpIds}`;

      const response = await axios.get(url, {
        headers: { accept: "text/plain" },
      });

      const sageData = response.data;
      const bulkOps = [];
      const newlyCompletedOrders = [];
      const statusChangeNotifications = [];

      // Get ERP identifiers that were returned from Sage
      const sageErpIds = sageData.map((item) => item.OrdUniq);

      // Find orders that were sent to ERP but not found in Sage response
      const ordersInChunk = orders.filter((order) =>
        chunk.includes(order.erpIdentifier)
      );
      const missingFromSage = ordersInChunk.filter(
        (order) =>
          !sageErpIds.includes(order.erpIdentifier) &&
          order.status !== "order deleted"
      );

      // Mark missing orders as deleted
      for (const missingOrder of missingFromSage) {
        console.log(
          `Order ${missingOrder.erpIdentifier} not found in Sage response, marking as deleted`
        );

        bulkOps.push({
          updateOne: {
            filter: { erpIdentifier: missingOrder.erpIdentifier },
            update: {
              $set: {
                status: "order deleted",
              },
            },
          },
        });

        // Prepare email notification for deleted order
        statusChangeNotifications.push({
          erpIdentifier: missingOrder.erpIdentifier,
          status: "Cancelled",
          emailType: "ORDER_CANCELLED",
        });
      }

      // Process orders that were found in Sage response
      for (const item of sageData) {
        const dbOrder = orders.find(
          (order) => order.erpIdentifier === item.OrdUniq
        );

        if (!dbOrder) continue;

        const newStatus = item.Status.toLowerCase();
        const currentStatus = dbOrder.status?.toLowerCase();

        // Skip if status hasn't changed
        if (newStatus === currentStatus) continue;

        // Mark as completed if applicable
        if (item.Status === "Order Completed") {
          newlyCompletedOrders.push(item.OrdUniq);
        }

        // Prepare for email notification
        const emailType = getEmailTypeFromStatus(item.Status);
        if (emailType) {
          statusChangeNotifications.push({
            erpIdentifier: item.OrdUniq,
            status: item.Status,
            emailType: emailType,
          });
        }

        // Only push to bulk update if status changed
        bulkOps.push({
          updateOne: {
            filter: { erpIdentifier: item.OrdUniq },
            update: {
              $set: {
                status: newStatus,
                sageOrderNumber: item.OrderNumber,
              },
            },
          },
        });
      }

      if (bulkOps.length > 0) {
        const res = await Order.bulkWrite(bulkOps);
        totalUpdated += res.modifiedCount || 0;

        // Process email notifications for status changes
        if (statusChangeNotifications.length > 0) {
          console.log(
            `Processing ${statusChangeNotifications.length} email notifications for status changes`
          );

          for (const notification of statusChangeNotifications) {
            try {
              // Find the order with distributor details
              const order = await Order.findOne({
                erpIdentifier: notification.erpIdentifier,
              })
                .populate("distributor")
                .lean();

              if (order) {
                await createStatusChangeEmailNotification(
                  order,
                  notification.emailType,
                  notification.status
                );
              } else {
                console.log(
                  `Order not found for ERP ID: ${notification.erpIdentifier}`
                );
              }
            } catch (emailError) {
              console.error(
                `Failed to process email for ERP ID ${notification.erpIdentifier}:`,
                emailError
              );
            }
          }
        }
      }

      if (newlyCompletedOrders.length > 0) {
        const completedOrdersBatch = await Order.find({
          erpIdentifier: { $in: newlyCompletedOrders },
        }).lean();

        completedOrders.push(...completedOrdersBatch);
      }
    }

    if (completedOrders.length > 0) {
      for (const order of completedOrders) {
        try {
          const fulfillmentResult =
            await processOrderFulfillmentsWithPagination(order.order_id);

          if (fulfillmentResult.success) {
            console.log(
              `Successfully processed fulfillment for order ${order.order_id}:`,
              fulfillmentResult.message
            );
          } else {
            console.error(
              `Failed to process fulfillment for order ${order.order_id}:`,
              fulfillmentResult.message
            );
          }
        } catch (error) {
          console.error(
            `Error processing fulfillment for order ${order.order_id}:`,
            error.message
          );
        }
      }
    }
  } catch (err) {
    console.error("Error updating ERP statuses:", err.message || err);
  }
}

const processOrderFulfillmentsWithPagination = async (shopifyOrderId) => {
  try {
    const dbOrders = await Order.find(
      { order_id: shopifyOrderId },
      { line_items: 1, subOrderId: 1 }
    ).lean();

    if (!dbOrders || dbOrders.length === 0) {
      return { success: false, message: "Order not found in database" };
    }

    const allLineItems = [];
    dbOrders.forEach((order) => {
      if (order.line_items) {
        allLineItems.push(...order.line_items);
      }
    });

    const dbLineItemsMap = new Map();

    allLineItems.forEach((item) => {
      if (item.fulfilled > 0) {
        dbLineItemsMap.set(item.lineItemId, item);
      }
    });

    if (dbLineItemsMap.size === 0) {
      return {
        success: true,
        message: "No line items with fulfilled quantities to process",
        processed: 0,
      };
    }

    const matchedItems = [];
    let cursor = null;
    let hasNextPage = true;
    let pageCount = 0;

    while (hasNextPage && pageCount < 20) {
      pageCount++;

      const response = await getOrderFulfillments(
        `gid://shopify/Order/${shopifyOrderId}`,
        cursor
      );

      const fulfillmentOrdersData = response?.data?.order?.fulfillmentOrders;
      if (!fulfillmentOrdersData?.edges) {
        break;
      }

      for (const { node: fulfillmentOrder } of fulfillmentOrdersData.edges) {
        if (
          fulfillmentOrder?.status === "CLOSED" ||
          fulfillmentOrder?.status === "CANCELLED"
        ) {
          continue;
        }

        let lineItemCursor = null;
        let hasMoreLineItems = true;
        let lineItemPageCount = 0;

        while (hasMoreLineItems && lineItemPageCount < 10) {
          lineItemPageCount++;

          if (lineItemPageCount > 1) {
            const lineItemResponse = await getOrderFulfillments(
              `gid://shopify/Order/${shopifyOrderId}`,
              lineItemCursor
            );

            const updatedFulfillmentOrder =
              lineItemResponse?.data?.order?.fulfillmentOrders?.edges?.find(
                (edge) => edge.node.id === fulfillmentOrder.id
              )?.node;

            if (!updatedFulfillmentOrder) break;
            fulfillmentOrder.lineItems = updatedFulfillmentOrder.lineItems;
          }

          for (const { node: lineItem } of fulfillmentOrder.lineItems.edges) {
            if (lineItem.remainingQuantity <= 0) {
              continue;
            }

            const shopifyLineItemId = lineItem.lineItem.id.replace(
              "gid://shopify/LineItem/",
              ""
            );

            const dbLineItem = dbLineItemsMap.get(shopifyLineItemId);

            if (!dbLineItem) {
              continue;
            }

            const quantityToFulfill = Math.min(
              dbLineItem.fulfilled,
              lineItem.remainingQuantity
            );

            if (quantityToFulfill > 0) {
              matchedItems.push({
                fulfillmentOrderId: fulfillmentOrder.id,
                fulfillmentOrderLineItemId: lineItem.id,
                quantity: quantityToFulfill,
                dbLineItem: {
                  sku: dbLineItem.sku,
                  title: dbLineItem.productTitle || lineItem.lineItem.title,
                },
              });
            }
          }

          const lineItemPageInfo = fulfillmentOrder.lineItems.pageInfo;
          hasMoreLineItems = lineItemPageInfo.hasNextPage;
          lineItemCursor = lineItemPageInfo.endCursor;
        }
      }

      const mainPageInfo = fulfillmentOrdersData.pageInfo;
      hasNextPage = mainPageInfo?.hasNextPage || false;
      cursor = mainPageInfo?.endCursor;
    }

    if (matchedItems.length === 0) {
      return {
        success: true,
        message: "No matching line items found to fulfill",
        processed: 0,
      };
    }

    const fulfillmentGroups = matchedItems.reduce((groups, item) => {
      if (!groups[item.fulfillmentOrderId]) {
        groups[item.fulfillmentOrderId] = [];
      }
      groups[item.fulfillmentOrderId].push({
        id: item.fulfillmentOrderLineItemId,
        quantity: item.quantity,
      });
      return groups;
    }, {});

    const fulfillmentResults = await Promise.all(
      Object.entries(fulfillmentGroups).map(
        async ([fulfillmentOrderId, lineItems]) => {
          try {
            const fulfillmentInput = {
              lineItemsByFulfillmentOrder: {
                fulfillmentOrderId,
                fulfillmentOrderLineItems: lineItems,
              },
            };

            const fulfillmentResponse = await createFulfillment(
              fulfillmentInput
            );

            if (fulfillmentResponse?.errors) {
              return {
                fulfillmentOrderId,
                success: false,
                errors: fulfillmentResponse.errors.map((e) => e.message),
              };
            }

            const { fulfillment, userErrors } =
              fulfillmentResponse?.data?.fulfillmentCreate || {};

            if (userErrors?.length) {
              return {
                fulfillmentOrderId,
                success: false,
                errors: userErrors.map((e) => e.message),
              };
            }

            if (!fulfillment) {
              return {
                fulfillmentOrderId,
                success: false,
                errors: ["No fulfillment object returned from Shopify"],
              };
            }

            return {
              fulfillmentOrderId,
              success: true,
              fulfillmentId: fulfillment?.id,
              status: fulfillment?.status,
              totalQuantity: fulfillment?.totalQuantity,
            };
          } catch (error) {
            return {
              fulfillmentOrderId,
              success: false,
              error: error.message,
            };
          }
        }
      )
    );

    const successfulFulfillments = fulfillmentResults.filter((r) => r.success);

    return {
      success: true,
      message: `Processed ${matchedItems.length} items, created ${successfulFulfillments.length} fulfillments`,
      processed: matchedItems.length,
      successful: successfulFulfillments.length,
      failed: fulfillmentResults.length - successfulFulfillments.length,
      results: fulfillmentResults,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      message: error.message,
      error: error,
    };
  }
};

export {
  getShopifyOrder,
  createERPOrderService,
  formatOrderForERP,
  getcreatedOrderDataHandler,
  getERPOrders,
  shippedOrderSync,
  cancelOrderSync,
  getOrderFulfillmentsService,
  processMatchedERPFulfillmentsforRefund,
  refundLineItemsService,
  updateERPStatusesInBatches,
  processOrderFulfillmentsWithPagination,
};
