import moment from "moment";
import fs from "fs";
import xlsx from "xlsx";
import path from "path";
import { fileURLToPath } from "url";
import mongoose from "mongoose";
import Department from "../model/department.model.js";
import Distributor from "../model/distributor.model.js";
import DepartmentType from "../model/departmentType.model.js";
import Order from "../model/order.model.js";
import Status from "../model/status.model.js";
import {
  breakOrderBasedOnShipment,
  createOrderInDBService,
  validateAllocationQuantityWithERPService,
  generateOrderSheetNewService,
  checkUploadedOrder,
  cancelOrderService,
} from "../service/orderService.js";
import AppError from "../utils/appError.js";
import NewAppError from "../utils/newAppError.js";
import catchAsync from "../utils/catchAsync.js";
import APIFeatures from "../utils/apiFeatures.js";
import {
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import { Email } from "../service/rule/index.js";
import Timeline from "../model/timeline.model.js";
import Shipment from "../model/shipment.model.js";
import Cart from "../model/cart.model.js";
import EmailNotification from "../model/emailNotification.model.js";
import {
  extractKeyFromURL,
  generateSignedURL,
  uploadToS3,
} from "../utils/helperFunction.js";
import Inventory from "../model/inventory.model.js";
import {
  getDepartmentNotifiedEmails,
  getDepartmentForCreated,
} from "../utils/findNotifiedDepartment.js";
import { processOrders } from "../controller/action.controller.js";
import { allocatedMoreThanOrderedCheck } from "../helpers/sheetValidation.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import {
  getCompanyCatalogs,
  getCustomerInfo,
  getProductDetails,
  getVariantPrice,
  getVariantPriceFromCatalog,
} from "./shopify.controller.js";
import {
  GET_ALL_PRODUCTS_AND_VARIANTS,
  GET_PRODUCT_METAFIELD,
  GET_VARIANTS_FROM_CATALOG,
} from "../queries/internalQueries.js";
import {
  confirmDraftOrder,
  createDraftOrder,
  deleteDraftOrder,
  getVariantIdBySKU,
  // amount,
} from "./shopify.controller.js";
import { generateExcelSheet } from "../helpers/excelSheetGenerate.js";
import { CURRENCY_CODE } from "../constants/erpConstants.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const orderReport = async (req, res, next) => {
  try {
    const today = moment().startOf("day");
    const startOfCurrentMonth = today.clone().startOf("month");
    const startOfPreviousMonth = startOfCurrentMonth
      .clone()
      .subtract(1, "month");
    const endOfPreviousMonth = startOfCurrentMonth.clone().subtract(1, "day");

    const ordersLast30Days = await Order.find({
      createdAt: { $gte: startOfCurrentMonth.toDate(), $lte: today.toDate() },
    });

    const ordersPreviousMonth = await Order.find({
      createdAt: {
        $gte: startOfPreviousMonth.toDate(),
        $lte: endOfPreviousMonth.toDate(),
      },
    });

    const distributorIdsLast30Days = new Set(
      ordersLast30Days.map((order) => order.distributor?._id)
    );
    const distributorIdsPreviousMonth = new Set(
      ordersPreviousMonth.map((order) => order.distributor?._id)
    );

    const countryManagersLast30Days = await Department.find({
      distributor: { $in: Array.from(distributorIdsLast30Days) },
      "designation.isCountryManager": true,
    });

    const countryManagersPreviousMonth = await Department.find({
      distributor: { $in: Array.from(distributorIdsPreviousMonth) },
      "designation.isCountryManager": true,
    });

    const ordersPerDistributorLast30Days = ordersLast30Days.reduce(
      (acc, order) => {
        const distributorName = order.distributor && order.distributor.name;
        if (distributorName) {
          if (!acc[distributorName]) {
            acc[distributorName] = 0;
          }
          acc[distributorName] += 1;
        } else {
          if (!acc["Unknown"]) {
            acc["Unknown"] = 0;
          }
          acc["Unknown"] += 1;
        }
        return acc;
      },
      {}
    );

    const ordersPerDistributorPreviousMonth = ordersPreviousMonth.reduce(
      (acc, order) => {
        const distributorName = order.distributor && order.distributor.name;
        if (distributorName) {
          if (!acc[distributorName]) {
            acc[distributorName] = 0;
          }
          acc[distributorName] += 1;
        } else {
          if (!acc["Unknown"]) {
            acc["Unknown"] = 0;
          }
          acc["Unknown"] += 1;
        }
        return acc;
      },
      {}
    );

    // Count orders per country manager
    // const ordersPerCountryManagerLast30Days = countryManagersLast30Days.reduce(
    //   (acc, manager) => {
    //     // Filter orders placed by the current manager's distributors
    //     const managerDistributorIds = manager.distributor.map(dist => dist.toString());

    //     const ordersByManager = ordersLast30Days.filter(
    //       (order) =>
    //         managerDistributorIds.includes(order.distributor.toString())
    //     );

    //     // Count the total orders placed by this manager's distributors
    //     const managerName = manager.name;
    //     acc[managerName] = ordersByManager.length;

    //     return acc;
    //   },
    //   {}
    // );

    // const ordersPerCountryManagerPreviousMonth =
    //   countryManagersPreviousMonth.reduce((acc, manager) => {
    //     ordersPreviousMonth.forEach((order) => {
    //       if (manager.distributor.includes(order.distributor)) {
    //         acc[manager._id] = (acc[manager._id] || 0) + 1;
    //       }
    //     });
    //     return acc;
    //   }, {});

    const orderCountLast30Days = ordersLast30Days.length;
    const orderCountPreviousMonth = ordersPreviousMonth.length;
    const distributorCountLast30Days = distributorIdsLast30Days.size;
    const distributorCountPreviousMonth = distributorIdsPreviousMonth.size;
    const countryManagerCountLast30Days = countryManagersLast30Days.length;
    const countryManagerCountPreviousMonth =
      countryManagersPreviousMonth.length;
    const orderPercentageChange =
      ((orderCountLast30Days - orderCountPreviousMonth) /
        orderCountPreviousMonth) *
      100;
    const distributorPercentageChange =
      ((distributorCountLast30Days - distributorCountPreviousMonth) /
        distributorCountPreviousMonth) *
      100;
    const countryManagerPercentageChange =
      ((countryManagerCountLast30Days - countryManagerCountPreviousMonth) /
        countryManagerCountPreviousMonth) *
      100;
    res.status(200).send({
      orderCountLast30Days,
      orderCountPreviousMonth,
      orderPercentageChange,
      distributorCountLast30Days,
      distributorCountPreviousMonth,
      distributorPercentageChange,
      countryManagerCountLast30Days,
      countryManagerCountPreviousMonth,
      countryManagerPercentageChange,
      ordersPerDistributorLast30Days,
      ordersPerDistributorPreviousMonth,
      // ordersPerCountryManagerLast30Days,
      // ordersPerCountryManagerPreviousMonth,
    });
  } catch (error) {
    console.log(error);
    res.status(200).send({ error: "An error occurred" });
  }
};

export const generateOrderAttributes = async (shopifyCompanyId) => {
  const distributor = await Distributor.findOne({
    shopifyCompanyId: shopifyCompanyId,
  });
  const allShipmentStatus = await Status.find({ statusType: "Shipment" });
  const initialOrderStatus = await Status.find({
    statusType: "Order",
    isInitialStatus: true,
  });

  const departmentTypeIds = [...allShipmentStatus, ...initialOrderStatus].map(
    (shipmentStatus) => {
      return shipmentStatus.departmentType._id;
    }
  );

  const departmentPeoples = await Department.find({
    departmentType: { $in: departmentTypeIds },
    distributor: distributor._id,
  });

  const shipmentAttributesValues = allShipmentStatus.map((status) => {
    const departmentPeople = departmentPeoples.find(
      (it) =>
        it.departmentType._id.toString() == status.departmentType._id.toString()
    );

    return {
      category: "Shipment",
      statusId: status._id,
      status: status.status,
      isInitialStatus: status.isInitialStatus,
      escalationTriggerPeriod: status.escalationTriggerPeriod,
      emailInfo: [
        {
          name: departmentPeople?.name,
          email: departmentPeople?.email,
        },
      ],
    };
  });
  const orderAttributesValues = initialOrderStatus.map((status) => {
    const departmentPeople = departmentPeoples.find(
      (it) =>
        it.departmentType._id.toString() ==
        status.departmentType.map((dept) => dept._id.toString())
    );

    return {
      category: "Order",
      statusId: status._id,
      status: status.status,
      isInitialStatus: status.isInitialStatus,
      escalationTriggerPeriod: status.escalationTriggerPeriod,
      emailInfo: [
        {
          name: departmentPeople?.name,
          email: departmentPeople?.email,
        },
      ],
    };
  });
  const attributes = {
    name: "Send Notification Mail",
    type: "SEND_EMAIL",
    value: [...shipmentAttributesValues, ...orderAttributesValues],
  };
  return [attributes];
};

export const shopifyOrderDataInDB = catchAsync(async (req, res, next) => {
  try {
    const orderPayload = req.body;
    const isExistingOrder = await Order.findOne({ name: orderPayload.name });

    if (isExistingOrder) {
      return res.status(200).json({
        status: "ok",
      });
    }

    const shopifyCompanyId = orderPayload.company?.id;

    const initialOrderStatus = await Status.findOne({
      isInitialStatus: true,
      statusType: "Order",
    });
    const distributor = await Distributor.findOne({
      shopifyCompanyId: shopifyCompanyId,
    });

    //! Logic To Create Attribute [START]
    //Send mail to department people associated to distributor
    //* 1) Get Departmet Associated
    //* 1) Get All Shipment Status
    //! Logic To Create Attribute [END]

    const shopifyCompanyLocationId = orderPayload.company?.location_id;
    const attributes = await generateOrderAttributes(shopifyCompanyId);

    const orderObject = {
      ...orderPayload,
      attributes: attributes,
      shopifyOrderId: orderPayload.id,
      shopifyCompanyId,
      shopifyCompanyLocationId,
      status: initialOrderStatus._id,
      distributor: distributor._id,
    };
    console.log("orderCreated", orderObject);

    const orderCreated = await Order.create(orderObject);

    const sendEmailAttributes = attributes.find((it) => {
      return it.type == "SEND_EMAIL";
    });
    const orderEmailSendAttribute = sendEmailAttributes.value.find((it) => {
      return it.category == "Order" && it.isInitialStatus;
    });
    if (orderEmailSendAttribute?.emailInfo) {
      for (const emailDetail of orderEmailSendAttribute.emailInfo) {
        await new Email(
          {
            name: emailDetail?.name,
            email: emailDetail?.email,
            orderId: orderPayload.id,
            orderStatus: orderEmailSendAttribute.status,
          },
          "http://google.com"
        ).sendTest();
      }
    }
    await Timeline.create({
      category: "Order",
      message: `Order ${orderPayload.id} created`,
      order: orderCreated._id,
    });
    const shipmentCreated = await breakOrderBasedOnShipment(
      orderObject,
      orderCreated._id
    );

    res.status(201).json({
      status: "success",
      data: {
        data: {
          order: orderCreated,
          shipment: shipmentCreated,
        },
      },
    });
  } catch (error) {
    console.log(error);
  }
});

export const updateOrderAttribute = catchAsync(async (req, res, next) => {
  const orderId = req.params.id;
  const attributeId = req.params.attributeId;
  // const fieldId = req.params.fieldId;
  const attributes = req.body.attributes;
  if (!attributes)
    return next(
      new AppError("Missing attributes key in payload, example attributes[{}]")
    );
  const setOperations = {}; // To store $set operations
  const arrayFilters = []; // To store array filters
  attributes.forEach((fieldsToUpdate) => {
    const filterName = `attribute_${fieldId
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "")}`;
    // Unique name for array filter
    arrayFilters.push({ [`${filterName}.attributeId`]: attributeId }); // Define array filter
    // Create $set operations for each key-value pair in the update
    Object.entries(fieldsToUpdate).forEach(([key, value]) => {
      setOperations[`attribute.$[${filterName}].${key}`] = value; // Dynamic $set operation
    });
  });
  const updateResult = await Order.updateOne(
    { _id: orderId }, // Find the document by ID
    { $set: setOperations }, // Apply the set operations
    { arrayFilters } // Apply the defined array filters
  );
  res.status(200).json({
    status: "success",
    data: {
      data: updateResult,
    },
  });
});

export const deleteOrderAttribute = catchAsync(async (req, res, next) => {
  const orderId = req.params.id;
  const attributeId = req.params.attributeId;
  const updateResult = await Order.updateOne(
    { _id: orderId },
    { $pull: { attributes: { attributeId: attributeId } } }
  );
  res.status(200).json({
    status: "success",
    data: updateResult,
  });
});

export const getOrderByStatus = async (req, res, next) => {
  try {
    const user = req.user;
    const departmentTypes =
      user?.departmentType?.map((dt) => dt.pseudoId) || [];
    let filter = {};

    // Admins see all orders
    if (
      departmentTypes.includes("SUNRISE_ADMIN") ||
      user.type === "admin_user"
    ) {
      // No filter needed
    } else {
      // For non-admins, find all distributors where salespersonId matches user._id
      const distributors = await Distributor.find({ salespersonId: user._id });
      const distributorIds = distributors.map((d) => d._id);
      filter.distributor = { $in: distributorIds };
    }

    // Get counts
    const [total, pending, orderCreatedInSage, orderCompleted, orderRejected] =
      await Promise.all([
        Order.countDocuments(filter),
        Order.countDocuments({ ...filter, status: "pending" }),
        Order.countDocuments({ ...filter, status: "order placed" }),
        Order.countDocuments({ ...filter, status: "order completed" }),
        Order.countDocuments({ ...filter, status: "rejected" }),
      ]);

    return res.status(200).json({
      status: "success",
      data: {
        data: [
          { status: "Pending", count: pending },
          { status: "Order Placed", count: orderCreatedInSage },
          { status: "Order Completed", count: orderCompleted },
          { status: "Order Rejected", count: orderRejected },
        ],
        total,
      },
    });
  } catch (error) {
    console.log("error------------------>", error);
    res.status(500).json({ status: "error", message: error.message });
  }
};

export const editOrderFromSheet = catchAsync(async (req, res, next) => {
  const orderId = req.params.id;
  const orderData = await Order.findOne({ _id: orderId });
  if (orderData.status.status != "Created") {
    res.status(400).send({ message: "Processed order can not be updated" });
  }

  if (!req.file) return next(new AppError("No file attached"));

  const filePath = req.file.path;
  const workbook = xlsx.readFile(filePath);

  const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
  const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

  if (jsonData.length === 0) return next(new AppError("Sheet is Empty", 400));

  const hasDuplicates = checkForDuplicates(jsonData);
  if (hasDuplicates)
    return next(
      new AppError("Duplicate ProductCode entries are not allowed.", 400)
    );

  const orderWithInvalidQuantity = jsonData.filter(
    (order) => !(Number.isInteger(order.Quantity) && order.Quantity > 0)
  );

  const orderWithEmptySKU = jsonData.filter((order) => !order.ProductCode);

  if (orderWithEmptySKU.length > 0) {
    return next(new AppError("Some Items have invalid ProductCode", 400));
  }

  if (orderWithInvalidQuantity.length > 0) {
    return next(new AppError("Some Items have Invalid quantity", 400));
  }

  const allSkus = jsonData.map((x) => x.ProductCode);

  const itemsFromDB = await Inventory.find({
    sku: { $in: allSkus },
  });

  const jsonDataWithPrice = jsonData.map((item) => {
    const inventory = itemsFromDB.find((x) => x.sku === item.ProductCode);
    if (!inventory) {
      return next(new AppError(item.ProductCode + " is not available", 400));
    }
    delete inventory._id;
    delete inventory.quantity;
    return {
      shopifyVariantId: inventory.shopifyVariantId,
      shopifyProductId: inventory.shopifyProductId,
      productTitle: inventory.productTitle,
      variantTitle: inventory.variantTitle,
      image: inventory.image,
      quantity: item.Quantity,
      price: inventory.price,
      sku: inventory.sku,
    };
  });

  const updatedOrder = await Order.updateOne(
    { _id: orderId },
    { line_items: jsonDataWithPrice }
  );

  fs.unlinkSync(filePath);
  res.status(200).json({ updatedOrder, updatedLineItems: jsonDataWithPrice });
});

export const generateOrderSheet = async (req, res, next) => {
  const orderId = req.params.id;
  const uploadResponse = await generateOrderSheetService(orderId);
  res.status(200).send(uploadResponse);
};

export const generateOrderSheetService = async (orderId) => {
  const order = await Order.findOne({ _id: orderId });
  let sheetPayload = [];

  let sheetHeader = ["productTitle", "quantity", "sku", "price"];

  sheetPayload.push(sheetHeader);

  order.line_items.forEach((lineItem) => {
    let filteredItem = [];
    sheetHeader.forEach((key) => {
      if (lineItem.hasOwnProperty(key)) {
        filteredItem.push(lineItem[key] ? lineItem[key] : "");
      } else {
        filteredItem.push("");
      }
    });
    sheetPayload.push(filteredItem);
  });

  sheetPayload[0] = ["Title", "Quantity", "ProductCode", "FOB"];

  const workbook = xlsx.utils.book_new();
  const sheet = xlsx.utils.aoa_to_sheet(sheetPayload);

  xlsx.utils.book_append_sheet(workbook, sheet, "Sheet1");

  const fileName = `OrderName-${order.name}-m1.xlsx`;

  const orderSheetPath = path.join(
    __dirname,
    "..",
    "asset",
    "pi",
    `${fileName}`
  );

  xlsx.writeFile(workbook, orderSheetPath);

  const uploadResponse = await uploadToS3(
    orderSheetPath,
    process.env.AWS_S3_BUCKET,
    fileName
  );

  fs.unlinkSync(orderSheetPath);
  return fileName;
};

export const generateOrderSheetNewController = async (req, res, next) => {
  try {
    const orderId = req.params.id;
    const filedata = await generateOrderSheetNewService(orderId);
    if (filedata instanceof NewAppError) {
      return next(filedata);
    }

    const fileBuffer = xlsx.write(filedata.workbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    // Set headers for file download
    res.setHeader(
      "Content-disposition",
      `attachment; filename=${filedata.fileName}`
    );
    res.setHeader(
      "Content-type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );

    // Send the file buffer directly in the response
    return res.status(200).send(fileBuffer);
  } catch (error) {
    return next(
      new NewAppError("SERVER_ERROR", `Could not generate sheet.`, 500)
    );
  }
};

export const manualAllocateOrderSheetController = catchAsync(
  async (req, res, next) => {
    try {
      const orderId = req.params.id;
      const orderData = await Order.findOne({ _id: orderId });
      if (!orderData) {
        return next(
          new NewAppError(
            "NOT_FOUND",
            `Order not found with id:${orderId}`,
            404
          )
        );
      }

      //below hardcoded value is status id for Created Order.Needs to be dynamic.
      if (orderData.status._id.toString() != "673ad82d9f700ef856ca0124") {
        return next(
          new NewAppError(
            "INVALID_INPUT",
            "Processed order can not be updated.",
            400
          )
        );
      }

      if (!req.file) {
        return next(new NewAppError("FILE_MISSING", "No file attached", 400));
      }

      const filePath = req.file.path;

      const workbook = xlsx.readFile(filePath);

      const sheetName = workbook.SheetNames[0];
      const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName], {
        defval: null,
      });

      if (jsonData.length === 0) {
        return new NewAppError("INVALID_INPUT", "Sheet is Empty.", 400);
      }

      const otherChecks = checkUploadedOrder(jsonData, orderData.line_items);

      if (!otherChecks.valid) {
        return next(new NewAppError("INVALID_INPUT", otherChecks.error, 400));
      }

      const orderWithInvalidQuantity = jsonData.filter(
        (order) =>
          !(
            Number.isInteger(order.AllocatedQuantity) &&
            order.AllocatedQuantity >= 0
          )
      );

      if (orderWithInvalidQuantity.length > 0) {
        return next(
          new NewAppError(
            "INVALID_INPUT",
            "Some Items have Invalid AllocatedQuantity.",
            400
          )
        );
      }

      const allocatedMoreThanOrdered = await allocatedMoreThanOrderedCheck(
        orderData.line_items,
        jsonData,
        "order"
      );
      if (!allocatedMoreThanOrdered) {
        return next(
          new NewAppError("SERVER_ERROR", "Error while validating input.", 500)
        );
      }
      if (!allocatedMoreThanOrdered.valid) {
        return next(
          new NewAppError(
            "INVALID_INPUT",
            `Allocated quantity cannot be greater than order quantity for skus:${allocatedMoreThanOrdered.errors.map(
              (sku) => sku
            )}.`,
            400
          )
        );
      }

      const validateAllocationWithERP =
        await validateAllocationQuantityWithERPService(jsonData);

      if (validateAllocationWithERP instanceof NewAppError) {
        return next(validateAllocationWithERP);
      }

      if (!validateAllocationWithERP.allocationValid) {
        return next(
          new NewAppError(
            "INVALID_INPUT",
            `Allocated quantity not Available on ERP for skus:${validateAllocationWithERP.skusWithInvalidAllocation.join()}`,
            400
          )
        );
      }

      const allocatedQuantity = jsonData.map((item) => {
        return { sku: item.Sku, quantity: item.AllocatedQuantity };
      });
      const allocationType = { type: "manual", allocatedQuantity };

      const orderIdArray = Array.of(orderId);
      const processOrder = await processOrders(orderIdArray, allocationType);
      if (processOrder instanceof NewAppError) {
        return next(processOrder);
      }
      fs.unlinkSync(filePath);

      res.status(200).send({
        responseCode: 0,
        status: "success",
        data: {
          successfulOrders: processOrder.successfulOrders,
          failedOrders: processOrder.failedOrders,
          message: processOrder.message,
        },
      });
    } catch (error) {
      return next(new NewAppError("SERVER_ERROR", error.message, 500));
    }
  }
);

export const convertCartToOrder = async (req, res, next) => {
  const cartId = req.params.cartId;
  const { billing_address, shipping_address } = req.body;

  if (!billing_address || !shipping_address) {
    return next(
      new AppError(
        "Both billing_address and shipping_address are mandatory.",
        400
      )
    );
  }
  const cart = await Cart.findOne({ _id: cartId, status: "CREATED" });

  if (!cart) return next(new AppError(`Invalid Cart!`, 400));
  const initialOrderStatus = await Status.findOne({
    statusType: "Order",
    isInitialStatus: true,
  });

  if (!initialOrderStatus)
    return next(
      new AppError("Missing Initial Order Status, Create Initial Order Status")
    );

  const originalDistributor = await Distributor.findOne({
    shopifyCustomerId: parseInt(cart.customer.shopifyCustomerId),
  });

  if (!originalDistributor) return next(new AppError("Customer is missing!"));

  const orderPayload = {
    shopifyCompanyId: cart.shopifyCompanyId,
    cartId: cart.cartId,
    status: initialOrderStatus._id,
    distributor: originalDistributor._id,
    customer: cart.customer,
    line_items: cart.line_items,
    shipping_address: shipping_address,
    billing_address: billing_address,
  };
  const createdOrder = await Order.create(orderPayload);
  const updatedCartStatus = await Cart.updateOne(
    { _id: cartId },
    { status: "COMPLETED" }
  );
  // const orderSheetUploadResponse = await generateOrderSheetService(
  //   createdOrder._id
  // );
  // orderSheetUploadResponse.Location,

  const departmentPeoples = await Department.find({
    departmentType: { $in: [initialOrderStatus.departmentType._id] },
  });

  const departmentDetails = await getDepartmentForCreated(
    initialOrderStatus._id
  );

  const distributor = await Distributor.findOne({
    email: cart?.customer.email,
  });

  const recipients = departmentPeoples.map((departmentPeople) => ({
    name: departmentPeople.name,
    email: departmentPeople.email,
    cc: Array.isArray(departmentDetails) ? [...departmentDetails] : [],
  }));

  // if (recipients.length > 0) {
  //   await EmailNotification.create({
  //     emailCategory: "ORDER",
  //     emailType: "ORDER_CREATE",
  //     reciepient: recipients,
  //     emailPayload: {
  //       orderName: createdOrder.name,
  //       date: createdOrder?.createdAt,
  //       orderStatus: initialOrderStatus.status,
  //       distributorName: distributor.name,
  //       cartProducts: createdOrder.line_items,
  //     },
  //   });
  // }

  let countryManagerEmails = [];

  if (distributor) {
    if (distributor.countryManager && distributor.countryManager.length > 0) {
      await Promise.all(
        distributor.countryManager.map(async (x) => {
          const manager = await Department.findById(x);
          if (manager) {
            countryManagerEmails.push({
              email: manager.email,
              name: manager.name,
            });
          }
        })
      );
    }
  }

  const recipientsPeople = countryManagerEmails.map((manager) => ({
    name: manager.name,
    email: manager.email,
    cc: Array.isArray(departmentDetails) ? [...departmentDetails] : [],
  }));

  if (recipientsPeople.length > 0) {
    await EmailNotification.create({
      emailCategory: "ORDER",
      emailType: "ORDER_CREATE_NOTIFY_MANAGER",
      reciepient: recipientsPeople,
      emailPayload: {
        orderName: createdOrder.name,
        orderStatus: initialOrderStatus.status,
        date: createdOrder.createdAt,
        // attachments: [orderSheetUploadResponse.Location],
        distributorName: distributor.name,
        distributorEmail: distributor.email,
        cartProducts: createdOrder.line_items,
      },
    });
  }

  res.status(200).send({
    status: "success",
    data: {
      data: createdOrder,
    },
  });
};

const splitOrderIntoSubOrders = async (orderData) => {
  try {
    const line_items = orderData.line_items;
    const orderNumber = orderData.order_number;
    const preOrdersMap = {};
    const budgetMap = {};

    for (const lineItem of line_items) {
      const productDetails = await getProductDetails(lineItem.productId);
      const tags = productDetails?.tags || [];

      // Extract metafields into a key-value map
      const metafields = {};
      if (productDetails?.metafields?.edges) {
        for (const edge of productDetails.metafields.edges) {
          const { key, value } = edge.node;
          metafields[key] = value;
        }
      }

      const isPreOrder = tags?.includes("pre_order");
      const groupKey = isPreOrder
        ? (metafields.pre_order || "-").trim()
        : (metafields.budget_category || "-").trim();

      if (isPreOrder) {
        if (!preOrdersMap[groupKey]) {
          preOrdersMap[groupKey] = {
            preOrderValue: (metafields.pre_order || "-").trim(),
            budgetCategoryValue: (metafields.budget_category || "-").trim(),
            lineItems: [],
          };
        }
        preOrdersMap[groupKey].lineItems.push(lineItem);
      } else {
        if (!budgetMap[groupKey]) budgetMap[groupKey] = [];
        budgetMap[groupKey].push(lineItem);
      }
    }

    const subOrders = [];
    let subOrderCount = 1;

    // Create suborders from Pre-Order groups
    for (const model in preOrdersMap) {
      const preOrderGroup = preOrdersMap[model];
      subOrders.push({
        subOrderId: `${orderNumber}_${subOrderCount++}`,
        orderType: "pre-order",
        preOrderValue: preOrderGroup.preOrderValue,
        budgetCategoryValue: preOrderGroup.budgetCategoryValue,
        line_items: preOrderGroup.lineItems,
      });
    }

    // Create suborders from Budget groups
    for (const category in budgetMap) {
      subOrders.push({
        subOrderId: `${orderNumber}_${subOrderCount++}`,
        orderType: "budget-category",
        preOrderValue: "-",
        budgetCategoryValue: category,
        line_items: budgetMap[category],
      });
    }

    return subOrders;
  } catch (error) {
    console.log("order split error", error);
    throw new NewAppError("SERVER_ERROR", error.message, 500);
  }
};
export const storeOrderInDB = catchAsync(async (req, res, next) => {
  res.status(200).json({
    responseCode: 0,
    status: "success",
    data: [],
  });
  const orderData = req.body;

  if (!orderData) {
    return next(new AppError(`Order Payload Missing.`, 400));
  }
  const updatedLineItems = await getMetafieldsForLineItems(
    orderData.line_items
  );

  const updatedOrderData = {
    ...orderData,
    line_items: updatedLineItems,
  };

  const subOrders = await splitOrderIntoSubOrders(updatedOrderData);

  const createdOrders = [];

  for (const subOrder of subOrders) {
    const orderToCreate = {
      ...updatedOrderData,
      line_items: subOrder.line_items,
      subOrderId: subOrder.subOrderId,
      orderType: subOrder.orderType,
      preOrderValue: subOrder.preOrderValue || "-",
      budgetCategoryValue: subOrder.budgetCategoryValue || "-",
    };

    const orderCreated = await createOrderInDBService(orderToCreate);

    if (orderCreated instanceof AppError) {
      return next(orderCreated);
    }

    createdOrders.push(orderCreated);

    console.log(`Total orders created: ${createdOrders.length} `);
  }
});

export const getOrders = getAll(Order);
export const getOneOrder = getOne(Order);
export const updateOrder = updateOne(Order);
export const deleteOrder = deleteOne(Order);

export const getDeptWiseOrders = catchAsync(async (req, res, next) => {
  const user = req.user;
  const salesPersonDept = req.salesPersonDept;
  const salesPersonsUnderCoordinator = req.salesPersonsUnderCoordinator;

  let query = Order.find();
  let totalCount = await Order.countDocuments();

  // Case 1: Direct Salesperson
  if (salesPersonDept) {
    const distributorIds = await Distributor.distinct("_id", {
      salespersonId: user._id.toString(),
    });
    totalCount = await Order.countDocuments({
      distributor: { $in: distributorIds },
    });
    query = Order.find({ distributor: { $in: distributorIds } });
  }

  // Case 2: Sunrise Coordinator
  else if (
    salesPersonsUnderCoordinator &&
    Array.isArray(salesPersonsUnderCoordinator) &&
    salesPersonsUnderCoordinator.length > 0
  ) {
    const salesPersonIds = salesPersonsUnderCoordinator.map((sp) =>
      sp._id.toString()
    );

    const distributorIds = await Distributor.distinct("_id", {
      salespersonId: { $in: salesPersonIds },
    });

    totalCount = await Order.countDocuments({
      distributor: { $in: distributorIds },
    });

    query = Order.find({ distributor: { $in: distributorIds } });
  }

  const features = new APIFeatures(query, req.query)
    .filter()
    .sort()
    .limitFields()
    .paginate();

  const doc = await features.query;

  res.status(200).json({
    status: "success",
    result: totalCount,
    data: {
      data: doc,
    },
  });
});

// check for duplicate SKU entries
export function checkForDuplicates(data) {
  const productCodeSet = new Set();
  for (let item of data) {
    if (item.sku) {
      if (productCodeSet.has(item.sku)) {
        return true;
      }
      productCodeSet.add(item.sku);
    }

    if (item.ProductCode) {
      if (productCodeSet.has(item.ProductCode)) {
        return true;
      }
      productCodeSet.add(item.ProductCode);
    }
  }
  return false;
}

export async function cancelOrderController(req, res, next) {
  try {
    const orderDBId = req.params.id;
    if (!orderDBId) {
      return next(new NewAppError("BAD_REQUEST", `OrderId is required.`, 404));
    }

    const order = await Order.findById(orderDBId);
    if (!order) {
      return next(
        new NewAppError(
          "NOT_FOUND",
          `Order with ID ${orderDBId} does not exist.`,
          404
        )
      );
    }
    const orderId = order.order_id;

    const cancelOrder = await cancelOrderService(orderId);
    if (cancelOrder instanceof NewAppError) {
      return next(cancelOrder);
    }

    const cancelledOrderId = cancelOrder.id;

    //Below Hardcoded value is _id for status Cancelled Order.This needs to be dynamic.
    const updateOrderStatus = await Order.findByIdAndUpdate(
      orderDBId,
      { status: new mongoose.Types.ObjectId("674ef1c8ce77438d64f79cbd") },
      { new: true }
    );

    res.status(200).send({
      responseCode: 0,
      status: "success",
      data: {
        cancelledOrderId,
        message: "Order Cancelled.",
      },
    });
  } catch (error) {
    return next(new NewAppError("SERVER_ERROR", error.message, 500));
  }
}

export const getOrderShipmentsController = async (req, res, next) => {
  try {
    const orderId = req.params.id;
    const shipments = await Shipment.find({ order: orderId });
    return res.status(200).send({
      responseCode: 0,
      status: "success",
      data: {
        orderId,
        shipments,
      },
    });
  } catch (error) {
    return next(
      new NewAppError(
        "SERVER_ERROR",
        `Error while fetching shipments for orderId:${req.params.id}`,
        500
      )
    );
  }
};

async function getMetafieldsForLineItems(lineItems) {
  try {
    if (!lineItems || lineItems.length === 0) return lineItems;

    // Generate product GIDs
    const productIds = lineItems.map(
      (item) => `gid://shopify/Product/${item.product_id}`
    );

    const shopifyResponse = await fetchGraphqlDataShopify(
      GET_PRODUCT_METAFIELD,
      { ids: productIds }
    );

    // Extract metafield data
    const metafieldData = shopifyResponse?.data?.nodes || [];

    // Map lineItems and add meta_field_budget_category key
    return lineItems.map((item) => {
      const metafield = metafieldData.find(
        (node) => node?.id === `gid://shopify/Product/${item.product_id}`
      );

      return {
        ...item,
        meta_field_budget_category: metafield?.metafield?.value?.trim() || null,
        productId: `gid://shopify/Product/${item.product_id}`,
        requested: item.quantity,
        fulfilled: 0,
        remaining: item.quantity,
      };
    });
  } catch (error) {
    console.error("Error fetching metafields:", error);
    return lineItems.map((item) => ({
      ...item,
      meta_field_budget_category: null,
      productId: `gid://shopify/Product/${item.product_id}`,
      requested: item.quantity,
      fulfilled: 0,
      remaining: item.quantity,
    }));
  }
}

export const validateProductSheetHeader = (data) => {
  const availableHeaderKeys = Object.keys(data);
  const allowedHeaderKeys = ["SKU", "Quantity"];
  // const allowedHeaderKeys = ['brand', 'ProductCode', 'skuType', 'Quantity', 'ucp', 'variantId'];
  const invalidKeys = allowedHeaderKeys.filter((headerKey) => {
    return !availableHeaderKeys.includes(headerKey);
  });
  return invalidKeys;
};

export const createOrderFromSheet = catchAsync(async (req, res, next) => {
  let filePath;

  if (!req.file) {
    throw new NewAppError("FILE_MISSING", "No file attached", 400);
  }

  filePath = req.file.path;
  const customerId = req.body.customerId;

  if (!customerId) {
    // Clean up file if customerId is missing
    fs.unlinkSync(filePath);
    throw new NewAppError("VALIDATION_ERROR", "Missing customerId", 400);
  }

  const customerInfo = await getCustomerInfo(
    `gid://shopify/Customer/${customerId}`
  );
  if (!customerInfo || !customerInfo.companyInfo?.id) {
    throw new NewAppError(
      "NOT_FOUND",
      "Customer or company information not found",
      404
    );
  }

  // Try to get catalogs for the company
  const catalogs = await getCompanyCatalogs(customerInfo.companyInfo.id);
  let catalogId = null;

  if (catalogs && catalogs.length > 0) {
    catalogId = catalogs[0].id;
  }

  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  let jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

  const errors = [];
  const validData = [];

  // Header validation
  const incorrectHeaderKeys = validateProductSheetHeader(jsonData[0]);
  if (incorrectHeaderKeys.length > 0) {
    // Clean up file if headers are invalid
    fs.unlinkSync(filePath);
    throw new NewAppError(
      "VALIDATION_ERROR",
      `Invalid or missing header keys: [${incorrectHeaderKeys}]`,
      400
    );
  }

  // Validate each row and separate into valid and error data
  for (const item of jsonData) {
    const rowErrors = [];

    // Check for SKU
    if (!item.SKU) {
      rowErrors.push("Missing SKU");
    }

    // Check for quantity
    const quantity = item.quantity ?? item.Quantity;
    if (!quantity || isNaN(quantity) || quantity <= 0) {
      rowErrors.push("Invalid or missing quantity");
    }

    // Check for duplicate SKUs
    if (validData.some((valid) => valid.SKU === item.SKU)) {
      rowErrors.push("Duplicate SKU");
    }

    if (rowErrors.length > 0) {
      errors.push({
        ...item,
        Error: rowErrors.join(", "),
      });
    } else {
      validData.push({
        ...item,
        quantity: Number(quantity),
      });
    }
  }

  // If no valid data at all, return error sheet
  if (validData.length === 0) {
    const errorWorkbook = xlsx.utils.book_new();
    const errorSheet = xlsx.utils.json_to_sheet(errors);
    xlsx.utils.book_append_sheet(errorWorkbook, errorSheet, "Errors");

    const buffer = xlsx.write(errorWorkbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    // Clean up the original file
    fs.unlinkSync(filePath);

    res.setHeader(
      "Content-disposition",
      "attachment; filename=error_report.xlsx"
    );
    res.setHeader(
      "Content-type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );

    return res.status(400).send(buffer);
  }

  const lineItems = [];
  for (const item of validData) {
    const inventoryItem = await getVariantIdBySKU(item.SKU);
    if (inventoryItem === null) {
      errors.push({
        ...item,
        Error: "Invalid SKU",
      });
      continue;
    }

    const inventoryId = inventoryItem.id.split("/")?.pop();

    let priceData;
    if (catalogId) {
      priceData = await getVariantPriceFromCatalog(catalogId, inventoryId);
    } else {
      const variantData = await getVariantPrice(inventoryId);
      priceData = {
        amount: variantData.price,
        currencyCode: CURRENCY_CODE,
      };
    }

    lineItems.push({
      variantId: inventoryItem.id,
      quantity: item.quantity || item.Quantity,
      originalUnitPrice: priceData,
    });
  }

  const orderPayload = {
    input: {
      lineItems,
      customer: { id: `gid://shopify/Customer/${customerId}` },
    },
  };

  const orderCreated = await createDraftOrder(orderPayload);

  await Timeline.create({
    category: "Order",
    message: `Order created from sheet upload`,
    order: orderCreated._id,
  });

  // Clean up the uploaded file after successful processing
  fs.unlinkSync(filePath);

  // If there were any errors, return 207 status with both success and error data
  if (errors.length > 0) {
    const errorWorkbook = xlsx.utils.book_new();
    const errorSheet = xlsx.utils.json_to_sheet(errors);
    xlsx.utils.book_append_sheet(errorWorkbook, errorSheet, "Errors");

    const buffer = xlsx.write(errorWorkbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    return res.status(207).json({
      responseCode: 0,
      status: "partial_success",
      data: {
        order: orderCreated,
        successCount: validData.length,
        errorCount: errors.length,
        errorFile: buffer.toString("base64"),
      },
    });
  }

  // If all items were successful, return 200 status
  return res.status(200).json({
    responseCode: 0,
    status: "success",
    data: {
      order: orderCreated,
      totalSkuCount: validData.length,
    },
  });
});

export const removeDraftOrder = catchAsync(async (req, res, next) => {
  const orderId = req.params.id;
  if (!orderId)
    return next(new NewAppError("BAD_REQUEST", "Order Id is required", 400));
  const gid = `gid://shopify/DraftOrder/${orderId}`;
  const cancelledOrder = await deleteDraftOrder(gid);
  return res.status(200).json({
    responseCode: 0,
    status: "success",
    data: {
      cancelledOrder,
    },
  });
});

export const createCompleteOrder = catchAsync(async (req, res, next) => {
  const orderId = req.params.id;
  if (!orderId)
    return next(new NewAppError("BAD_REQUEST", "Order Id is required", 400));
  const confirmedOrder = await confirmDraftOrder(orderId);
  return res.status(200).send({
    responseCode: 0,
    status: "success",
    data: {
      confirmedOrder,
    },
  });
});

export const generateSampleSheetController = catchAsync(
  async (req, res, next) => {
    const headers = [
      { label: "SKU", key: "sku" },
      { label: "Quantity", key: "quantity" },
    ];

    const sampleData = [
      {
        quantity: 10,
        sku: "SKU001",
      },
      {
        quantity: 20,
        sku: "SKU002",
      },
    ];

    // Pass the header objects directly instead of just the labels
    const result = generateExcelSheet(sampleData, headers);

    if (!result.success) {
      return next(new NewAppError("EXCEL_GENERATION_ERROR", result.error, 500));
    }

    // Set headers for file download
    res.setHeader(
      "Content-disposition",
      "attachment; filename=sample_order_sheet.xlsx"
    );
    res.setHeader(
      "Content-type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );

    // Send the file buffer directly in the response
    return res.status(200).send(result.buffer);
  }
);

export const generateFullStockSheetController = catchAsync(
  async (req, res, next) => {
    const customerId = req.query.customerId;
    const userEmail = req.user?.email; // Get user email from authenticated user

    if (!customerId) {
      return next(
        new NewAppError("INVALID_INPUT", "Customer ID is required", 400)
      );
    }

    if (!userEmail) {
      return next(new NewAppError("UNAUTHORIZED", "User email not found", 401));
    }

    // Send immediate response to frontend
    res.status(200).json({
      success: true,
      message:
        "Stock sheet generation started. You will receive an email with the download link once it's ready.",
      status: "processing",
    });

    // Start background processing (don't await this)
    generateStockSheetBackground(customerId, userEmail).catch((error) => {
      console.error("Background stock sheet generation failed:", error);
    });
  }
);

// Background function to generate stock sheet
const generateStockSheetBackground = async (customerId, userEmail) => {
  try {
    let hasNextPage = true;
    let cursor = null;
    const batchSize = 50;

    // Get customer and company information
    const customerInfo = await getCustomerInfo(
      `gid://shopify/Customer/${customerId}`
    );

    const customerDetails = await Distributor.findOne({
      shopifyCustomerId: customerId,
    });

    if (!customerDetails) {
      throw new NewAppError("BAD_REQUEST", "Customer not found", 400);
    }

    if (!customerInfo || !customerInfo.companyInfo?.id) {
      throw new Error("Customer or company information not found");
    }

    // Try to get catalogs for the company
    const catalogs = await getCompanyCatalogs(customerInfo.companyInfo.id);
    const excelData = [];

    if (catalogs && catalogs.length > 0) {
      // Use catalog if available
      const catalogId = catalogs[0].id;
      console.log("Using catalog:", catalogId);

      while (hasNextPage) {
        try {
          const response = await fetchGraphqlDataShopify(
            GET_VARIANTS_FROM_CATALOG,
            {
              catalogId: catalogId,
              cursor: cursor,
              first: batchSize,
            }
          );

          const products = response?.data?.catalog?.priceList?.prices;

          if (!products?.edges) {
            console.log("No products found in response");
            break;
          }

          // Process products in current batch
          const filteredProducts = products.edges
            .map((edge) => edge.node)
            .filter((product) => product.variant?.availableForSale === true);

          filteredProducts.forEach((product) => {
            excelData.push({
              "Product Title": product?.variant?.product?.title || "",
              "Variant Title": product?.variant?.title || "",
              SKU: product?.variant?.sku || "",
              Price: product?.price?.amount || "0",
              "Product ID":
                product?.variant?.product?.id?.split("/").pop() || "",
              "Variant ID": product?.variant?.id?.split("/").pop() || "",
            });
          });

          hasNextPage = products.pageInfo.hasNextPage;
          cursor = products.pageInfo.endCursor;

          // Add small delay to prevent overwhelming the API
          if (hasNextPage) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        } catch (error) {
          console.error("Error fetching batch:", error.message);
          break;
        }
      }
    } else {
      // If no catalog found, fetch all products directly
      console.log(
        "No catalog found, fetching all products directly from Shopify"
      );

      while (hasNextPage) {
        try {
          const response = await fetchGraphqlDataShopify(
            GET_ALL_PRODUCTS_AND_VARIANTS,
            {
              cursor: cursor,
              first: batchSize,
            }
          );

          const products = response.data.products;

          if (!products?.edges) {
            console.log("No products found in response");
            break;
          }
          let addedCount = 0;

          products.edges.forEach((productEdge) => {
            const product = productEdge.node;
            if (product.variants?.edges) {
              product.variants.edges.forEach((variant) => {
                // Process all variants from active products (no availableForSale filter needed)
                excelData.push({
                  "Product Title": product.title || "",
                  "Variant Title": variant.node.title || "",
                  SKU: variant.node.sku || "",
                  Price: variant.node.price || "0",
                  "Product ID": product.id?.split("/").pop() || "",
                  "Variant ID": variant.node.id?.split("/").pop() || "",
                });
                addedCount++;
              });
            }
          });

          hasNextPage = products.pageInfo.hasNextPage;
          cursor = products.pageInfo.endCursor;
          if (hasNextPage) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }
        } catch (error) {
          console.error("Error fetching batch:", error.message);
          console.error("Full error:", error);
          break;
        }
      }
    }

    // Create workbook
    const workbook = xlsx.utils.book_new();
    const worksheet = xlsx.utils.json_to_sheet(excelData);
    xlsx.utils.book_append_sheet(workbook, worksheet, "Stock List");

    // Generate buffer
    const excelBuffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const fileName = `full_stock_list_${customerId}_${timestamp}.xlsx`;

    // Save buffer to temporary file for S3 upload
    const tempFilePath = path.join(__dirname, "..", "asset", "temp", fileName);

    // Ensure temp directory exists
    const tempDir = path.dirname(tempFilePath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Write buffer to temporary file
    fs.writeFileSync(tempFilePath, excelBuffer);

    // Upload to S3 using existing function
    const uploadResult = await uploadToS3(
      tempFilePath,
      process.env.AWS_S3_BUCKET,
      fileName
    );

    // Clean up temporary file
    fs.unlinkSync(tempFilePath);

    const sheetKey = extractKeyFromURL(uploadResult.Location);

    console.log(sheetKey, "sheetKey");

    const signedExcelSheetUrl = await generateSignedURL(
      process.env.AWS_S3_BUCKET,
      sheetKey,
      86400
    );

    console.log(signedExcelSheetUrl, "signedExcelSheetUrl");

    // Send email with download link
    await sendStockSheetEmail(userEmail, signedExcelSheetUrl, customerDetails);
  } catch (error) {
    console.error("Error in background stock sheet generation:", error);

    // Send error email to user
    await sendStockSheetErrorEmail(userEmail, error.message);
  }
};

// Helper function to send success email with download link
const sendStockSheetEmail = async (userEmail, downloadUrl, customerDetails) => {
  try {
    const emailNotification = new EmailNotification({
      emailCategory: "SYSTEM",
      emailType: "STOCK_SHEET_READY",
      reciepient: [
        {
          name: "User",
          email: userEmail,
          cc: [],
        },
      ],
      emailPayload: {
        stockSheetUrl: downloadUrl,
        customerName: customerDetails.name,
        salesPersonName: userEmail,
        customerNumber: customerDetails.customerNumber,
      },
    });

    await emailNotification.save();
    console.log(`Stock sheet email notification queued for: ${userEmail}`);
  } catch (error) {
    console.error("Error sending stock sheet email:", error);
    throw new Error("Failed to send email notification");
  }
};

// Helper function to send error email
const sendStockSheetErrorEmail = async (userEmail, errorMessage) => {
  try {
    const emailNotification = new EmailNotification({
      emailCategory: "SYSTEM",
      emailType: "STOCK_SHEET_ERROR",
      reciepient: [
        {
          name: "User",
          email: userEmail,
          cc: [],
        },
      ],
      emailPayload: {
        errorMessage: errorMessage,
        timestamp: new Date().toISOString(),
        storeName: "Sunrise Trade",
      },
    });

    await emailNotification.save();
    console.log(
      `Stock sheet error email notification queued for: ${userEmail}`
    );
  } catch (error) {
    console.error("Error sending stock sheet error email:", error);
  }
};

export const getAllBudgetAndPreOrderValues = catchAsync(async (req, res) => {
  const pipeline = [
    {
      $group: {
        _id: null,
        budgetCategoryValues: { $addToSet: "$budgetCategoryValue" },
        preOrderValues: { $addToSet: "$preOrderValue" },
      },
    },
    {
      $project: {
        _id: 0,
        budgetCategoryValues: 1,
        preOrderValues: 1,
      },
    },
  ];

  const [result] = await Order.aggregate(pipeline);

  const cleanResult = {
    budgetCategoryValues: (result?.budgetCategoryValues || []).filter(
      (val) => val && !val.startsWith("-")
    ),
    preOrderValues: (result?.preOrderValues || []).filter(
      (val) => val && !val.startsWith("-")
    ),
  };

  res.status(200).json({
    responseCode: 0,
    status: "success",
    data: cleanResult,
  });
});

export const getPendingOrdersSheet = catchAsync(async (req, res) => {
  const user = req.user;
  const salesPersonDept = req.salesPersonDept;
  const salesPersonsUnderCoordinator = req.salesPersonsUnderCoordinator;

  let filter = { status: { $in: ["pending", "created"] } };

  const {
    orderIds,
    input,
    fromDate,
    toDate,
    budgetCategoryValue,
    preOrderValue,
    status,
  } = req.query;

  // Filter by specific order IDs
  if (orderIds) {
    const idsArray = orderIds.split(",").map((id) => id.trim());
    filter._id = { $in: idsArray };
  }

  // Text search (applies to subOrderId and name fields)
  if (input) {
    filter.$or = [
      { subOrderId: { $regex: input, $options: "i" } },
      { name: { $regex: input, $options: "i" } },
    ];
  }

  // Date range filtering (createdAt)
  if (fromDate || toDate) {
    filter.createdAt = {};
    if (fromDate) filter.createdAt.$gte = new Date(fromDate);
    if (toDate) filter.createdAt.$lte = new Date(toDate);
  }

  if (budgetCategoryValue) {
    filter.budgetCategoryValue = budgetCategoryValue;
  }

  if (preOrderValue) {
    filter.preOrderValue = preOrderValue;
  }

  if (status) {
    filter.status = status;
  }

  // Case 1: Salesperson access
  if (salesPersonDept) {
    const distributorIds = await Distributor.distinct("_id", {
      salespersonId: user._id.toString(),
    });
    filter.distributor = { $in: distributorIds };
  }
  // Case 2: Sunrise Coordinator access
  else if (
    salesPersonsUnderCoordinator &&
    Array.isArray(salesPersonsUnderCoordinator) &&
    salesPersonsUnderCoordinator.length > 0
  ) {
    const salesPersonIds = salesPersonsUnderCoordinator.map((sp) =>
      sp._id.toString()
    );

    const distributorIds = await Distributor.distinct("_id", {
      salespersonId: { $in: salesPersonIds },
    });

    filter.distributor = { $in: distributorIds };
  }

  // Get filtered orders
  const orders = await Order.find(filter).populate("customer distributor");

  const sheetData = [];

  orders.forEach((order) => {
    order.line_items.forEach((item) => {
      sheetData.push({
        "customer id": order.customer?.id || "",
        "shopify order id": order.name,
        "oms order id": order.subOrderId,
        title: item.productTitle,
        sku: item.sku,
        "location code": item.locationCode || "1SR",
        "budget category": order.budgetCategoryValue || "-",
        "pre order number": order.preOrderValue || "-",
        "requested qty": item.quantity,
        "allocated qty": 0,
        "action ( future , reject , allocate )": "",
      });
    });
  });

  // Generate Excel workbook
  const workbook = xlsx.utils.book_new();
  const worksheet = xlsx.utils.json_to_sheet(sheetData);

  worksheet["!cols"] = [
    { width: 15 }, // customer id
    { width: 20 }, // shopify order id
    { width: 20 }, // oms order id
    { width: 30 }, // title
    { width: 20 }, // sku
    { width: 15 }, // location code
    { width: 25 }, // budget category
    { width: 25 }, // pre order number
    { width: 18 }, // requested qty
    { width: 18 }, // allocated qty
    { width: 30 }, // action
  ];

  xlsx.utils.book_append_sheet(workbook, worksheet, "Pending Orders");

  const excelBuffer = xlsx.write(workbook, {
    bookType: "xlsx",
    type: "buffer",
  });

  res.setHeader(
    "Content-Type",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  );
  res.setHeader(
    "Content-Disposition",
    "attachment; filename=pending_orders.xlsx"
  );

  res.send(excelBuffer);
});

