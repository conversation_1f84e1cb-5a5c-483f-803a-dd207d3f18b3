import axios, { all } from "axios";
import Inventory from "../../model/inventory.model.js";
import fetchGraphqlDataShopify from "../fetchGraphqlDataShopify.js";

const extractShopifyId = (gid) => gid.split("/").pop();

export const syncAppInventoryManually = async () => {
  try {
    let nextPageId;
    let requestUrl = `${process.env.SHOP_URL}/admin/api/${
      process.env.VERSION
    }/products.json?limit=${250}`;
    //&since_id=${nextPageId}`

    for (let i = 0; i < 10000; i++) {
      if (nextPageId) {
        console.log(`Fetching Data for Next Page Id ${nextPageId}`);
        requestUrl = `${process.env.SHOP_URL}/admin/api/${
          process.env.VERSION
        }/products.json?limit=${250}&since_id=${nextPageId}`;
      }
      const config = {
        method: "get",
        url: requestUrl,
        contentType: "application/json",
        headers: {
          "X-Shopify-Access-Token": `${process.env.SHOPIFY_ADMIN_ACCESS_TOKEN}`,
        },
      };
      const productResponse = await axios(config);
      const products = productResponse.data.products;
      console.log(productResponse.data);
      console.log(requestUrl);
      if (products.length <= 0) {
        console.log("All Products synced successfully Closing Exiting Process");
        // process.exit(1);
        break;
      }
      const productPayload = products.flatMap((product) => {
        const image = product.image;
        return product.variants.map((variant) => {
          return {
            shopifyVariantId: variant.id,
            shopifyProductId: product.id,
            productTitle: product.title,
            variantTitle: variant.title,
            price: variant.price,
            sku: variant.sku,
            quantity: variant.inventory_quantity,
            image:
              image?.src ||
              "https://cdn.shopify.com/s/files/1/0656/0969/5370/files/Titan-Watches-Logo.jpg?v=1725868130",
          };
        });
      });

      const bulkOps = productPayload.map((variant) => {
        return {
          updateOne: {
            filter: { sku: variant.sku }, // Find the product by productId
            update: { $set: variant }, // Update the product fields
            upsert: true, // Insert if product doesn't exist
          },
        };
      });

      // Execute the bulkWrite operation
      const syncingProducts = await Inventory.bulkWrite(bulkOps);
      console.log("syncingProducts", syncingProducts);

      // const createdInventory = await Inventory.insertMany(productPayload);
      nextPageId = products.pop()?.id;
      // console.log(
      //   `Saved data for Page ${i + 1}, RECORDS FETCHED ==> ${
      //     products.length
      //   }, RECORD SAVED ==> ${createdInventory.length}`
      // );
      // console.log(`NextPageId is ${nextPageId}`);
    }

    console.log("product update/insert function end");
  } catch (error) {
    console.log(error);
  }
};

const fetchProductsAndMetafields = async (cursor = null) => {
  const response = await fetchGraphqlDataShopify(GET_PRODUCTS_WITH_METAFIELDS, {
    productCursor: cursor,
  });

  const { nodes: products, pageInfo } = response.data.products;

  // Map the product data with metafields
  const productData = products.map((product) => ({
    id: product.id,
    title: product.title, // Added title to product mapping
    metafields: {
      brand: product.m1?.value || null,
      cluster: product.m2?.value || null,
    },
  }));

  return { productData, pageInfo };
};

const fetchProductVariants = async (productId, cursor = null) => {
  const response = await fetchGraphqlDataShopify(GET_PRODUCT_VARIANTS, {
    productId,
    variantCursor: cursor,
  });

  console.log(response, "Fetched Product Variants");

  const { edges: variants, pageInfo } = response.data.product.variants;
  const { nodes: images } = response.data.product.images; // Fetch images
  console.log(images, "images");

  const variantData = variants.map((edge) => ({
    id: edge.node.id,
    title: edge.node.title,
    price: edge.node.price,
    sku: edge.node.sku,
    inventoryQuantity: edge.node.inventoryQuantity,
    image:
      images?.[0]?.src || // Use the first product image if available
      "https://cdn.shopify.com/s/files/1/0656/0969/5370/files/Titan-Watches-Logo.jpg?v=1725868130",
  }));

  return { variantData, pageInfo };
};

export const syncProductsAndVariants = async () => {
  let productCursor = null;
  let hasNextPage = true;

  // Loop through all products with pagination
  while (hasNextPage) {
    const { productData, pageInfo } = await fetchProductsAndMetafields(
      productCursor
    );

    // Iterate through each product to fetch its variants
    for (const product of productData) {
      let variantCursor = null;
      let variantHasNextPage = true;
      let allVariants = [];

      // Fetch all variants for the product using pagination
      while (variantHasNextPage) {
        const { variantData, pageInfo: variantPageInfo } =
          await fetchProductVariants(product.id, variantCursor);
        allVariants.push(...variantData);

        // Update variant pagination info
        variantCursor = variantPageInfo.endCursor;
        variantHasNextPage = variantPageInfo.hasNextPage;
      }

      // Prepare product payload for the database
      const productPayload = allVariants.map((variant) => {
        return {
          shopifyVariantId: extractShopifyId(variant.id),
          shopifyProductId: extractShopifyId(product.id),
          productTitle: product.title || "Unknown Product",
          variantTitle: variant.title || variant.sku,
          price: variant.price,
          sku: variant.sku,
          brand: product.metafields.brand,
          cluster: product.metafields.cluster,
          quantity: variant.inventoryQuantity,
          image: variant.image,
        };
      });

      // Prepare bulk operations for database insertion
      const bulkOps = productPayload.map((variant) => {
        return {
          updateOne: {
            filter: { sku: variant.sku },
            update: { $set: variant },
            upsert: true,
          },
        };
      });

      // Execute the bulkWrite operation to sync variants to the database
      const result = await Inventory.bulkWrite(bulkOps);
      console.log(`Synced variants for product ID ${product.id}`, result);
    }

    // Update product pagination info
    productCursor = pageInfo.endCursor;
    hasNextPage = pageInfo.hasNextPage;
  }

  console.log("All products and variants synced successfully");
};

const GET_PRODUCTS_WITH_METAFIELDS = `
  query getProductsWithMetafields($productCursor: String) {
    products(first: 100, after: $productCursor) {
      nodes {
        id
        title
        m1: metafield(namespace: "custom", key: "brand") {
          value
          key
        }
        m2: metafield(namespace: "custom", key: "cluster") {
          value
          key
        }
      }
      pageInfo {
        hasNextPage
        endCursor
      }
    }
  }
`;

const GET_PRODUCT_VARIANTS = `
  query getProductVariants($productId: ID!, $variantCursor: String) {
    product(id: $productId) {
      variants(first: 100, after: $variantCursor) {
        pageInfo {
          hasNextPage
          endCursor
        }
        edges {
          node {
            id
            title
            price
            sku
            inventoryQuantity
          }
        }
      }
      images(first: 1) {
        nodes {
          src
        }
      }
    }
  }
`;
