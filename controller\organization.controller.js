import Organization from '../model/organization.model.js';
import { createOne, deleteOne, getAll, getOne, updateOne } from '../utils/controllerFactory.js';


export const getOrganizations = getAll(Organization);
export const getOneOrganization = getOne(Organization);
export const createOrganization = createOne(Organization);
export const updateOrganization = updateOne(Organization);
export const deleteOrganization = deleteOne(Organization);