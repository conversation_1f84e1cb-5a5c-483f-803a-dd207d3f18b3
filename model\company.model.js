import mongoose from "mongoose";

const companySchema = new mongoose.Schema(
  {
    shopifyCompanyId: {
      type: String,
    },
    name: {
      type: String,
    },
    note: {
      type: String,
    },
    priority: {
      type: Number,
    },
    customerIds: [{ type: String }],
    companyLocationCatalogIds: [
      {
        type: String,
      },
    ],
  },
  { timestamps: true }
);

const Company = mongoose.model("Company", companySchema);
export default Company;
