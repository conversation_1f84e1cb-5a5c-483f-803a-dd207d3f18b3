import nodemailer from "nodemailer";
import { htmlToText } from "html-to-text";
import path from "path";
import pug from "pug";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class Email {
  constructor(reciepient, emailPayload, ccRecipients = [], emailCategory) {
    this.to = reciepient.map((recipient) => recipient.email);
    this.cc = reciepient[0].cc || [];
    this.category = emailCategory;
    // this.orderId = user.orderId;
    // this.orderStatus = user.orderStatus;
    // this.url = url;
    // this.shipmetRef = user.shipmetRef,
    // this.currentStatus = user.currentStatus,
    // this.nextStatus = user.nextStatus,
    // this.actionByUser = user.actionByUser
    this.emailPayload = emailPayload;
    this.from = `Sunrise <${process.env.EMAIL_FROM}>`;
  }

  newTransport() {
    if (process.env.NODE_ENV === "production") {
      // Sendgrid
      return nodemailer.createTransport({
        service: "SendGrid",
        auth: {
          user: process.env.SENDGRID_USERNAME,
          pass: process.env.SENDGRID_PASSWORD,
        },
      });
    }

    return nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      auth: {
        user: process.env.EMAIL_USERNAME,
        pass: process.env.EMAIL_PASSWORD,
      },
    });
  }

  // Send the actual email
  async send(template, subject) {
    // 1) Render HTML based on a pug template
    // const templatePath = path.join(__dirname, '..', '..', `views/email/${template}.pug`);
    const templatePath = path.resolve(
      __dirname,
      "..",
      "..",
      "views",
      "email",
      `${template}.pug`
    );

    const html = pug.renderFile(templatePath, {
      firstName: this.firstName,
      // orderId: this.orderId,
      // orderStatus: this.orderStatus,
      // shipmetRef: this.shipmetRef,
      // currentStatus: this.currentStatus,
      // nextStatus: this.nextStatus,
      // actionByUser: this.actionByUser,
      // url: this.url,
      emailPayload: this.emailPayload,
      subject:
        this.category.toLowerCase() == "order"
          ? `${subject} - ${this.emailPayload.orderName} `
          : `${subject} - ${this.emailPayload.shipmentName}`,
    });

    // 2) Define email options
    const mailOptions = {
      from: this.from,
      to: this.to,
      subject,
      html,
      // text: htmlToText.fromString(html)
    };

    if (this.cc.length) {
      mailOptions.cc = this.cc;
    }

    const attachments = [];
    if (this.emailPayload.attachments) {
      for (const url of this.emailPayload.attachments) {
        attachments.push({
          filename: path.basename(url),
          path: url,
        });
      }
    }

    if (attachments.length > 0) {
      mailOptions.attachments = attachments;
    }

    // 3) Create a transport and send email
    await this.newTransport().sendMail(mailOptions);
  }

  async sendShipmentStatusChangeEmail() {
    console.log("TEST");
    await this.send(
      "shipmentStatusChange",
      "Shipment Status Change" + " " + (this.emailPayload.shipmentName || "")
    );
  }

  async sendOrderCreationEmail() {
    console.log("TEST");
    await this.send(
      "orderCreated",
      "New Order Created" + " " + (this.emailPayload.orderName || "")
    );
  }

  async sendShipmentCreationEmail() {
    console.log("TEST-----------------");
    await this.send(
      "shipmentCreated",
      "New Shipment Created" + " " + (this.emailPayload.shipmentName || "")
    );
  }

  async sendShipmentEscalationEmail() {
    console.log("TEST");
    await this.send(
      "shipmentEscalation",
      "Escalation Email" + " " + (this.emailPayload.shipmentName || "")
    );
  }

  async sendDistributorAccoutActivationEmail() {
    console.log("TEST");
    await this.send("accountActivation", "Account Activation");
  }

  async sendPiGenerationEmail() {
    console.log("TEST");
    await this.send(
      "piGenerated",
      "Pi Generated" + " " + (this.emailPayload.shipmentName || "")
    );
  }

  async sendOrderCreateNotifyManagersEmail() {
    console.log("TEST");
    await this.send(
      "orderCreatedNotifyManagers",
      "New order created " + " " + (this.emailPayload.orderName || "")
    );
  }
  async sendSlaBreachEmail() {
    console.log("TEST");
    await this.send(
      "slaBreach",
      "SLA Breach: Shipment Fulfillment Delay for Shipment" +
        " " +
        (this.emailPayload.shipmentName || "")
    );
  }

  async sendPasswordResetEmail() {
    await this.send("passwordReset", "Password Reset Request");
  }

  //order allocated
  async sendOrderAllocatedEmail() {
    await this.send(
      "orderAllocated",
      `Your Order ${this.emailPayload.orderId} Has Been Allocated`
    );
  }

  //order-invoiced
  async sendOrderInvoicedEmail() {
    await this.send(
      "orderInvoiced",
      `Your Invoice for Order ${this.emailPayload.orderId}`
    );
  }

  //out-for-delivery
  async sendOutForDeliveryEmail() {
    await this.send(
      "outForDelivery",
      `Your Order ${this.emailPayload.orderName} Is Out for Delivery!`
    );
  }

  //delivered
  async sendOrderDeliveredEmail() {
    await this.send(
      "delivered",
      `Your Order ${this.emailPayload.orderId} Has Been Delivered`
    );
  }

  //cancelled
  async sendOrderCancelledEmail() {
    await this.send(
      "orderCancelled",
      `⚠️ Your Order ${this.emailPayload.orderId} Has Been Cancelled`
    );
  }

  async sendCustomerApproval1Email() {
    await this.send(
      "customerApproval1",
      `Your Account Has Been Approved - Meet Your Dedicated  Sales Representative`
    );
  }

  async sendCustomerApproval2Email() {
    await this.send(
      "customerApproval2",
      `🎉 Your Account Is Ready – Start Placing Orders Today!`
    );
  }

  async sendStockSheetReady() {
    await this.send(
      "stockSheetReady",
      `Stock Sheet for ${this.emailPayload.customerName} is ready`
    );
  }
}
