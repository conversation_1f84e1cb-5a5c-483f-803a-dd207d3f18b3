extends baseEmail

block content
  p Hi #{emailPayload.customerName},

  p Good news! Your order ##{emailPayload.orderId} has been successfully allocated based on available stock.

  p 🛠️ Allocation Details:
  p Your detailed allocation sheet is ready!
    if emailPayload.attachments && emailPayload.attachments[0]
      a(href=emailPayload.attachments[0] style="color: #007bff; text-decoration: none; font-weight: bold;") 📊 Download Allocation Sheet
    else
      | (Allocation sheet is attached with product name, order qty, allocation qty, shipment number.)

  p We’ve confirmed item availability, and they’re now reserved for you.

  p Any remaining items will be updated shortly.

  p You’ll be notified once the order is invoiced and out for delivery.

  p For help, contact us at 
    a(href=`mailto:${emailPayload.salesPersonEmail}`) #{emailPayload.salesPersonEmail}.

  p Thanks for shopping with us!
  p - The #{emailPayload.storeName} Team
