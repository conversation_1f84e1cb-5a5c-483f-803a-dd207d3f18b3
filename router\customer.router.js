import express from "express";
import {
  ProcessGetCompanyDetailsFromCustomerId,
  ProcessUpdateCompanyDetails,
} from "../controller/companyS3.controller.js";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import { generateRandomNumber } from "../utils/helperFunction.js";

const router = express.Router();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "company"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".").pop();
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Renames the file
  },
});

const upload = multer({
  storage,
}).fields([
  { name: "form9" },
  { name: "form24" },
  { name: "form49" },
  { name: "memArticle" },
  { name: "icCard" },
  { name: "lan" },
  { name: "bankStatement" },
  { name: "formD" },
  { name: "form1" },
]);

router
  .route("/:id")
  .get(ProcessGetCompanyDetailsFromCustomerId)
  .post(upload, ProcessUpdateCompanyDetails);

export default router;
