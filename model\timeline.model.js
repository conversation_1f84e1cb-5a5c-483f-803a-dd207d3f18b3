import { UUID } from "mongodb";
import mongoose from "mongoose";

const timelineSchema = new mongoose.Schema({
  category: {
    type: String,
    required: true,
    enum: ['Order', 'Shipment', 'Other']
  },
  message: {
    type: String,
    required: true,
  },
  user: {
    type: String,
    default: "Test User",
    required: true
  },
  order: {
    type: mongoose.Schema.ObjectId,
    ref: 'Order'
  },
  shipment: {
    type: mongoose.Schema.ObjectId,
    ref: 'Shipment'
  },
  actionType: {
    type: String,
  },
  journey: {
    type: String,
  },
}, { timestamps: true });


timelineSchema.pre(/^find/, function (next) {
  this.populate({
    path: "order",
    // select: "-__v",
  }).populate({
    path: "shipment",
    // select: "-__v",
  }).lean();
  next();
});


const Timeline = mongoose.model("Timeline", timelineSchema);
export default Timeline;
