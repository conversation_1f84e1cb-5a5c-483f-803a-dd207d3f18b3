import express from 'express';
import {
  getDepartmentsType,
  createDepartmentType,
  getOneDepartmentType,
  updateDepartmentType,
  deleteDepartmentType
} from '../controller/departmentType.controller.js';

const router = express.Router();

router
  .route('/')
  .get(getDepartmentsType)
  .post(createDepartmentType);

router
  .route('/:id')
  .get(getOneDepartmentType)
  .patch(updateDepartmentType)
  .delete(deleteDepartmentType);

export default router;