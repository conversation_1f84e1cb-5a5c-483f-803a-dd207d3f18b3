import express from 'express';
import {
  getDesignations,
  createDesignation,
  getOneDesignation,
  updateDesignation,
  deleteDesignation,
  updateDesignationHierarchy,
  checkExistingDesignation
} from '../controller/designation.controller.js';

const router = express.Router();

router
  .route('/')
  .get(getDesignations)
  .post(checkExistingDesignation, createDesignation);

  router
  .route('/hierarchy/update')
  .patch(updateDesignationHierarchy);
  
router
  .route('/:id')
  .get(getOneDesignation)
  .patch(checkExistingDesignation, updateDesignation)
  .delete(deleteDesignation);

export default router;