import Inventory from "../../model/inventory.model.js";
import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";
import Company from "../../model/company.model.js";

const extractShopifyId = (gid) => gid.split("/").pop();

export const updateCustomerIdsForCompanies = async (companies) => {
  if (!companies || companies.length < 0) {
    return;
  }
  try {
    for (const company of companies) {
      let customerCursor = null;
      let allCustomerIds = [];

      while (true) {
        const shopifyCompanyId = `gid://shopify/Company/${company.shopifyCompanyId}`;

        const result = await fetchGraphqlDataShopify(
          GET_CUSTOMERS_FOR_COMPANY,
          {
            companyId: shopifyCompanyId,
            contactCursor: customerCursor,
          }
        );

        const customers = result.data.company?.contacts?.edges;
        const customerIds = customers?.map((edge) =>
          extractShopifyId(edge.node.customer.id)
        );
        allCustomerIds = allCustomerIds.concat(customerIds);

        customerCursor = result.data.company?.contacts.pageInfo.endCursor;
        if (!result.data.company?.contacts.pageInfo.hasNextPage) {
          break;
        }
      }

      await Company.updateOne(
        { _id: company._id },
        { $set: { customerIds: allCustomerIds } }
      );
    }
  } catch (err) {
    console.error("Error updating customer IDs:", err);
  }
};

export const updateLocationCatalogsForCompanies = async () => {
  try {
    // Fetch all companies from the database
    const companies = await Company.find({});
    const bulkOps = [];

    for (const company of companies) {
      let locationCursor = null;
      let allCatalogIds = [];
      const shopifyCompanyId = `gid://shopify/Company/${company.shopifyCompanyId}`;

      while (true) {
        // Fetch locations and catalogs for the company from Shopify
        const result = await fetchGraphqlDataShopify(
          GET_LOCATIONS_AND_CATALOGS,
          {
            companyId: shopifyCompanyId,
            locationCursor: locationCursor,
          }
        );

        // Extract catalog IDs from locations
        const locations = result.data.company?.locations.edges;
        for (const location of locations) {
          const catalogs = location.node.catalogs.edges;
          const catalogIds = catalogs.map((edge) =>
            extractShopifyId(edge.node.id)
          );
          allCatalogIds = allCatalogIds.concat(catalogIds);
        }

        // Pagination handling
        locationCursor = result.data.company?.locations.pageInfo.endCursor;
        if (!result.data.company?.locations.pageInfo.hasNextPage) {
          break;
        }
      }

      // Add the update operation to the bulkOps array
      bulkOps.push({
        updateOne: {
          filter: { _id: company._id },
          update: {
            $set: { companyLocationCatalogIds: allCatalogIds },
          },
          upsert: true,
        },
      });
    }

    if (bulkOps.length > 0) {
      // Execute the bulk operations
      const result = await Company.bulkWrite(bulkOps);
      console.log(
        "Bulk write operation successful for company location catalogs:",
        result
      );
    }
  } catch (err) {
    console.error("Error updating location catalogs:", err);
  }
};
export const updateInventoryPrices = async () => {
  try {
    // Fetch all companies from the database
    const companies = await Company.find({});
    const bulkOps = [];

    for (const company of companies) {
      const catalogIds = company.companyLocationCatalogIds;

      for (const catalogId of catalogIds) {
        let hasNextPage = true;
        let priceCursor = null;

        while (hasNextPage) {
          // Fetch prices for each catalog
          const result = await fetchGraphqlDataShopify(GET_PRICE_LIST, {
            catalog_id: `gid://shopify/CompanyLocationCatalog/${catalogId}`,
            priceCursor,
          });
          const prices = result.data.catalog.priceList.prices.nodes;
          const pageInfo = result.data.catalog.priceList.prices.pageInfo;

          // Prepare bulk update operations for each price item
          for (const priceItem of prices) {
            const { sku } = priceItem.variant;
            const { amount, currencyCode } = priceItem.price;

            bulkOps.push({
              updateOne: {
                filter: {
                  sku,
                  "priceList.companyLocationCatalogId": catalogId,
                },
                update: {
                  $set: {
                    "priceList.$.price": amount,
                    "priceList.$.currency": currencyCode,
                  },
                },
              },
            });

            bulkOps.push({
              updateOne: {
                filter: { sku },
                update: {
                  $addToSet: {
                    priceList: {
                      price: amount,
                      currency: currencyCode,
                      companyLocationCatalogId: catalogId,
                    },
                  },
                },
                upsert: true, // Insert document if it doesn't exist
              },
            });
          }

          hasNextPage = pageInfo.hasNextPage;
          priceCursor = pageInfo.endCursor;
        }
      }
    }

    if (bulkOps.length > 0) {
      // Execute the bulk operations
      const result = await Inventory.bulkWrite(bulkOps);
      console.log(
        "Bulk write operation successful for inventory prices:",
        result
      );
    }
  } catch (err) {
    console.error("Error updating inventory prices:", err);
  }
};

export const fetchAndSaveCompanies = async (companyCursor = null) => {
  let hasMoreCompanies = true;
  let bulkOps = [];

  while (hasMoreCompanies) {
    const result = await fetchGraphqlDataShopify(GET_COMPANIES, {
      companyCursor,
    });

    const companies = result.data.companies.edges;
    companyCursor = result.data.companies.pageInfo.endCursor;
    hasMoreCompanies = result.data.companies.pageInfo.hasNextPage;

    for (const companyEdge of companies) {
      const companyNode = companyEdge.node;
      const shopifyCompanyId = extractShopifyId(companyNode.id);

      bulkOps.push({
        updateOne: {
          filter: { shopifyCompanyId },
          update: {
            $set: { name: companyNode.name },
          },
          upsert: true,
        },
      });
    }

    if (bulkOps.length > 0) {
      try {
        const result = await Company.bulkWrite(bulkOps);
        console.log("Bulk write operation successful:", result);
        bulkOps = [];
      } catch (err) {
        console.error("Error during bulk write:", err);
      }
    }
  }

  const companies = await Company.find({});

  const customerUpdate = await updateCustomerIdsForCompanies(companies);
  const catalogsUpdate = await updateLocationCatalogsForCompanies(companies);
  const priceUpdate = await updateInventoryPrices(companies);
};

const GET_COMPANIES = `
  query getCompanies($companyCursor: String) {
    companies(first: 100, after: $companyCursor) {
      edges {
        node {
          id
          name
        }
      }
      pageInfo {
        endCursor
        hasNextPage
      }
    }
  }
`;
const GET_CUSTOMERS_FOR_COMPANY = `
  query getCustomersForCompany($companyId: ID!, $contactCursor: String) {
    company(id: $companyId) {
      contacts(first: 10, after: $contactCursor) {
        edges {
          node {
            customer {
              id
            }
          }
        }
        pageInfo {
          endCursor
          hasNextPage
        }
      }
    }
  }
`;
const GET_LOCATIONS_AND_CATALOGS = `
  query getLocationsAndCatalogs($companyId: ID!, $locationCursor: String) {
    company(id: $companyId) {
      locations(first: 10, after: $locationCursor) {
        edges {
          node {
            id
            catalogs(first: 1) {
              edges {
                node {
                  id
                }
              }
            }
          }
        }
        pageInfo {
          endCursor
          hasNextPage
        }
      }
    }
  }
`;
const GET_PRICE_LIST = `
  query PriceList($catalog_id: ID!, $priceCursor: String) {
    catalog(id: $catalog_id) {
      ... on CompanyLocationCatalog {
        id
        priceList {
          prices(originType: FIXED, first: 250, after: $priceCursor) {
            pageInfo {
              hasNextPage
              endCursor
            }
            nodes {
              price {
                amount
                currencyCode
              }
              variant {
                sku
              }
            }
          }
        }
      }
    }
  }
`;
