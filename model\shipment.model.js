import { UUID } from "mongodb";
import mongoose from "mongoose";

const productSchema = new mongoose.Schema({
  shopifyVariantId: {
    type: String,
    required: true,
  },
  shopifyProductId: {
    type: String,
    required: true,
  },
  productTitle: {
    type: String,
    required: true,
  },
  variantTitle: {
    type: String,
    default: "Default Title",
  },
  image: {
    type: String,
    // required: true,
  },
  requested: {
    type: Number,
    required: true,
  },
  fulfilled: {
    type: Number,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  sku: {
    type: String,
    required: true,
  },
  locationCode: {
    type: String,
    required: true,
  },
});

const escalationHistorySchema = new mongoose.Schema({
  status: {
    type: mongoose.Schema.ObjectId,
  },
  start: {
    type: Date,
  },
  end: {
    type: Date,
  },
});

const slaSchema = new mongoose.Schema(
  {
    days: {
      type: Number,
    },
    created_on: {
      type: Date,
    },
    statusEnded: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

const statusChangeHistorySchema = new mongoose.Schema(
  {
    status: {
      type: mongoose.Schema.ObjectId,
    },
  },
  { timestamps: true }
);

const shipmentSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      unique: true,
    },
    internalRef: {
      type: String,
      default: new UUID(),
    },
    ref: {
      type: String,
    },
    shopifyOrderId: {
      type: Number,
    },
    piSheet: {
      type: String,
    },
    piPdf: {
      type: String,
    },
    order: {
      type: mongoose.Schema.ObjectId,
      ref: "Order",
      required: true,
    },
    order_data: {
      type: Object,
    },
    erpIdentifier: {
      type: Number,
    },
    distributor: {
      type: mongoose.Schema.ObjectId,
      ref: "Distributor",
      required: true,
    },
    status: {
      type: mongoose.Schema.ObjectId,
      ref: "Status",
      required: true,
    },
    status_data: {
      type: Object,
    },
    sla: slaSchema,
    escalation_status: {
      type: Boolean,
      default: false,
    },
    escalation_history: {
      type: [escalationHistorySchema],
    },
    status_change_history: {
      type: [statusChangeHistorySchema],
    },
    isFuture: {
      type: Boolean,
      default: false,
    },
    initial_status: {
      type: String,
      // enum: ["aligned", "non-aligned"],
    },
    amount: {
      type: Number,
    },
    shipping_address: {
      type: Object,
    },
    lineItems: {
      type: [productSchema],
    },
    attributes: {
      type: Array,
      default: [],
    },
    timeline: {
      type: Array,
      default: [],
    },
  },
  { timestamps: true }
);

shipmentSchema.pre(/^find/, function (next) {
  this.populate({
    path: "status",
  })
    .populate({
      path: "order",
    })
    .lean();
  next();
});

const Shipment = mongoose.model("Shipment", shipmentSchema);
export default Shipment;
