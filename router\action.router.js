import express from "express";
import {
  authenticateDepttPeopleAccess,
  authenticateDepttPeopleAccessModuleWise,
} from "../middlewares/authenticateDepttPeople.js";
import {
  getAction,
  createAction,
  getOneAction,
  updateAction,
  deleteAction,
  changeStatusAction,
  triggerBatchProcessOrders,
  alignmentPendingOrderProcess,
} from "../controller/action.controller.js";
import multer from "multer";
import { fileURLToPath } from "url";
import path from "path";
import { generateRandomNumber } from "../utils/helperFunction.js";

const router = express.Router();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "..", "asset", "upload", "shipment"));
  },
  filename: (req, file, cb) => {
    const fileExtension = file.originalname.split(".")[1];
    const originalFileName = file.originalname.split(".")[0];
    cb(
      null,
      `${originalFileName
        .split(" ")
        .join("_")}_${generateRandomNumber()}.${fileExtension}`
    ); // Retains the original file name
  },
});

const upload = multer({ storage });

router.route("/").get(getAction).post(createAction);

router.post(
  "/change_status",
  authenticateDepttPeopleAccess(),
  upload.single("file"),
  changeStatusAction
);
router.post(
  "/autoAllocate",
  authenticateDepttPeopleAccessModuleWise("order", "autoAllocate"),
  upload.single("file"),
  triggerBatchProcessOrders
);
router.post("/process_pending_orders", alignmentPendingOrderProcess);

router.route("/:id").get(getOneAction).patch(updateAction).delete(deleteAction);

export default router;
