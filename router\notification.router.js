import express from 'express';
import {
  getEmailNotifications,
  getOneEmailNotification,
  createEmailNotification,
  updateEmailNotification,
  deleteEmailNotification,
} from '../controller/notification.controller.js';

const router = express.Router();

router
  .route('/')
  .get(getEmailNotifications)
  .post(createEmailNotification);

router
  .route('/:id')
  .get(getOneEmailNotification)
  .patch(updateEmailNotification)
  .delete(deleteEmailNotification);

export default router;