const CHECK_CUSTOMER_EXISTENCE_BY_ID = `query CheckCustomerExistence($id: ID!) {
  customer(id: $id) {
    id
    email
    companyContactProfiles {
      id
      company {
        id
        mainContact {
          isMainContact
        }
      }
    }
  }
}
`;

const GET_COMPANY_BY_ID = `query GetCompanyByID($id: ID!) {
  company(id: $id) {
    id
    name
    metafields(first: 250) {
      edges {
        node {
          id
          key
          namespace
          type
          value
        }
      }
    }
    contacts(first: 1) {
      edges {
        node {
          customer {
            id
            firstName
            lastName
            email
            phone
          }
        }
      }
    }
  }
}
`;

const CREATE_COMPANY_BY_NAME = `mutation CreateCompany($name: String!, $paymentTermsTemplateId: ID!) {
  companyCreate(
    input: {
      company: { name: $name },
      companyLocation: {
        buyerExperienceConfiguration: {
          checkoutToDraft: false,
          editableShippingAddress: false,
          paymentTermsTemplateId: $paymentTermsTemplateId
        }
      }
    }
  ) {
    company {
      id
      name
    }
    userErrors {
      field
      message
    }
  }
}
`;

const UPDATE_CUSTOMER = `mutation UpdateCustomer(
  $id: ID!
  $firstName: String!
  $lastName: String
  $email: String!
  $phone: String!
) {
  customerUpdate(
    input: {
      id: $id
      firstName: $firstName
      lastName: $lastName
      email: $email
      phone: $phone
    }
  ) {
    customer {
      id
    }
    userErrors {
      message
      field
    }
  }
}
`;

const COMPANY_ASSIGN_CUSTOMER = `mutation companyAssignCustomerAsContact($companyId: ID!, $customerId: ID!) {
  companyAssignCustomerAsContact(
    companyId: $companyId
    customerId: $customerId
  ) {
    companyContact {
      id
      isMainContact
    }
    userErrors {
      field
      message
    }
  }
}
`;

const ASSIGN_MAIN_CONTACT = `mutation companyAssignMainContact($companyContactId: ID!, $companyId: ID!) {
  companyAssignMainContact(
    companyContactId: $companyContactId
    companyId: $companyId
  ) {
    company {
      id
    }
    userErrors {
      field
      message
    }
  }
}
`;

const UPDATE_COMPANY_NAME = `mutation companyAssignMainContact($companyId: ID!,$name: String!) {
  companyUpdate(companyId: $companyId, input: {name: $name}) {
    company {
      id
      name
    }
    userErrors {
      field
      message
    }
  }
}

`;

//not used
const GET_COMPANY_CONTACT_BY_COMPANY_ID = `query GetCompanyByID($id: ID!) {
  company(id: $id) {
    id
    contacts(first: 1) {
      edges {
        node {
          roleAssignments(first: 1) {
            edges {
              node {
                companyContact {
                  id
                  roleAssignments(first: 1) {
                    edges {
                      node {
                        id
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
`;

export {
  CHECK_CUSTOMER_EXISTENCE_BY_ID,
  GET_COMPANY_BY_ID,
  CREATE_COMPANY_BY_NAME,
  UPDATE_CUSTOMER,
  COMPANY_ASSIGN_CUSTOMER,
  ASSIGN_MAIN_CONTACT,
  UPDATE_COMPANY_NAME
};
