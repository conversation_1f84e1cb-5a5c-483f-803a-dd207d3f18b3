import mongoose from "mongoose";

const actionSchema = new mongoose.Schema(
  {
    action_name: {
      type: String,
      required: true,
      unique: true,
    },
    route: {
      type: String,
      required: true,
      unique: true,
    },
    action_unique_key: {
      type: String,
      required: true,
      unique: true,
    },
  },
  { timestamps: true }
);

const Action = mongoose.model("Action", actionSchema);
export default Action;
