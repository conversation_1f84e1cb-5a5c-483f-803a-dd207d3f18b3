import EmailNotification from "../../model/emailNotification.model.js";
import { Email } from "../rule/index.js";
import Shipment from "../../model/shipment.model.js";
import Status from "../../model/status.model.js";
import Department from "../../model/department.model.js";
import { generateXlsxSheet } from "../../utils/lineSheetFileDownload.js";
import { getDepartmentForCreated } from "../../utils/findNotifiedDepartment.js";
import Distributor from "../../model/distributor.model.js";

export const emailSendCron = async () => {
  const emailNotifications = await EmailNotification.find({
    isProcessed: false,
  }).limit(10);
  for (const emailNotification of emailNotifications) {
    await EmailNotification.updateOne(
      { _id: emailNotification._id },
      { isProcessed: true }
    );
    if (!emailNotification.reciepient || !emailNotification.emailPayload) {
      await EmailNotification.updateOne(
        { _id: emailNotification._id },
        {
          note: "Missing reciepients or email payload",
          tag: "MROP",
          isProcessed: false,
        }
      );
    }
    try {
      if (emailNotification.emailType == "ORDER_CREATE") {
        console.log(
          "SENDING EMAIL FOR ORDER_CREATE",
          emailNotification?.reciepient
        );
        const link = await generateXlsxSheet(
          emailNotification?.emailPayload.cartProducts,
          process.env.AWS_S3_BUCKET,
          "order"
        );
        emailNotification.emailPayload.attachments = [link.downloadUrl];
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendOrderCreationEmail();
      }
      if (emailNotification.emailType == "SHIPMENT_CREATE") {
        console.log(
          "SENDING EMAIL FOR SHIPMENT_CREATE",
          emailNotification?.reciepient
        );
        const link = await generateXlsxSheet(
          emailNotification?.emailPayload.cartProducts,
          process.env.AWS_S3_BUCKET,
          "shipment"
        );
        emailNotification.emailPayload.attachments = [link.downloadUrl];
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendShipmentCreationEmail();
      }
      if (emailNotification.emailType == "SHIPMENT_STATUS_CHANGE") {
        console.log("SENDING EMAIL FOR SHIPMENT_STATUS_CHANGE");
        const link = await generateXlsxSheet(
          emailNotification?.emailPayload.cartProducts,
          process.env.AWS_S3_BUCKET,
          "shipment"
        );
        emailNotification.emailPayload.attachments = [link.downloadUrl];
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendShipmentStatusChangeEmail();
      }
      if (emailNotification.emailType == "SHIPMENT_ESCALATION") {
        console.log("SENDING EMAIL FOR SHIPMENT_ESCALATION");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendShipmentEscalationEmail();
      }
      if (emailNotification.emailType == "CUSTOMER_ACCOUNT_ACTIVATION") {
        console.log("SENDING EMAIL FOR ACCOUNT_ACTIVATION");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendDistributorAccoutActivationEmail();
      }
      if (emailNotification.emailType == "PI_GENERATED") {
        console.log("SENDING EMAIL FOR PI_GENERATED");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendPiGenerationEmail();
      }
      if (emailNotification.emailType == "ORDER_CREATE_NOTIFY_MANAGER") {
        console.log("SENDING EMAIL FOR ORDER_CREATE_NOTIFY_MANAGER");
        const link = await generateXlsxSheet(
          emailNotification?.emailPayload.cartProducts,
          process.env.AWS_S3_BUCKET,
          "shipment"
        );
        emailNotification.emailPayload.attachments = [link.downloadUrl];
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendOrderCreateNotifyManagersEmail();
      }
      if (emailNotification.emailType == "SLA_BREACH") {
        console.log("SENDING EMAIL FOR SLA_BREACH");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendSlaBreachEmail();
      }

      //order allocated
      if (emailNotification.emailType == "ORDER_CREATED_IN_SAGE") {
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendOrderAllocatedEmail();
      }

      //order-invoiced
      if (emailNotification.emailType == "ORDER_INVOICED") {
        console.log("SENDING EMAIL FOR ORDER_INVOICED");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendOrderInvoicedEmail();
      }

      //out-for-delivery
      if (emailNotification.emailType == "ORDER_OUT_FOR_DELIVERY") {
        console.log("SENDING EMAIL FOR ORDER_OUT_FOR_DELIVERY");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendOutForDeliveryEmail();
      }

      //delivered
      if (emailNotification.emailType == "ORDER_DELIVERED") {
        console.log("SENDING EMAIL FOR ORDER_DELIVERED");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendOrderDeliveredEmail();
      }

      //cancelled
      if (emailNotification.emailType == "ORDER_CANCELLED") {
        console.log("SENDING EMAIL FOR ORDER_CANCELLED");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendOrderCancelledEmail();
      }

      //CUSTOMER_APPROVAL1
      if (emailNotification.emailType == "CUSTOMER_APPROVAL_1") {
        console.log("SENDING EMAIL FOR CUSTOMER_APPROVAL_1");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendCustomerApproval1Email();
      }

      //CUSTOMER_APPROVAL2
      if (emailNotification.emailType == "CUSTOMER_APPROVAL_2") {
        console.log("SENDING EMAIL FOR CUSTOMER_APPROVAL_2");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendCustomerApproval2Email();
      }
      if (emailNotification.emailType == "STOCK_SHEET_READY") {
        console.log("SENDING EMAIL FOR STOCK_SHEET_READY");
        await new Email(
          emailNotification?.reciepient,
          emailNotification?.emailPayload,
          emailNotification?.reciepient.cc,
          emailNotification?.emailCategory
        ).sendStockSheetReady();
      }
    } catch (error) {
      console.log(error);
      await EmailNotification.updateOne(
        { _id: emailNotification._id },
        { isProcessed: false }
      );
    }
  }
};

export const generateEscalationNotification = async () => {
  const shipments = await Shipment.find({});
  for (const shipment of shipments) {
    if (
      shipment.status_change_history &&
      shipment.status_change_history.length > 0
    ) {
      const latestStatusChange = shipment.status_change_history.sort((a, b) => {
        return new Date(b.createdAt) - new Date(a.createdAt);
      })[0];
      const statusDetails = await Status.findById(
        latestStatusChange.status
      ).lean();

      if (statusDetails && statusDetails.escalationPeriod) {
        const escalationPeriodEndDate = new Date(latestStatusChange.createdAt);
        escalationPeriodEndDate.setDate(
          escalationPeriodEndDate.getDate() + statusDetails.escalationPeriod
        );

        const isOverEscalationPeriod = new Date() > escalationPeriodEndDate;

        if (isOverEscalationPeriod) {
          const escalationDepartment = statusDetails.escalationDepartment;

          // const departmentPeoples = await Department.find({
          //   _id: escalationDepartment._id,
          // });
          const departmentPeoples = await Department.find({
            departmentType: escalationDepartment._id,
          });

          const departmentDetails = await getDepartmentForCreated(
            statusDetails._id
          );

          if (shipment.escalation_status === false) {
            shipment.escalation_status = true;
            shipment.escalation_history.push({
              status: statusDetails._id,
              start: new Date(),
              end: null,
            });

            await shipment.save();
          }

          const distributerId = shipment.order.distributor._id;
          const distributor = await Distributor.findOne({
            _id: distributerId,
          });

          const recipients = departmentPeoples.map((departmentPeople) => ({
            name: departmentPeople.name,
            email: departmentPeople.email,
            cc: [...departmentDetails],
          }));

          // Check if recipients array is blank
          if (recipients.length > 0) {
            await EmailNotification.create({
              emailCategory: "SHIPMENT",
              emailType: "SHIPMENT_ESCALATION",
              reciepient: recipients,
              emailPayload: {
                orderName: shipment.order.name,
                distributorName: distributor.name,
                date: shipment.order.createdAt,
                department: escalationDepartment.department,
                shipmentName: shipment.name,
                shipmentRef: shipment.ref,
                lastStatus: statusDetails.status,
                lastStatusChangeDate: latestStatusChange.createdAt,
              },
            });
          }

          console.log(
            `Shipment ${
              shipment.name
            } is over the escalation period by ${Math.floor(
              (new Date() - escalationPeriodEndDate) / (1000 * 60 * 60 * 24)
            )} days.`
          );
        }
      }
    } else {
      console.log(`Shipment ${shipment.name} has no status change history.`);
    }
  }
};

export const checkSLABreach = async () => {
  try {
    const currentDate = new Date();

    const shipments = await Shipment.find({
      sla: { $exists: true },
      "sla.statusEnded": false,
    });

    if (!shipments || shipments.length === 0) {
      console.log("No shipments found with slaStatus breach.");
      return;
    }

    for (let i = 0; i < shipments.length; i++) {
      const shipment = shipments[i];

      if (shipment.sla.createdAt && shipment.sla.days) {
        const slaCreatedDate = new Date(shipment.sla.createdAt);
        slaCreatedDate.setDate(slaCreatedDate.getDate() + shipment.sla.days);

        if (currentDate > slaCreatedDate) {
          const delayDays = Math.floor(
            (currentDate - slaCreatedDate) / (1000 * 60 * 60 * 24)
          );

          const statusDetails = await Status.findById(shipment.status);
          const escalationDepartment = statusDetails.escalationDepartment;

          const departmentPeoples = await Department.find({
            departmentType: escalationDepartment._id,
          });

          const departmentDetails = await getDepartmentForCreated(
            statusDetails._id
          );

          const distributerId = shipment.order.distributor._id;
          const distributor = await Distributor.findOne({ _id: distributerId });

          const recipients = departmentPeoples.map((departmentPeople) => ({
            name: departmentPeople.name,
            email: departmentPeople.email,
            cc: [...departmentDetails],
          }));

          if (recipients.length > 0) {
            await EmailNotification.create({
              emailCategory: "SHIPMENT",
              emailType: "SLA_BREACH",
              reciepient: recipients,
              emailPayload: {
                orderName: shipment.order.name,
                distributorName: distributor.name,
                shipmentName: shipment.name,
                fulfillmentDays: shipment.sla.days,
                delayDays: delayDays,
              },
            });
          }

          console.log(
            `SLA Breach! Shipment ID: ${shipment._id}, SLA Deadline: ${slaCreatedDate}`
          );
        }
      }
    }
  } catch (error) {
    console.error("Error checking SLA breach:", error);
  }
};
