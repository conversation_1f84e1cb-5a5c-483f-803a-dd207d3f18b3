import { UUID } from "mongodb";
import mongoose from "mongoose";
import AppError from "../utils/appError.js";
import { toScreamingSnakeCase } from "../utils/helperFunction.js";

const statusSchema = new mongoose.Schema(
  {
    pseudoId: {
      type: String,
      // index: true,
    },
    status: {
      type: String,
      required: true,
    },
    isInitialStatus: {
      type: Boolean,
      default: false,
    },
    statusType: {
      type: String,
      required: true,
      enum: ["Order", "Shipment"],
    },
    departmentNotified: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "DepartmentType",
        required: true,
      },
    ],
    departmentType: {
      type: mongoose.Schema.ObjectId,
      ref: "DepartmentType",
      required: true,
    },
    escalationDepartment: {
      type: mongoose.Schema.ObjectId,
      ref: "DepartmentType",
      required: true,
    },
    escalationTriggerPeriod: {
      type: Number,
    },
    customFields: [
      {
        fieldId: {
          type: String,
          default: new UUID(),
        },
        fieldLabel: {
          type: String,
        },
        fieldType: {
          type: String,
          enum: ["Text", "Number", "File", "True/False", "JSON"],
        },
        fieldKey: {
          type: String,
        },
      },
    ],
    colorCode: {
      type: String,
    },
    flowType: {
      type: String,
      required: true,
      enum: ["happy", "unhappy"],
    },
  },
  { timestamps: true }
);

statusSchema.pre(/^find/, function (next) {
  this.populate({
    path: "departmentType",
    // select: "-__v",
  })
    .populate({
      path: "departmentNotified",
      select: "department",
    })
    .populate({
      path: "escalationDepartment",
      select: "department",
    })
    .lean();
  next();
});

statusSchema.pre("save", async function (next) {
  const isExistingInitialStatus = await Status.findOne({
    statusType: this.statusType,
    isInitialStatus: true,
  });
  if (isExistingInitialStatus && this.isInitialStatus) {
    return next(
      new AppError(
        `Cannot create two Initial Status for [${this.statusType}], Initial Status [${isExistingInitialStatus.status}] already exist`
      )
    );
  }
  if (!isExistingInitialStatus && !this.isInitialStatus) {
    return next(
      new AppError(
        `There is no Initial Status for [${this.statusType}] create one by passing isInitialStatus:true in Payload`
      )
    );
  }
  this.pseudoId = toScreamingSnakeCase(this.status);
  next();
});

const Status = mongoose.model("Status", statusSchema);
export default Status;
