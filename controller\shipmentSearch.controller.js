import mongoose from "mongoose";
import Shipment from "../model/shipment.model.js";
import Distributor from "../model/distributor.model.js";

const ObjectId = mongoose.Types.ObjectId;

export async function shipmentSearch(req, res) {
  try {
    const {
      input,
      distributor,
      shipmentStatus,
      fromDate,
      toDate,
      flowType,
      page = 1,
      pageSize = 10,
    } = req.body;
    const salesPersonDept = req.salesPersonDept;
    const user = req.user;

    const aggregateQuery = [];
    let hasFilters = false;
    let distributorIds = [];

    if (salesPersonDept) {
      // Get distributors for the salesperson
      distributorIds = await Distributor.distinct("_id", {
        salespersonId: user._id.toString(),
      });

      // If no distributors found, return an empty response
      if (!distributorIds.length) {
        return res.send({ shipments: [], total: 0, page, pageSize });
      }

      // Directly match shipments belonging to the salesperson's distributors
      aggregateQuery.push({
        $match: {
          distributor: { $in: distributorIds },
        },
      });
    }

    if (input && input.trim()) {
      hasFilters = true;
      aggregateQuery.push({
        $match: {
          $or: [
            { name: new RegExp(input, "i") },
            { status: new RegExp(input, "i") },
            { distributor: new RegExp(input, "i") },
            { flowType: new RegExp(input, "i") },
            { createdAt: new RegExp(input, "i") },
          ],
        },
      });
    }

    // Add flowType filter condition
    if (flowType && flowType.trim()) {
      hasFilters = true;
      aggregateQuery.push({
        $match: { flowType: flowType.toLowerCase() },
      });
    }

    // Add date range filter conditions
    if (fromDate && toDate) {
      hasFilters = true;
      aggregateQuery.push({
        $match: {
          createdAt: {
            $gte: new Date(fromDate),
            $lte: new Date(toDate),
          },
        },
      });
    }

    // Add shipmentStatus filter condition
    if (shipmentStatus && shipmentStatus.length > 0) {
      hasFilters = true;
      const shipmentStatusArrayWithObjectId = shipmentStatus.map(
        (value) => new ObjectId(value)
      );
      aggregateQuery.push({
        $match: { status: { $in: shipmentStatusArrayWithObjectId } },
      });
    }

    // Add distributor filter condition
    if (distributor && distributor.length > 0) {
      hasFilters = true;
      const distributorObjectIdArray = distributor.map(
        (value) => new ObjectId(value)
      );
      aggregateQuery.push({
        $match: { distributor: { $in: distributorObjectIdArray } },
      });
    }

    // Add lookup stages for status and distributor
    aggregateQuery.push(
      {
        $lookup: {
          from: "status", // Name of the status collection
          localField: "status",
          foreignField: "_id",
          as: "status",
        },
      },
      {
        $unwind: { path: "$status", preserveNullAndEmptyArrays: true },
      }
    );

    // Pagination
    const skip = (page - 1) * pageSize;
    const limit = pageSize;

    if (hasFilters || salesPersonDept) {
      // Run the aggregation query with pagination if there are filters
      aggregateQuery.push({ $sort: { createdAt: -1 } });
      aggregateQuery.push({ $skip: skip });
      aggregateQuery.push({ $limit: limit });

      const shipments = await Shipment.aggregate(aggregateQuery).exec();
      const totalCountAggregate = await Shipment.aggregate([
        ...aggregateQuery.slice(0, -2), // Exclude pagination stages for counting
        { $count: "total" },
      ]).exec();
      const total = totalCountAggregate[0]?.total || 0;

      res.send({ shipments, total, page, pageSize });
    } else {
      // Fetch all data with pagination if no filters are provided
      const shipments = await Shipment.aggregate([
        {
          $lookup: {
            from: "status", // Name of the status collection
            localField: "status",
            foreignField: "_id",
            as: "status",
          },
        },
        {
          $unwind: { path: "$status", preserveNullAndEmptyArrays: true },
        },

        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: limit },
      ]).exec();
      const total = await Shipment.countDocuments().exec();

      res.send({ shipments, total, page, pageSize });
    }
  } catch (error) {
    res
      .status(500)
      .send({ error: "Something went wrong.", message: error.message });
  }
}
