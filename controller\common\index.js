import Department from '../../model/department.model.js';
import Order from '../../model/order.model.js';
import Shipment from '../../model/shipment.model.js';
import Status from '../../model/status.model.js';
import { Email } from "../../service/rule/index.js";
import AppError from '../../utils/appError.js';

export const notifyStatusChange = async (req, res, next) => {
  const shipmentId = req.params.id
  const statusId = req.body.status
  const statusDetail = await Status.findOne({_id: statusId})
  const shipment = await Shipment.findOne({_id: shipmentId})
  console.log(shipment.order.attributes)
  const attributes = shipment.order.attributes
  const sendEmailAttributes = attributes.find(attribute => {
    return attribute.type == 'SEND_EMAIL'
  })
  console.log('statusDetail', statusDetail)
  console.log('statusId', statusId)
  console.log('sendEmailAttributes', sendEmailAttributes)

  const filteredAttribute = sendEmailAttributes.value.find(attribute => {
    console.log('attribute.status', attribute.status)
    console.log('statusDetail.status', statusDetail.status)
    return attribute.category == 'Shipment' && attribute.status == statusDetail.status
  })
  console.log(filteredAttribute)
  
  for(const emailDetail of (filteredAttribute?.emailInfo || [])) {
    if(emailDetail?.name || emailDetail?.email) {
      await new Email({ name: emailDetail?.name, email: emailDetail?.email, orderId: shipment._id, orderStatus: statusDetail.status }, 'http://google.com').sendTest();
    }
  }

  //TODO: MAKE ORDER AUTOAPPROVED DYNAMIC
  if(statusDetail.pseudoId == 'SHIPMENT_DELIVERED') {
    const orderStatusDetail = await Status.findOne({statusType: 'Order', pseudoId: 'ORDER_COMPLETED'})
    await Order.updateOne({_id: shipment.order._id}, {status: orderStatusDetail._id})
  }

  
  console.log(sendEmailAttributes)

  // const isStatusChanged = req.body.isStatusChanged;
  // const statusId = req.body.status;
  // const statusType = req.body.middlewareStatusCategory
  // const orderId = req.body.orderId
  // const shipmentId = req.body.shipmentId
  // if(!orderId && statusType == 'Order') {
  //   return next(new AppError('Missing one of the following field [orderId, status]'))
  // }
  // if(!shipmentId && statusType == 'Shipment') {
  //   return next(new AppError('Missing one of the following field [shipmentId, status]'))
  // }
  // let statusQuery = {_id: statusId}
  // if(!statusId) {
  //   statusQuery = {status: req.body.status}
  // }
  // if(statusType) {
  //   statusQuery = {...statusQuery, statusType: statusType}
  // }
  // console.log('statusQuery', statusQuery)
  // if (isStatusChanged) {
  //   const status = await Status.findOne(statusQuery);
  //   console.log(status, " statussssss")
  //   console.log(status?.departmentType?._id, " status?.departmentTypesssss")
  //   const departmentPeoples = await Department.find({ departmentType: status?.departmentType?.id });
  //   // console.log(departmentPeoples, " departmentPeoples")
  //   for (const departmentPeople of departmentPeoples) {
  //     await new Email({ name: departmentPeople.name, email: departmentPeople.email, orderStatus: status.status, orderId: orderId || shipmentId }, 'http://google.com').sendTest();
  //   }
  // }
  next();
};

//TODO
//!Send Email Production
/** Approach, save all the data that will used to send mail in notification Model
 find the people of departmet with multiple reporting to
 and keep the seniors in cc and send mail to original person, the email of original person can e duplicate as 
 he might report more than one person, so need to add remove duplicate email logic,**/

//! Send ESCLATION Email

/**
Find place to save escelation information and in notification model save 
**/

//! Department PEOPLE
/* Add Reporting to as mongoId and self referencing */
