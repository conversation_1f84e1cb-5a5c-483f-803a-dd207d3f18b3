class NewAppError extends Error {
  constructor(code, message, statusCode) {
    super(message);
    this.code = code || "SERVER_ERROR";
    this.message = message || "Something went wrong.";
    this.statusCode = statusCode || 500;
    this.status = `${this.statusCode}`.startsWith("4") ? "fail" : "error";
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
  format() {
    return {
      responseCode: 1,
      status: this.status,
      errors: [
        {
          code: this.code,
          message: this.message,
        },
      ],
    };
  }
}
export default NewAppError;
