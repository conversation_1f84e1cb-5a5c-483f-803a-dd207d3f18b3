import xlsx from "xlsx";

export const generateExcelSheet = (data, headers) => {
  try {
    // Create workbook and worksheet
    const workbook = xlsx.utils.book_new();

    // Initialize sheet with header labels
    const headerRow = headers.map(header => header.label || header);
    const sheet = [headerRow];

    // Add data rows
    data.forEach((item) => {
      const row = headers.map((header) => {
        if (typeof header === "string") {
          // If header is a string, use it directly as key
          return item[header] || "";
        }

        // Handle nested object properties using dot notation
        if (header?.key) {
          const props = header.key.split(".");
          let value = item;

          // Traverse through nested objects
          for (const prop of props) {
            value = value?.[prop];
          }

          return value || "";
        }

        return ""; // Return empty string if header is invalid
      });

      sheet.push(row);
    });

    // Convert sheet array to worksheet
    const worksheet = xlsx.utils.aoa_to_sheet(sheet);

    // Add worksheet to workbook
    xlsx.utils.book_append_sheet(workbook, worksheet, "Sheet1");

    // Generate buffer
    const buffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    return {
      success: true,
      buffer,
      workbook,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};
