import axios from "axios";
import mongoose from "mongoose";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import StatusActionLink from "../model/statusActionLink.model.js";
import Action from "../model/action.model.js";
import Shipment from "../model/shipment.model.js";
import Order from "../model/order.model.js";
import Status from "../model/status.model.js";
import Inventory from "../model/inventory.model.js";
import {
  createOne,
  deleteOne,
  getAll,
  getOne,
  updateOne,
} from "../utils/controllerFactory.js";
import { Email } from "../service/rule/index.js";
import Department from "../model/department.model.js";
import EmailNotification from "../model/emailNotification.model.js";

import { uploadToS3 } from "../utils/helperFunction.js";
import DepartmentType from "../model/departmentType.model.js";
import {
  getDepartmentNotifiedEmails,
  getDepartmentForCreated,
} from "../utils/findNotifiedDepartment.js";
import Distributor from "../model/distributor.model.js";

import { createERPOrderController } from "../controller/erp.order.controller.js";
import NewAppError from "../utils/newAppError.js";
import { getErpItemsAvailableInventoryUtility } from "../utils/ERPUtils/erpItemUtils.js";
import xlsx from "xlsx";
import { addErpIdentifierOrderDbService } from "../service/orderService.js";
import { generateXlsxSheet } from "../utils/lineSheetFileDownload.js";
import {
  generateErrorSheetWithExcelJS,
  generateExcelSheetWithExcelJS,
} from "../utils/excelHelper.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const createOrderCreatedInSageEmailNotification = async (
  subOrderId,
  orderType = "regular"
) => {
  try {
    const orderWithDistributor = await Order.findOne({ subOrderId })
      .populate("distributor")
      .lean();
    if (!orderWithDistributor) {
      return;
    }

    const salesPerson = await Department.findById(
      orderWithDistributor.distributor.salespersonId
    );
    if (!salesPerson) {
      return;
    }

    if (orderWithDistributor?.distributor) {
      // Prepare sheet data with required details
      const sheetData = orderWithDistributor.line_items.map((item, index) => ({
        "S.No": index + 1,
        "Product Name": item.productTitle || item.title || "N/A",
        SKU: item.sku || "N/A",
        "Order Qty": item.quantity || 0,
        "Allocation Qty": item.allocatedQuantity || item.quantity || 0,
        "Shipment Number":
          orderWithDistributor.shipmentNumber ||
          orderWithDistributor.name ||
          "N/A",
        "Unit Price": item.price || 0,
        "Total Price": (item.price || 0) * (item.quantity || 0),
        "Order Date": new Date(
          orderWithDistributor.createdAt
        ).toLocaleDateString(),
        "Order Type": orderType || "regular",
      }));

      // Generate Excel sheet with signed URL using new helper
      const signedSheetUrl = await generateExcelSheetWithExcelJS(
        sheetData,
        `order_sage_details_${Date.now()}`, // filename for S3 upload
        {}, // no special column colors for this sheet
        "Order Details"
      );

      const recipients = [
        {
          name: orderWithDistributor.distributor.name,
          email: orderWithDistributor.distributor.email,
          // cc: [salesPerson?.email],
          cc: [],
        },
      ];

      await EmailNotification.create({
        emailCategory: "ORDER",
        emailType: "ORDER_CREATED_IN_SAGE",
        reciepient: recipients,
        orderType: orderType,
        emailPayload: {
          orderId: orderWithDistributor.name,
          customerName: orderWithDistributor.distributor.name,
          salesPersonEmail: salesPerson?.email,
          storeName: "Sunrise Trade",
          totalItems: orderWithDistributor.line_items?.length || 0,
          orderDate: new Date(
            orderWithDistributor.createdAt
          ).toLocaleDateString(),
          attachments: [signedSheetUrl], // Attach the generated sheet with signed URL
        },
      });
    }
  } catch (emailError) {
    // Email notification creation failed
  }
};

export const getAction = getAll(Action);
export const getOneAction = getOne(Action);
export const createAction = createOne(Action);
export const updateAction = updateOne(Action);
export const deleteAction = deleteOne(Action);

export const changeStatusAction = async (req, res) => {
  const { action_link_id, shipment_id, form_data } = req.body;
  const current_user = req.body.current_user || req.user;

  try {
    if (!action_link_id || !shipment_id) {
      return res.send({ error: "Action link id and shipment id is mandatory" });
    }
    const shipment = await Shipment.findById(shipment_id).populate();

    if (!shipment) {
      return res.send({ error: "Shipment not found with id:" + shipment_id });
    }
    const statusActionLinkData = await StatusActionLink.findById(
      action_link_id
    );

    if (!statusActionLinkData) {
      return res.send({ error: "statusActionLinkData not present" });
    }

    const totalAmount = shipment?.lineItems?.reduce(
      (sum, item) => sum + item.requested * item.price,
      0
    );

    const salesOrderPriceField = statusActionLinkData?.formData?.find(
      (data) => data.priceValidation === true
    );

    if (salesOrderPriceField) {
      const salesOrderPriceValue = form_data.find(
        (item) => item?.input_id === salesOrderPriceField?._id
      );

      const salesOrderPrice = parseFloat(salesOrderPriceValue.value);

      // Compare salesOrderPrice with totalAmount
      if (Math.abs(totalAmount - salesOrderPrice) >= 1) {
        return res.send({
          error:
            "Difference between actual price and sales order price should be less than 1.",
        });
      }
    }

    const current_shipment_status = shipment.status._id;
    const current_actionable_deptt =
      shipment.status.departmentType._id.toString();
    const current_user_deptt_ids = current_user.departmentType.map((dept) =>
      dept._id.toString()
    );

    if (!current_user_deptt_ids.includes(current_actionable_deptt)) {
      return res.send({ error: "You aren't authorized" });
    }

    if (
      current_shipment_status.toString() !==
      statusActionLinkData.current_status_id.toString()
    ) {
      return res.send({ error: "Invalid status change request" });
    }

    //this id is of Allocate inventory action link
    if (action_link_id === "6645e2c10f7a9ff7099cad53") {
      const error = await holdManualAllocatedInventory(shipment.lineItems);
      if (error) {
        return res.send({ error: "Insufficient quantity" });
      }
      const updatedShipment = await Shipment.findByIdAndUpdate(
        shipment._id,
        { "sla.statusEnded": true },
        { new: true }
      );
    }

    const formSchema = statusActionLinkData.formData;

    let fileUrl = null;
    if (req.file) {
      const filePath = path.join(
        __dirname,
        "..",
        "asset",
        "upload",
        "shipment",
        req.file.filename
      );

      try {
        const uploadResult = await uploadToS3(
          filePath,
          process.env.AWS_S3_BUCKET,
          req.file.filename
        );
        fileUrl = uploadResult.Location; // Get the S3 URL

        // Optionally delete the local file after upload
        fs.unlinkSync(filePath);
      } catch (err) {
        console.error("S3 Upload Error:", err);
        return res.send({ error: "File upload failed" });
      }
    }

    if (shipment.escalation_status) {
      await endEscalationStatus(shipment);
    }

    const formSchemaLookup = formSchema.reduce((acc, item) => {
      acc[item._id] = item.input_name;
      return acc;
    }, {});

    const processedFormData = JSON.parse(form_data)?.map((item) => {
      const inputName = formSchemaLookup[item.input_id] || item.input_id;
      // Check if the value is empty (null, undefined, empty object, empty string, etc.)
      if (
        !item.value ||
        (typeof item.value === "object" && Object.keys(item.value).length === 0)
      ) {
        return {
          ...item,
          input_name: inputName,
          value: fileUrl,
        };
      }

      return {
        ...item,
        input_name: inputName, // Add the input_name to each item
      };
    });

    const nextStatus = await Status.findOne({
      _id: statusActionLinkData.next_status_id,
    });
    const currentStatus = await Status.findOne({
      _id: statusActionLinkData.current_status_id,
    });

    const updateStatus = await Shipment.findByIdAndUpdate(shipment_id, {
      $set: {
        status: statusActionLinkData.next_status_id,
      },
      $push: {
        timeline: {
          time: new Date(),
          comment: `Shipment Status has been changed by ${current_user.name} from ${currentStatus.status} to ${nextStatus.status}`,
        },
        attributes: {
          name: shipment.status.pseudoId,
          type: "JSON",
          value: processedFormData,
          time: new Date(),
        },
        status_change_history: {
          status: statusActionLinkData.next_status_id,
        },
      },
    });

    //this id is of Approve payment action link
    if (action_link_id === "663ba558513a0934f1734d06") {
      await consumeOnHoldInventory(shipment.lineItems);
    }

    const distributerId = shipment.order.distributor._id;
    const distributor = await Distributor.findOne({ _id: distributerId });

    const departmentPeoples = await Department.find({
      departmentType: { $in: [nextStatus.departmentType._id] },
    });

    const departmentNotified = await getDepartmentNotifiedEmails(nextStatus);

    const recipients = departmentPeoples.map((departmentPeople) => ({
      name: departmentPeople.name,
      email: departmentPeople.email,
      cc: [...departmentNotified],
    }));

    if (recipients.length > 0) {
      await EmailNotification.create({
        emailCategory: "SHIPMENT",
        emailType: "SHIPMENT_STATUS_CHANGE",
        reciepient: recipients,
        emailPayload: {
          shipmentId: shipment_id,
          date: shipment.order.createdAt,
          orderName: shipment.order.name,
          distributorName: distributor.name,
          shipmentStatus: nextStatus.status,
          shipmetRef: shipment.ref,
          currentStatus: currentStatus.status,
          nextStatus: nextStatus.status,
          actionByUser: current_user.name,
          shipmentName: shipment.name,
        },
      });
    }

    res.send({
      updateStatus,
    });
  } catch (error) {
    console.log("changeStatusAction", error);
    res.send({ error: "Something went wrong" });
  }
};

const normalizeRowKeys = (row) => {
  const cleaned = {};
  for (const key in row) {
    cleaned[key.trim().toLowerCase()] = row[key];
  }
  return cleaned;
};

export const validateSheetRows = async (rows) => {
  try {
    const validRows = [];
    const allRowsWithErrorColumn = [];

    const requiredFields = {
      "oms order id": "OMS Order ID",
      title: "Title",
      sku: "SKU",
      "location code": "Location Code",
      "budget category": "Budget Category",
      "requested qty": "Requested Quantity",
      "allocated qty": "Allocated Quantity",
      "action ( future , reject , allocate )": "Action",
    };

    const allowedActions = ["future", "reject", "allocate"];

    // 1. Normalize all rows
    const normalizedRows = rows.map((r) => normalizeRowKeys(r));

    // 2. Get unique OMS Order IDs
    const omsOrderIds = [
      ...new Set(
        normalizedRows.map((row) => row["oms order id"]).filter((id) => !!id)
      ),
    ];

    // 3. Get Orders from DB
    const dbOrders = await Order.find({
      subOrderId: { $in: omsOrderIds },
    }).lean();

    const orderMap = new Map();
    dbOrders.forEach((order) => {
      orderMap.set(order.subOrderId, order);
    });

    // 4. Prepare SKU-Location pairs for inventory fetch
    const skuLocationPairs = normalizedRows.map((row) => ({
      sku: row["sku"]?.trim(),
      location: row["location code"]?.trim()?.toUpperCase() || "MAIN",
    }));

    // 5. Fetch inventory
    const inventoryResponse = await getErpItemsAvailableInventory(
      skuLocationPairs
    );
    if (inventoryResponse instanceof NewAppError) throw inventoryResponse;

    const inventoryMap = inventoryResponse.data.items.reduce((map, item) => {
      const key = `${item.ItemNumber.trim()}|${item.Location.trim().toUpperCase()}`;
      map[key] = item.QuantityAvailableToShip || 0;
      return map;
    }, {});

    // 6. Track inventory usage
    const inventoryUsed = {}; // key: sku|location => used quantity

    // 7. Validate each row
    for (let index = 0; index < normalizedRows.length; index++) {
      const row = normalizedRows[index];
      const rawRow = rows[index]; // original row with original headers
      let error = null;

      // Required field check
      for (const key in requiredFields) {
        if (row[key] === undefined || row[key] === "") {
          error = `${requiredFields[key]} is missing`;
          break;
        }
      }

      const requestedQuantity = Number(row["requested qty"]);
      const allocatedQuantity = Number(row["allocated qty"]);
      const action = row["action ( future , reject , allocate )"];

      if (!error && isNaN(requestedQuantity)) {
        error = "'Requested Quantity' must be a valid number";
      }

      if (!error && isNaN(allocatedQuantity)) {
        error = "'Allocated Quantity' must be a valid number";
      }

      if (!error && (isNaN(allocatedQuantity) || allocatedQuantity < 0)) {
        error = "'Allocated Quantity' must be a valid non-negative number";
      }

      if (!error && !allowedActions.includes(action?.toLowerCase())) {
        error = `Invalid action '${action}'. Must be one of: ${allowedActions.join(
          ", "
        )}`;
      }

      // Order and line item match
      if (!error && row["oms order id"]) {
        const dbOrder = orderMap.get(row["oms order id"]);
        if (!dbOrder) {
          error = `Order not found in database with ID: ${row["oms order id"]}`;
        } else {
          const dbLineItem = dbOrder.line_items?.find(
            (item) => item.sku === row["sku"]
          );

          if (!dbLineItem) {
            error = `SKU "${row["sku"]}" not found in order ${row["oms order id"]}`;
          } else {
            const dbQuantity = parseInt(dbLineItem.quantity) || 0;
            if (requestedQuantity !== dbQuantity) {
              error = `Quantity mismatch for SKU "${row["sku"]}". Sheet: ${requestedQuantity}, Database: ${dbQuantity}`;
            }

            if (
              !error &&
              row["title"] &&
              dbLineItem.title &&
              row["title"].toLowerCase().trim() !==
                dbLineItem.title.toLowerCase().trim()
            ) {
              error = `Title mismatch for SKU "${row["sku"]}". Sheet: "${row["title"]}", Database: "${dbLineItem.title}"`;
            }
          }
        }
      }

      // Inventory sufficiency check with cumulative tracking
      if (!error) {
        const inventoryKey = `${row["sku"].trim()}|${row["location code"]
          .trim()
          .toUpperCase()}`;

        const availableQty = inventoryMap[inventoryKey] ?? 0;
        const usedQty = inventoryUsed[inventoryKey] || 0;
        const remaining = availableQty - usedQty;

        if (allocatedQuantity > remaining) {
          error = `Inventory insufficient: Available = ${remaining}, Allocated = ${allocatedQuantity}`;
        } else {
          inventoryUsed[inventoryKey] = usedQty + allocatedQuantity;
        }
      }

      if (error) {
        allRowsWithErrorColumn.push({ ...rawRow, error });
      } else {
        allRowsWithErrorColumn.push({ ...rawRow, error: "" });
        validRows.push({
          omsOrderId: row["oms order id"],
          title: row["title"],
          sku: row["sku"],
          locationCode: row["location code"],
          budgetCategory: row["budget category"],
          preOrderNumber: row["pre order number"],
          requestedQuantity,
          allocatedQuantity,
          action,
        });
      }
    }

    return { validRows, allRowsWithErrorColumn };
  } catch (error) {
    throw new NewAppError("SHEET_VALIDATION_ERROR", error.message, 500);
  }
};

export const parseSheetFromReq = async (filePath) => {
  try {
    if (!filePath)
      throw new NewAppError("SHEET_PARSE_ERROR", "No file provided", 400);
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const jsonData = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);
    return jsonData;
  } catch (err) {
    throw new NewAppError("SHEET_PARSE_ERROR", err.message, 400);
  }
};

export const getErpItemsAvailableInventory = async (skuLocationPairs = []) => {
  try {
    // Deduplicate SKUs and Locations
    const uniqueSkus = Array.from(
      new Set(skuLocationPairs.map((item) => item.sku))
    );
    const uniqueLocations = Array.from(
      new Set(skuLocationPairs.map((item) => item.location))
    );

    const url = `${process.env.SAGE_CUSTOM_API_ENDPOINT}/api/ICItem/GetICLocationDetails`;
    const response = await axios.get(url, {
      params: {
        company: process.env.SAGE_COMPANY,
        APIKey: process.env.SAGE_CUSTOM_API_KEY,
        UnformattedItemNo: uniqueSkus.join(","),
        Location: uniqueLocations.join(","),
      },
    });

    const rawItems = response.data;

    const validCombinations = new Set(
      skuLocationPairs.map(
        (item) => `${item.sku.trim()}|${item.location.trim().toUpperCase()}`
      )
    );

    const filteredItems = rawItems.filter((item) => {
      const key = `${item.ItemNumber.trim()}|${item.Location.trim().toUpperCase()}`;
      return validCombinations.has(key);
    });

    return { status: "success", data: { items: filteredItems } };
  } catch (error) {
    return new NewAppError("INVENTORY_FETCH_ERROR", error.message, 500);
  }
};

async function createNewOrder(baseOrder, lineItems, orderName) {
  try {
    // Create a clean copy of the base order without Mongoose metadata
    const newOrderData = {
      order_id: baseOrder.order_id,
      subOrderId: orderName,
      name: baseOrder.name,
      current_subtotal_price: baseOrder.current_subtotal_price,
      shopifyCompanyId: baseOrder.shopifyCompanyId,
      status: lineItems[0].status.toLowerCase(),
      distributor: baseOrder.distributor,
      customer: baseOrder.customer,
      line_items: lineItems.map((item) => ({
        lineItemId: item.lineItemId,
        shopifyVariantId: item.shopifyVariantId,
        shopifyProductId: item.shopifyProductId,
        productTitle: item.productTitle,
        variantTitle: item.variantTitle,
        image: item.image,
        quantity: item.quantity, // This is the remaining quantity
        price: item.price,
        sku: item.sku,
        productCategory: item.productCategory,
        metaFieldBudgetCategory: item.metaFieldBudgetCategory,
        fulfilled: item.fulfilled || 0, // Should be 0 for new orders with remaining quantities
        remaining: item.remaining || item.quantity, // Remaining quantity
        requested: item.requested || item.quantity, // Original requested quantity
        status: item.status,
        locationCode: item.locationCode,
      })),
      type: baseOrder.type,
      budgetCategoryValue: baseOrder.budgetCategoryValue,
      preOrderValue: baseOrder.preOrderValue,
      isFuture: lineItems[0].status === "order placed" && lineItems[0].isFuture,
      createdAt: new Date(),
      updatedAt: new Date(),
      timeline: [
        {
          time: new Date(),
          comment: `Order created from ${baseOrder.subOrderId} with ${lineItems[0].quantity} quantity`,
        },
      ],
    };

    const newOrder = new Order(newOrderData);
    await newOrder.save();
    return newOrder;
  } catch (error) {
    console.error("Error creating new order:", error);
    throw new Error("Failed to create new order");
  }
}

export const triggerBatchProcessOrders = async (req, res, next) => {
  const MAX_CONCURRENT_OPERATIONS = 10;
  const PROCESSING_TIMEOUT = 300000;
  const timeoutId = setTimeout(() => {
    throw new Error(
      `Batch processing timeout after ${PROCESSING_TIMEOUT / 1000} seconds`
    );
  }, PROCESSING_TIMEOUT);

  try {
    let rows = [];
    let orderIdentifiers = [];
    let errorRows = [];

    // Process input (file or order IDs)
    if (req.file) {
      const parsedRows = await parseSheetFromReq(req.file.path);
      if (!parsedRows || parsedRows instanceof NewAppError) {
        return next(
          new NewAppError("SHEET_PARSE_ERROR", parsedRows?.message, 400)
        );
      }

      const { validRows, allRowsWithErrorColumn } = await validateSheetRows(
        parsedRows
      );

      if (allRowsWithErrorColumn.some((row) => row.error)) {
        const errorBuffer = await generateErrorSheetWithExcelJS(
          allRowsWithErrorColumn
        );

        res.set({
          "Content-Type":
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "Content-Disposition":
            'attachment; filename="validation_errors.xlsx"',
          "Content-Length": errorBuffer.length,
        });

        res.status(400).send(errorBuffer);
        return;
      }

      rows = validRows;
      orderIdentifiers = [...new Set(validRows.map((row) => row.omsOrderId))];
    } else if (req.body.orderIds) {
      orderIdentifiers = Array.isArray(req.body.orderIds)
        ? req.body.orderIds
        : [req.body.orderIds];
    } else {
      return next(
        new NewAppError(
          "INPUT_ERROR",
          "Either file or orderIds must be provided",
          400
        )
      );
    }
    // Fetch orders
    const orders = await Order.find({ subOrderId: { $in: orderIdentifiers } })
      .populate("distributor status")
      .lean();

    if (!orders.length) {
      return next(
        new NewAppError(
          "ORDER_NOT_FOUND",
          `No orders found with provided IDs: ${orderIdentifiers.join(", ")}`,
          404
        )
      );
    }

    // Prepare SKU-location pairs
    const skuLocationPairs = [];
    const orderMap = new Map(
      orders.map((order) => [order._id.toString(), order])
    );

    for (const order of orders) {
      const orderRows = rows.filter(
        (row) =>
          row.omsOrderId === order.order_id.toString() ||
          row.omsOrderId === order.subOrderId
      );

      for (const item of order.line_items) {
        const matchingRow = orderRows.find((row) => row.sku === item.sku);
        const defaultAction = req.file ? "allocate" : "pending";
        const locationCode =
          matchingRow?.locationCode || item.locationCode || "MAIN";

        skuLocationPairs.push({
          sku: item.sku,
          location: locationCode,
          orderId: order._id,
          orderIdentifier: order.subOrderId,
          requestedQty: matchingRow?.requestedQuantity || item.quantity,
          allocatedQty: matchingRow?.allocatedQuantity || 0,
          action: matchingRow?.action || defaultAction,
          lineItemId: item.lineItemId,
        });
      }
    }

    // Check inventory
    const inventoryResponse = await getErpItemsAvailableInventory(
      skuLocationPairs.map(({ sku, location }) => ({ sku, location }))
    );

    if (inventoryResponse instanceof NewAppError) {
      return next(inventoryResponse);
    }

    const inventoryMap = inventoryResponse.data.items.reduce((map, item) => {
      map[`${item.ItemNumber.trim()}|${item.Location.trim().toUpperCase()}`] =
        item.QuantityAvailableToShip || 0;
      return map;
    }, {});

    const inventoryUsed = {};
    const errorSheetData = [];

    for (const pair of skuLocationPairs) {
      const inventoryKey = `${pair.sku.trim()}|${pair.location
        .trim()
        .toUpperCase()}`;
      const availableQty = inventoryMap[inventoryKey] ?? 0;
      const usedQty = inventoryUsed[inventoryKey] || 0;
      const remaining = availableQty - usedQty;

      if (pair.allocatedQty > remaining) {
        errorSheetData.push({
          "OMS Order ID": pair.orderIdentifier,
          SKU: pair.sku,
          "Requested Quantity": pair.requestedQty,
          "Allocated Quantity": pair.allocatedQty,
          "Location Code": pair.location,
          Action: pair.action,
          Error: `Inventory insufficient: Available = ${remaining}, Allocated = ${pair.allocatedQty}`,
        });
      } else {
        inventoryUsed[inventoryKey] = usedQty + pair.allocatedQty;
        errorSheetData.push({
          "OMS Order ID": pair.orderIdentifier,
          SKU: pair.sku,
          "Requested Quantity": pair.requestedQty,
          "Allocated Quantity": pair.allocatedQty,
          "Location Code": pair.location,
          Action: pair.action,
          Error: "",
        });
      }
    }

    // If any error exists in inventory check, stop further processing
    const hasError = errorSheetData.some((row) => row.Error);

    if (hasError) {
      const errorSheetBuffer = await generateErrorSheetWithExcelJS(
        errorSheetData
      );

      res.set({
        "Content-Type":
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "Content-Disposition": `attachment; filename="inventory_errors_${
          new Date().toISOString().split("T")[0]
        }.xlsx"`,
        "Content-Length": errorSheetBuffer.length,
      });

      return res.status(400).send(errorSheetBuffer);
    }

    // Process orders
    const processingResults = [];
    const newOrders = [];
    const orderProcessingMap = new Map();

    for (const pair of skuLocationPairs) {
      const order = orderMap.get(pair.orderId.toString());
      if (!order) continue;

      if (!orderProcessingMap.has(order._id.toString())) {
        orderProcessingMap.set(order._id.toString(), {
          order,
          lineItemUpdates: [],
          newOrderItems: [],
        });
      }

      const inventoryKey = `${pair.sku}|${pair.location}`;
      const availableQty = inventoryMap[inventoryKey] || 0;
      const requestedQty = pair.requestedQty;
      const allocatedQty = pair.allocatedQty;
      const isFuture = pair.action.toLowerCase() === "future";
      const action = pair.action.toLowerCase();

      let fulfilled = 0;
      let remainingQty = requestedQty;
      let errorMessage = "";

      const orderProcessing = orderProcessingMap.get(order._id.toString());
      const lineItem = order.line_items.find(
        (li) => li.lineItemId === pair.lineItemId
      );
      if (!lineItem) continue;

      if (availableQty >= allocatedQty) {
        // Inventory enough — fulfill
        fulfilled = allocatedQty;
        remainingQty = requestedQty - allocatedQty;

        inventoryMap[inventoryKey] = availableQty - fulfilled;

        // Push to current order line item updates
        orderProcessing.lineItemUpdates.push({
          lineItem,
          fulfilled,
          requestedQty,
          location: pair.location,
          action,
          remainingQty,
          isFuture,
        });

        // If some quantity is still left (requested > allocated), create new order as per action
        if (remainingQty > 0) {
          const status =
            action === "reject"
              ? "REJECTED"
              : action === "future"
              ? "ORDER PLACED"
              : "PENDING";

          orderProcessing.newOrderItems.push({
            lineItem: {
              ...lineItem,
              quantity: remainingQty,
              requested: requestedQty,
              fulfilled: 0,
              remaining: remainingQty,
              status,
              locationCode: pair.location,
              isFuture: action === "future",
            },
            action,
          });
        }
      } else {
        // Not enough inventory — don't fulfill anything
        fulfilled = 0;
        remainingQty = requestedQty;
        errorMessage = `Inventory not sufficient for allocation.`;

        // Move SKU to new order with status "created"
        orderProcessing.newOrderItems.push({
          lineItem: {
            ...lineItem,
            quantity: remainingQty,
            requested: requestedQty,
            fulfilled: 0,
            remaining: remainingQty,
            status: "CREATED",
            locationCode: pair.location,
            isFuture: false,
          },
          action: "created",
        });

        // Add to error sheet
        errorRows.push({
          ...pair,
          error: errorMessage,
          orderId: pair.orderIdentifier,
          timestamp: new Date().toISOString(),
        });

        // Skip adding this SKU to current order
        continue;
      }

      // Track result for final JSON response
      processingResults.push({
        orderId: order._id,
        subOrderId: order.subOrderId,
        sku: pair.sku,
        requested: requestedQty,
        fulfilled,
        remaining: remainingQty,
        status: fulfilled > 0 ? "ORDER PLACED" : "CREATED",
        action: pair.action,
      });
    }

    // Process each order's updates
    for (const [orderId, orderProcessing] of orderProcessingMap) {
      const { order, lineItemUpdates, newOrderItems } = orderProcessing;

      try {
        const hasFulfilledItems = lineItemUpdates.some(
          (update) => update.fulfilled > 0
        );

        if (!hasFulfilledItems) {
          // Handle orders with no inventory
          const actions = lineItemUpdates.map((update) => update.action);
          const uniqueActions = [...new Set(actions)];
          let orderStatus, statusReason;

          if (uniqueActions.length === 1) {
            const action = uniqueActions[0];
            if (action === "reject") {
              orderStatus = "rejected";
              statusReason = "All items rejected as per sheet instructions";
            } else if (action === "future") {
              orderStatus = "future";
              statusReason = "All items marked as future orders";
            } else {
              orderStatus = "pending";
              statusReason = "No inventory available for allocation";
            }
          } else {
            orderStatus = "created";
            statusReason = "No inventory available for allocation";
          }

          const updatedLineItems = order.line_items.map((lineItem) => {
            const update = lineItemUpdates.find(
              (u) => u.lineItem.lineItemId === lineItem.lineItemId
            );
            if (!update) return lineItem;

            const itemStatus =
              update.action === "reject"
                ? "REJECTED"
                : update.action === "future"
                ? "FUTURE"
                : "PENDING";

            return {
              ...lineItem,
              requested: update.requestedQty,
              fulfilled: 0,
              remaining: update.requestedQty,
              status: itemStatus,
              locationCode: update.location,
            };
          });

          orderProcessing.updateData = {
            line_items: updatedLineItems,
            status: orderStatus,
            timeline: [
              ...(order.timeline || []),
              {
                time: new Date(),
                comment: `Order status changed to ${orderStatus} - ${statusReason}`,
              },
            ],
          };

          if (uniqueActions.length === 1) {
            orderProcessing.newOrderItems = [];
          }

          continue;
        }

        // Update original order with fulfilled items
        const updatedLineItems = lineItemUpdates
          .filter((update) => update.fulfilled > 0)
          .map((update) => ({
            ...update.lineItem,
            quantity: update.fulfilled,
            fulfilled: update.fulfilled,
            remaining: Math.abs(update.fulfilled - update.requestedQty),
            requested: update.requestedQty,
            locationCode: update.location,
            tracking: [
              ...(update.lineItem.tracking || []),
              {
                fulfilled: update.fulfilled,
                requested: update.requestedQty,
                at: new Date(),
              },
            ],
          }));

        const hasFutureItems = lineItemUpdates.some(
          (update) => update.isFuture && update.fulfilled > 0
        );
        const updateData = {
          line_items: updatedLineItems,
          timeline: [
            ...(order.timeline || []),
            {
              time: new Date(),
              comment: `Order processed - ${updatedLineItems.length} line items updated, ${newOrderItems.length} new orders created`,
            },
          ],
        };

        if (hasFutureItems) {
          updateData.isFuture = true;
        }

        if (updatedLineItems.length > 0) {
          updateData.status = "order placed";
        }

        orderProcessing.updateData = updateData;

        // Step 1: Group new order items BY action FOR THIS order only
        const groupedByAction = newOrderItems.reduce((acc, item) => {
          const actionKey = item.action.toLowerCase();
          if (!acc[actionKey]) acc[actionKey] = [];
          acc[actionKey].push(item.lineItem);
          return acc;
        }, {});

        // Step 2: Create one new order per action
        for (const [action, lineItems] of Object.entries(groupedByAction)) {
          const nextSuffix = await getNextOrderSuffix(order.subOrderId);
          const newSubOrderId = `${
            order.subOrderId.split("_")[0]
          }_${nextSuffix}`;

          const newOrder = await createNewOrder(
            order,
            lineItems,
            newSubOrderId
          );

          const hasFulfilledItems = lineItems.some(
            (item) => item.fulfilled > 0
          );
          newOrder.status = hasFulfilledItems
            ? "order placed"
            : action === "future"
            ? "order placed"
            : action === "reject"
            ? "rejected"
            : "pending";

          if (action === "future") newOrder.isFuture = true;

          newOrder.timeline = [
            ...(newOrder.timeline || []),
            {
              time: new Date(),
              comment: `Order created from ${order.subOrderId} - ${action} action for ${lineItems.length} line items`,
            },
          ];

          await newOrder.save();

          if (action === "future") {
            try {
              const erpResponse = await createERPOrderController(
                newOrder._id.toString()
              );
              if (!(erpResponse instanceof NewAppError)) {
                await addErpIdentifierOrderDbService(newOrder._id, erpResponse);
                newOrder.timeline.push({
                  time: new Date(),
                  comment: `ERP order created successfully - ERP ID: ${erpResponse.data.order.OrderUniquifier}. Status changed to order_placed`,
                });
                await newOrder.save();

                // Create email notification for future order placed
                await createOrderCreatedInSageEmailNotification(
                  newOrder.subOrderId,
                  "future"
                );
              } else {
                await Order.findByIdAndUpdate(
                  newOrder._id,
                  {
                    status: "created",
                    $push: {
                      timeline: {
                        time: new Date(),
                        comment: `ERP order creation failed`,
                      },
                    },
                  },
                  { new: true }
                );
              }
            } catch (erpError) {
              newOrder.timeline.push({
                time: new Date(),
                comment: `ERP order creation failed: ${erpError.message}`,
              });
              await newOrder.save();
            }
          }

          newOrders.push(newOrder);
        }
      } catch (error) {
        orderProcessing.hasError = true;
        orderProcessing.errorMessage = error.message;

        const orderRows = rows.filter(
          (row) =>
            row.omsOrderId === order.order_id.toString() ||
            row.omsOrderId === order.subOrderId
        );

        orderRows.forEach((row) => {
          errorRows.push({
            ...row,
            error: `Order processing failed: ${error.message}`,
            orderId: order.subOrderId,
            timestamp: new Date().toISOString(),
          });
        });
      }
    }

    // Save updated orders in batches
    const orderUpdates = Array.from(orderProcessingMap.values()).filter(
      ({ updateData, hasError }) => updateData && !hasError
    );

    for (let i = 0; i < orderUpdates.length; i += MAX_CONCURRENT_OPERATIONS) {
      const batch = orderUpdates.slice(i, i + MAX_CONCURRENT_OPERATIONS);

      await Promise.all(
        batch.map(async ({ order, updateData }) => {
          try {
            const updatedOrder = await Order.findByIdAndUpdate(
              order._id,
              updateData,
              { new: true }
            );

            // Create ERP order for fulfilled orders
            if (updateData.line_items?.some((item) => item.fulfilled > 0)) {
              try {
                const erpResponse = await createERPOrderController(
                  updatedOrder._id.toString()
                );

                if (!(erpResponse instanceof NewAppError)) {
                  await addErpIdentifierOrderDbService(
                    updatedOrder._id,
                    erpResponse
                  );
                  await Order.findByIdAndUpdate(
                    updatedOrder._id,
                    {
                      $push: {
                        timeline: {
                          time: new Date(),
                          comment: `ERP order created successfully - ERP ID: ${erpResponse.data.order.OrderUniquifier}. Status changed to order_placed`,
                        },
                      },
                    },
                    { new: true }
                  );

                  // Create email notification for order placed
                  await createOrderCreatedInSageEmailNotification(
                    updatedOrder.subOrderId,
                    "regular"
                  );
                } else {
                  await Order.findByIdAndUpdate(
                    updatedOrder._id,
                    {
                      status: "created",
                      $push: {
                        timeline: {
                          time: new Date(),
                          comment: `ERP order creation failed`,
                        },
                      },
                    },
                    { new: true }
                  );
                }
              } catch (erpError) {
                await Order.findByIdAndUpdate(
                  updatedOrder._id,
                  {
                    status: "created",
                    $push: {
                      timeline: {
                        time: new Date(),
                        comment: `ERP order creation failed: ${erpError.message}`,
                      },
                    },
                  },
                  { new: true }
                );
              }
            }
          } catch (error) {
            const orderRows = rows.filter(
              (row) =>
                row.omsOrderId === order.order_id.toString() ||
                row.omsOrderId === order.subOrderId
            );

            orderRows.forEach((row) => {
              errorRows.push({
                ...row,
                error: `Database update failed: ${error.message}`,
                orderId: order.subOrderId,
                timestamp: new Date().toISOString(),
              });
            });
          }
        })
      );

      if (orderUpdates.length > MAX_CONCURRENT_OPERATIONS) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    clearTimeout(timeoutId);
    const processedCount = orderProcessingMap.size;
    const errorCount = errorRows.length;

    // Generate error sheet if needed
    if (errorCount > 0) {
      try {
        const errorSheetData = errorRows.map((row) => ({
          "Order ID": row.orderId,
          "OMS Order ID": row.orderIdentifier,
          SKU: row.sku,
          "Requested Quantity": row.requestedQty,
          "Allocated Quantity": row.allocatedQty,
          "Location Code": row.location,
          Action: row.action,
          Error: row.error,
          Timestamp: row.timestamp,
        }));

        const errorSheetBuffer = await generateErrorSheetWithExcelJS(
          errorSheetData
        );

        res.set({
          "Content-Type":
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "Content-Disposition": `attachment; filename="order_processing_errors_${
            new Date().toISOString().split("T")[0]
          }.xlsx"`,
          "Content-Length": errorSheetBuffer.length,
        });

        return res.status(400).send(errorSheetBuffer);
      } catch (sheetError) {
        // Error generating error sheet
      }
    }

    // Success response
    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        updatedOrders: Array.from(orderProcessingMap.values())
          .filter(({ hasError }) => !hasError)
          .map(({ order }) => ({
            _id: order._id,
            subOrderId: order.subOrderId,
            status: order.status,
          })),
        newOrders: newOrders.map((o) => ({
          _id: o._id,
          subOrderId: o.subOrderId,
          status: o.status,
          isFuture: o.isFuture,
        })),
        processingResults: processingResults.filter((result) => {
          const orderProcessing = Array.from(orderProcessingMap.values()).find(
            ({ order }) => order._id.toString() === result.orderId.toString()
          );
          return orderProcessing && !orderProcessing.hasError;
        }),
        batchInfo: {
          totalProcessed: processedCount,
          errorsEncountered: errorCount,
          newOrdersCreated: newOrders.length,
          successfullyProcessed: processedCount - errorCount,
          erpOrdersAttempted: {
            fulfilledOrders: processedCount - errorCount,
            futureOrders: newOrders.filter((o) => o.isFuture).length,
            total:
              processedCount -
              errorCount +
              newOrders.filter((o) => o.isFuture).length,
          },
          processingTimeMs:
            Date.now() - (Date.now() - PROCESSING_TIMEOUT + PROCESSING_TIMEOUT),
        },
      },
    });
  } catch (error) {
    clearTimeout(timeoutId);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "ORDER_PROCESSING_ERROR",
          message: "An error occurred while processing the orders",
        },
      ],
    });
  }
};

export async function processOrders(validRows, inventoryData, createdOrders) {
  try {
    const futureOrders = [];
    const rejectedOrders = [];
    const alignedItems = [];
    const nonAlignedItems = [];

    // Build inventory map: SKU => availableQty
    const inventoryMap = {};
    inventoryData.forEach((item) => {
      inventoryMap[item.ItemNumber.trim()] = item.QuantityAvailableToShip || 0;
    });

    for (const { order } of createdOrders) {
      const matchingRows = validRows.filter(
        (row) => row.omsOrderId.toString() === order.subOrderId
      );

      for (const lineItem of order.line_items) {
        const matchingRow = matchingRows.find(
          (row) => row.sku === lineItem.sku
        );
        if (!matchingRow) continue;

        const requestedQty = Number(matchingRow.requestedQuantity);
        const allocatedQty = Number(matchingRow.allocatedQuantity);
        const action = matchingRow.action.toLowerCase();
        const sku = lineItem.sku.trim();
        const availableQty = inventoryMap[sku] || 0;

        // Allocate available quantity
        const fulfilled = Math.min(availableQty, allocatedQty);

        if (fulfilled > 0) {
          alignedItems.push({
            _id: order._id,
            subOrderId: order.subOrderId,
            line_items: [
              {
                ...lineItem,
                requested: requestedQty,
                fulfilled: fulfilled,
                locationCode: matchingRow.locationCode,
                status: "order placed",
              },
            ],
          });
          inventoryMap[sku] = availableQty - fulfilled;
        }

        // Process remaining quantity
        const remainingQty = requestedQty - fulfilled;
        if (remainingQty > 0) {
          const remainingItem = {
            ...lineItem,
            requested: remainingQty,
            fulfilled: 0,
            locationCode: matchingRow.locationCode,
            status: action === "reject" ? "REJECTED" : "PENDING",
          };

          if (action === "reject") {
            rejectedOrders.push({
              _id: order._id,
              subOrderId: order.subOrderId,
              line_items: [remainingItem],
            });
          } else if (action === "future") {
            futureOrders.push({
              _id: order._id,
              subOrderId: order.subOrderId,
              line_items: [remainingItem],
              isFuture: true,
            });
          } else {
            nonAlignedItems.push({
              _id: order._id,
              subOrderId: order.subOrderId,
              line_items: [remainingItem],
            });
          }
        }
      }
    }

    return {
      status: "success",
      alignedItems,
      nonAlignedItems,
      futureOrders,
      rejectedOrders,
    };
  } catch (err) {
    return new NewAppError("SERVER_ERROR", err.message, 500);
  }
}

async function getNextOrderSuffix(baseOrderId) {
  const basePrefix = baseOrderId.split("_")[0];

  const lastOrder = await Order.findOne({
    subOrderId: new RegExp(`^${basePrefix}_\\d+$`),
  })
    .sort({ subOrderId: -1 }) // ensures latest suffix
    .limit(1);

  if (!lastOrder) return "1";

  const lastSuffix = parseInt(lastOrder.subOrderId.split("_").pop());
  return (lastSuffix + 1).toString();
}
const createShipments = async (order, allocationType = { type: "auto" }) => {
  try {
    const orderLineSkus = order.line_items.map((x) => x.sku);
    const validSkus = orderLineSkus.filter((sku) => sku !== "" && sku !== null);

    if (validSkus.length <= 0) {
      return new NewAppError(
        "INVALID_INPUT",
        "Order items have no valid skus.",
        500
      );
    }
    let existingInventory;
    if (allocationType.type === "manual") {
      existingInventory = allocationType.allocatedQuantity;
    } else {
      let orderLineItemsInventoryAvailable =
        await getErpItemsAvailableInventoryUtility(orderLineSkus);

      if (orderLineItemsInventoryAvailable instanceof NewAppError) {
        return orderLineItemsInventoryAvailable;
      }
      existingInventory = orderLineItemsInventoryAvailable.data.items;
    }

    const data = compareOrderItems(order.line_items, existingInventory);
    if (data instanceof NewAppError) {
      return data;
    }

    return data;
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error while creating shipments.Message:${error.message}`,
      500
    );
  }
};

const createEmailNotifications = async (allrecipients) => {
  try {
    const shipmentIds = allrecipients.map(
      (notification) => notification.emailPayload.shipmentId
    );

    const shipments = await Shipment.find({
      _id: { $in: shipmentIds },
    }).select("_id");

    const validShipmentIds = shipments.map((shipment) =>
      shipment._id.toString()
    );

    const filteredNotifications = allrecipients.filter((notification) =>
      validShipmentIds.includes(notification.emailPayload.shipmentId.toString())
    );

    if (filteredNotifications.length > 0) {
      const response = await EmailNotification.insertMany(
        filteredNotifications
      );
      return response;
    } else {
      return { message: "No valid notifications to insert." };
    }
  } catch (error) {
    return new NewAppError(
      "SERVER_ERROR",
      `Error while creating notifications.`,
      500
    );
  }
};

export const endEscalationStatus = async (shipment) => {
  console.log(shipment.status._id, "shipment.status._id");
  const testUpdate = await Shipment.findOneAndUpdate(
    {
      _id: shipment._id,
      "escalation_history.status": shipment.status._id,
    },
    {
      $set: {
        "escalation_history.$.end": new Date(),
        escalation_status: false,
      },
    },
    { new: true }
  );
};

function compareOrderItems(orderItems, inventoryDocs) {
  try {
    //requestedItems has order line items,
    //inventory docs has ERP available/sheet allocated quantity
    const inventoryMap = new Map();

    //filtered so that only those line items are processed rest are left(handles case when no sku)
    const filterItemsWithoutSku = orderItems.filter(
      (item) => item.sku !== "" && item.sku !== null
    );

    inventoryDocs.forEach(function (doc) {
      inventoryMap.set(doc.sku, doc);
    });

    const fulfilledItems = [];
    const remainingItems = [];

    filterItemsWithoutSku.forEach((orderItem) => {
      const allocatedItem = inventoryMap.get(orderItem.sku);
      const allocatedItemQuantity = allocatedItem?.quantity || 0;
      const delta = allocatedItemQuantity - orderItem.quantity;

      if (delta >= 0) {
        //can fulfill all
        fulfilledItems.push({
          ...orderItem,
          requested: orderItem.quantity,
          fulfilled: orderItem.quantity,
          remaining: orderItem.quantity,
        });
      } else if (delta < 0) {
        //allocated less than order ,can fulfill partially
        if (allocatedItemQuantity > 0) {
          //can break shipment
          const fulfillable = delta * -1;
          fulfilledItems.push({
            ...orderItem,
            requested: allocatedItemQuantity,
            fulfilled: allocatedItemQuantity,
            remaining: fulfillable,
          });
          remainingItems.push({
            ...orderItem,
            requested: orderItem.quantity - allocatedItemQuantity,
            fulfilled: 0,
            remaining: 0,
          });
        } else {
          //if allocated/ERP quantity is zero careate only partial shipment(manual allocation or sku not present or erp)
          remainingItems.push({
            ...orderItem,
            requested: orderItem.quantity,
            fulfilled: 0,
            remaining: 0,
          });
        }
      }
    });

    return {
      alignedShipment: fulfilledItems,
      nonAlignedShipment: remainingItems,
    };
  } catch {
    return new NewAppError("PROCESS_ERROR", "Could not process order.", 500);
  }
}

async function getCreatedOrders(ids, req) {
  try {
    let createdOrders = [];
    if (Array.isArray(ids) && ids.length > 0) {
      const objectIds = ids
        .filter((id) => mongoose.Types.ObjectId.isValid(id))
        .map((id) => new mongoose.Types.ObjectId(id));
      createdOrders = await Order.aggregate([
        {
          $match: {
            _id: { $in: objectIds },
          },
        },
        {
          $lookup: {
            from: "orders",
            localField: "name",
            foreignField: "name",
            as: "order",
          },
        },
        {
          $unwind: {
            path: "$order",
          },
        },
        {
          $lookup: {
            from: "status",
            let: { statusId: "$order.status" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$pseudoId", "CREATED"] },
                      { $eq: ["$_id", "$$statusId"] },
                    ],
                  },
                },
              },
              {
                $project: { _id: 1 },
              },
            ],
            as: "matchingStatus",
          },
        },
        {
          $unwind: {
            path: "$matchingStatus",
          },
        },
        {
          $project: {
            order: 1,
            _id: 0,
          },
        },
        {
          $lookup: {
            from: "distributors",
            localField: "order.distributor",
            foreignField: "_id",
            as: "distributor",
          },
        },
        {
          $unwind: {
            path: "$distributor",
          },
        },
      ]);
    } else if (req.salesPersonDept) {
      const user = req.user;

      const distributorIds = await Distributor.distinct("_id", {
        salespersonId: user._id.toString(),
      });

      createdOrders = await Order.aggregate([
        // Match orders belonging to the salesperson's distributors
        {
          $match: {
            distributor: { $in: distributorIds },
          },
        },
        // Lookup status to filter by pseudoId: "CREATED"
        {
          $lookup: {
            from: "status",
            let: { statusId: "$status" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ["$pseudoId", "CREATED"] }, // Filter by pseudoId: "CREATED"
                      { $eq: ["$_id", "$$statusId"] }, // Match the status ID
                    ],
                  },
                },
              },
              { $project: { _id: 1 } }, // Only return the `_id` of the matching status
            ],
            as: "matchingStatus",
          },
        },
        // Filter out orders without a matching status
        {
          $unwind: "$matchingStatus",
        },
        // Lookup distributor details
        {
          $lookup: {
            from: "distributors",
            localField: "distributor",
            foreignField: "_id",
            as: "distributor",
          },
        },
        {
          $unwind: "$distributor", // Ensure distributor is not an array
        },
        // Structure the response to match the `else` format
        {
          $project: {
            order: {
              _id: "$_id",
              order_id: "$order_id",
              name: "$name",
              current_subtotal_price: "$current_subtotal_price",
              shopifyCompanyId: "$shopifyCompanyId",
              status: "$status",
              distributor: "$distributor._id", // Distributor ID only in the `order`
              customer: "$customer",
              line_items: "$line_items",
              timeline: "$timeline",
              attributes: "$attributes",
              createdAt: "$createdAt",
              updatedAt: "$updatedAt",
              __v: "$__v",
            },
            distributor: "$distributor", // Full distributor object here
          },
        },
      ]);
    } else {
      createdOrders = await Status.aggregate([
        {
          $match: {
            pseudoId: "CREATED",
          },
        },
        {
          $lookup: {
            from: "orders",
            localField: "_id",
            foreignField: "status",
            as: "order",
          },
        },
        {
          $unwind: {
            path: "$order",
          },
        },
        {
          $project: {
            order: 1,
            _id: 0,
          },
        },
        {
          $lookup: {
            from: "distributors",
            localField: "order.distributor",
            foreignField: "_id",
            as: "distributor",
          },
        },
        {
          $unwind: {
            path: "$distributor",
          },
        },
      ]);
    }
    return createdOrders;
  } catch {
    return null;
  }
}
// consume on hold inventory
async function consumeOnHoldInventory(inventoryToConsume) {
  const bulkUpdateOps = inventoryToConsume.map((x) => ({
    updateOne: {
      filter: { sku: x.sku },
      update: {
        $inc: { on_hold: x.fulfilled * -1 },
      },
      upsert: false,
    },
  }));
  const bulkwrite = await Inventory.bulkWrite(bulkUpdateOps);
  console.log("consumeOnHoldInventoryLog", bulkwrite);
}

async function holdManualAllocatedInventory(inventoryToHold) {
  const skuMapLineItems = {};
  const allSkus = inventoryToHold.map((x) => {
    skuMapLineItems[x.sku] = x.requested;
    return x.sku;
  });
  const existingInventory = await Inventory.find({ sku: { $in: allSkus } });
  const notFulfillableSkus = [];
  for (let i = 0; i < existingInventory.length; i++) {
    const inventory = existingInventory[i];
    const availableInventory = inventory.quantity || 0;
    const requestedInventory = skuMapLineItems[inventory.sku];
    if (requestedInventory > availableInventory) {
      notFulfillableSkus.push(inventory.sku);
    }
  }

  if (notFulfillableSkus.length) {
    return {
      skus: notFulfillableSkus,
      message: "Sufficient quantity is not available.",
    };
  } else {
    const bulkUpdateOps = inventoryToHold.map((x) => ({
      updateOne: {
        filter: { sku: x.sku },
        update: {
          $inc: { quantity: x.requested * -1, on_hold: x.requested },
        },
      },
    }));
    const bulkwrite = await Inventory.bulkWrite(bulkUpdateOps);
    console.log("hold inventory for manual allocation success", bulkwrite);
  }
}

export async function alignmentPendingOrderProcess(req, res) {
  try {
    const ids = req.body;
    const result = await alignmentPendingOrderProcessTrigger(ids);
    res.status(200).json({
      message: `All shipments are processed successfully ${result.createdShipments.length} new shipments created and ${result.updatedShipments.length} shipments processed updated`,
      data: result,
    });
  } catch (error) {
    return res.status(500).json({
      error: error.message || "Internal server error",
    });
  }
}

export async function alignmentPendingOrderProcessTrigger(
  ids,
  statusActionLinkData
) {
  try {
    let alignmentPendingShipments = [];

    if (ids && ids.length) {
      alignmentPendingShipments = await Shipment.aggregate([
        {
          $match: {
            name: { $in: [...ids] },
          },
        },
        {
          $lookup: {
            from: "shipments",
            localField: "name",
            foreignField: "name",
            as: "shipment",
          },
        },
        {
          $unwind: {
            path: "$shipment",
          },
        },
        {
          $lookup: {
            from: "orders",
            localField: "order",
            foreignField: "_id",
            as: "order",
          },
        },
        {
          $unwind: {
            path: "$order",
          },
        },
        {
          $lookup: {
            from: "distributors",
            localField: "order.distributor",
            foreignField: "_id",
            as: "distributor",
          },
        },
        {
          $unwind: {
            path: "$distributor",
          },
        },
      ]);
    } else {
      alignmentPendingShipments = await Status.aggregate([
        {
          $match: {
            pseudoId: "ALIGNMENT_PENDING",
          },
        },
        {
          $lookup: {
            from: "shipments",
            localField: "_id",
            foreignField: "status",
            as: "shipment",
          },
        },
        {
          $unwind: {
            path: "$shipment",
          },
        },
        {
          $project: {
            shipment: 1,
            _id: 0,
          },
        },
        {
          $lookup: {
            from: "orders",
            localField: "shipment.order",
            foreignField: "_id",
            as: "order",
          },
        },
        {
          $unwind: {
            path: "$order",
          },
        },
        {
          $lookup: {
            from: "distributors",
            localField: "order.distributor",
            foreignField: "_id",
            as: "distributor",
          },
        },
        {
          $unwind: {
            path: "$distributor",
          },
        },
      ]);
    }

    if (!alignmentPendingShipments.length) {
      return "Nothing to process";
    }

    const createdShipments = [];
    const updatedShipments = [];

    const alignShipments = alignmentPendingShipments.sort(
      (a, b) => a.distributor.priority - b.distributor.priority
    );

    const alignedShipmentStatus = await Status.findOne({
      _id: statusActionLinkData?.next_status_id || "673afb29907394a7758ae393",
    });

    const nonAlignedShipmentStatus = await Status.findOne({
      _id: "673afbb0a7f9a80fac8a4944",
    });

    const alignedDepartmentPeoples = await Department.find({
      departmentType: { $in: [alignedShipmentStatus.departmentType] },
    });

    const nonAlignedDepartmentPeoples = await Department.find({
      departmentType: { $in: [nonAlignedShipmentStatus.departmentType] },
    });

    for (let i = 0; i < alignShipments.length; i++) {
      const order = alignShipments[i].order;
      const shipment = alignShipments[i].shipment;
      const distributor = alignShipments[i].distributor;
      const shipments = await createShipments(
        {
          ...order,
          line_items: shipment.lineItems.map((item) => ({
            ...item,
            quantity: item.requested,
          })),
        },
        (allocationType = { type: "auto" })
      );

      if (shipments.inventoryToUpdate.length) {
        const bulkUpdateOps = shipments.inventoryToUpdate.map((x) => ({
          updateOne: {
            filter: { sku: x.sku },
            update: {
              $set: { quantity: x.quantity },
              $inc: { on_hold: x.on_hold },
            },
          },
        }));
        await Inventory.bulkWrite(bulkUpdateOps);
      }

      const allShipmentNames = [];

      if (shipments.alignedShipment.length) {
        const alignedUpdate = {
          status: alignedShipmentStatus._id, // Update the shipment to aligned status
          status_data: {
            name: alignedShipmentStatus.status,
          },
          lineItems: shipments.alignedShipment,
          $push: {
            timeline: {
              time: new Date(),
              comment: `Shipment updated to aligned.`,
            },
            status_change_history: {
              status: alignedShipmentStatus._id,
            },
          },
        };

        const updatedAlignedShipment = await Shipment.findByIdAndUpdate(
          shipment._id,
          alignedUpdate,
          { new: true }
        );

        allShipmentNames.push(updatedAlignedShipment.name);
        updatedShipments.push(updatedAlignedShipment.name);

        const alignedDepartmentNotified = await getDepartmentForCreated(
          alignedShipmentStatus._id
        );
        const recipients = alignedDepartmentPeoples.map((departmentPeople) => ({
          name: departmentPeople.name,
          email: departmentPeople.email,
          cc: Array.isArray(alignedDepartmentNotified)
            ? [...alignedDepartmentNotified]
            : [],
        }));

        if (recipients.length > 0) {
          await EmailNotification.create({
            emailCategory: "SHIPMENT",
            emailType: "SHIPMENT_CREATE",
            reciepient: recipients,
            emailPayload: {
              orderName: order.name,
              distributorName: distributor.name,
              date: order.createdAt,
              currentStatus: "",
              nextStatus: alignedShipmentStatus.status,
              shipmentName: updatedAlignedShipment.name,
              shipmentStatus: alignedShipmentStatus.status,
              shipmetRef: updatedAlignedShipment.name,
            },
          });
        }
      }

      if (shipments.nonAlignedShipment.length) {
        const nonAligned = {
          name: `${order.name}_${
            (parseInt(shipment.name.split("_")[1], 10) || 0) + 1
          }`,
          order: order._id,
          status: "673afbb0a7f9a80fac8a4944", //TODO this need to be dynamic
          amount: 5000,
          shipping_address: order.shipping_address,
          lineItems: shipments.nonAlignedShipment,
          timeline: [
            {
              time: new Date(),
              comment: `Non-Aligned shipment created.`,
            },
          ],
          status_change_history: [
            {
              status: "663c4ac9864c679bd21974a8", //TODO this need to be dynamic
            },
          ],
          initial_status: "non-aligned",
        };
        let matchingItem;
        let createdNonAlignedShipment;
        shipments.nonAlignedShipment.forEach((nonAlignedItem) => {
          matchingItem = shipment.lineItems.find(
            (item) =>
              item.sku === nonAlignedItem.sku &&
              item.requested === nonAlignedItem.requested
          );
        });
        if (!matchingItem) {
          const existName = await Shipment.findOne({ name: nonAligned.name });
          if (existName) {
            nonAligned.name =
              nonAligned.name.split("_")[0] +
              (parseInt(nonAligned.name.split("_")[1]) + 1);
          }
          createdNonAlignedShipment = await new Shipment(nonAligned).save();
          const nonAlignedDepartmentNotified = await getDepartmentForCreated(
            nonAlignedShipmentStatus._id
          );

          const recipients = nonAlignedDepartmentPeoples.map(
            (departmentPeople) => ({
              name: departmentPeople.name,
              email: departmentPeople.email,
              cc: Array.isArray(nonAlignedDepartmentNotified)
                ? [...nonAlignedDepartmentNotified]
                : [],
            })
          );

          if (recipients.length > 0) {
            await EmailNotification.create({
              emailCategory: "SHIPMENT",
              emailType: "SHIPMENT_CREATE",
              reciepient: recipients,
              emailPayload: {
                orderName: order.name,
                distributorName: distributor.name,
                date: order.createdAt,
                currentStatus: "",
                nextStatus: nonAlignedShipmentStatus.status,
                shipmentName: createdNonAlignedShipment.name,
                shipmentStatus: nonAlignedShipmentStatus.status,
                shipmetRef: createdNonAlignedShipment.name,
              },
            });

            allShipmentNames.push(createdNonAlignedShipment.name);
            createdShipments.push(createdNonAlignedShipment.name);
          }
        }
      }

      const valuesToPush = allShipmentNames.map((x) => ({
        time: new Date(),
        comment: `Shipment ${x} updated.`,
      }));

      await Order.findByIdAndUpdate(order._id, {
        $set: {
          status: "664372f129f6db844ee8bc0b", // this needs to be dynamic
        },
        $push: { timeline: { $each: valuesToPush } },
      });
    }

    return { createdShipments, updatedShipments };
  } catch (error) {
    console.log("processOrderError", error);
  }
}
