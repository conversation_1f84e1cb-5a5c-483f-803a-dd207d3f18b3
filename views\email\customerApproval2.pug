extends baseEmail

block content
  p Hi #{emailPayload.customerName},

  p Your account has been created! You can now access the portal to explore products and place orders.

  p 👉 
    a(href=`${emailPayload.portalUrl}`) Portal Access Link
  p 👉 Username: #{emailPayload.customerEmail}

  p For help, contact your sales representative:
  ul
    li #{emailPayload.salespersonName} – 
      a(href=`mailto:${emailPayload.salesPersonEmail}`) #{emailPayload.salesPersonEmail}

  p We’re thrilled to have you onboard!

  p Warm wishes,  
  p - The #{emailPayload.storeName} Team
