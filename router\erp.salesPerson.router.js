import express from "express";
import {
  getSalesPersonController,
  createSalesPersonController,
  deleteSalesPersonController,
} from "../controller/erp.customer.controller.js";

const erpSalesPersonRouter = express.Router();

erpSalesPersonRouter.get("/", getSalesPersonController);
erpSalesPersonRouter.post("/", createSalesPersonController);
erpSalesPersonRouter.delete("/:salesPersonKey", deleteSalesPersonController);

export default erpSalesPersonRouter;
