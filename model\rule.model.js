import { UUID } from "mongodb";
import mongoose from "mongoose";

const ruleSchema = new mongoose.Schema({
  ruleGroupId: {
    type: String,
    index: true
  },
  title: {
    type: String,
  },
  description: {
    type: String,
  },
  rules: [{
    id: {
      type: String,
      default: new UUID()
    },
    ruleType: {
      type: String,
      enum: ['SEND_EMAIL']
    },
    emails: {
      type: Array,
    },
    description: {
      type: String
    }
  }],
});

const Rule = mongoose.model("Rule", ruleSchema);
export default Rule;
