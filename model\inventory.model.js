import mongoose from "mongoose";

const inventorySchema = new mongoose.Schema(
  {
    shopifyVariantId: {
      type: String,
    },
    productCategory: {
      type: String,
    },
    inventoryType: {
      type: String,
      enum: ["CUSTOM", "STANDARD"],
      default: "STANDARD",
    },
    shopifyProductId: {
      type: String,
    },
    productTitle: {
      type: String,
    },
    variantTitle: {
      type: String,
    },
    price: {
      type: String,
    },
    sku: {
      type: String,
      index: true,
      unique: true,
    },
    brand: {
      type: String,
    },
    cluster: {
      type: String,
    },
    quantity: {
      type: Number,
      required: true,
    },
    on_hold: {
      type: Number,
    },
    skuType: {
      type: String,
    },
    image: {
      type: String,
    },
    pmrStatus: {
      type: String,
    },
    brand:{
      type:String,
    },
    cluster:{
      type:String
    },
    priceList:[
      {
        price: String,
        currency: String,
        companyLocationCatalogId: String,
      },
    ],
  },
  { timestamps: true }
);

const Inventory = mongoose.model("Inventory", inventorySchema);
export default Inventory;
