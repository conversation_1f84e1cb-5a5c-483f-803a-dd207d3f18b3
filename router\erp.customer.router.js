import express from "express";
import {
  createCustomersController,
  customerManualSyncController,
  deleteSalesPersonController,
} from "../controller/erp.customer.controller.js";

const erpCustomerRouter = express.Router();

// erpCustomerRouter.post("/", createCustomersController);
erpCustomerRouter.post("/manualSync", customerManualSyncController);
erpCustomerRouter.delete("/:salesPersonKey", deleteSalesPersonController);

export default erpCustomerRouter;
