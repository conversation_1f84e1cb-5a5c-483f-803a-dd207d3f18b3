import mongoose from "mongoose";

const departmentSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    salespersonkey: {
      type: String,
    },
    salesPersons: [
      {
        type: mongoose.Schema.ObjectId,
        ref: "Department",
      },
    ],
    email: {
      type: String,
      required: [true, "User with this email address already exists"],
      unique: true,
    },

    departmentType: [
      {
        type: mongoose.Schema.ObjectId,
        ref: "DepartmentType",
        required: true,
      },
    ],
    password: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      default: "deptt_people",
    },
    resetPasswordToken: {
      type: String,
    },
    resetPasswordExpires: {
      type: Date,
    },
  },
  { timestamps: true }
);

departmentSchema.pre(/^find/, function (next) {
  // this.populate({
  //   path: "distributor",
  //   // select: "",
  // })
  //   .populate({
  //     path: "designation",
  //     // select: "",
  //   })
  this.populate({
    path: "departmentType",
    // select: "",
  });
  next();
});

departmentSchema.pre("validate", async function (next) {
  try {
    if (this.departmentType && this.departmentType.length > 0) {
      // Fetch the first departmentType document
      const firstDepartmentTypeId = this.departmentType[0];
      const departmentTypeDoc = await mongoose
        .model("DepartmentType")
        .findById(firstDepartmentTypeId);

      // Check if pseudoId matches 'sales_person'
      if (departmentTypeDoc && departmentTypeDoc.pseudoId === "SALES_PERSON") {
        if (!this.salespersonkey) {
          // Throw validation error if salespersonkey is missing
          this.invalidate(
            "salespersonkey",
            "Salesperson key is required for sales person department type"
          );
        }
      }
    }
    next();
  } catch (error) {
    next(error);
  }
});

const Department = mongoose.model("Department", departmentSchema);
export default Department;
